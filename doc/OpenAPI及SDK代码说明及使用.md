## OpenAPI及SDK代码说明及使用

## 说明

### OpenAPI 设计

#### 响应模型

响应模型同其他 api 一致，均为 SingleResponse

#### 签名设计

##### 字段说明

签名字段：

```java
		/**
     * 请求域名
     */
    @StringValueValid(paramName = "signatureHost")
    private String signatureHost;
    /**
     * Api服务(对应s_developer表app_service字段，每个服务都有独立的app_key和app_secret_key)
     */
    @EnumStringValueValid(paramName = "apiService", enumClass = ApiServic8eEnum.class)
    private String apiService;
    /**
     * secretId
     */
    @StringValueValid(paramName = "secretId")
    private String secretId;
    /**
     * 请求时间戳(13位时间戳)，1分钟内的请求属于有效请求
     */
    @StringValueValid(paramName = "timestamp", regexp = "^\\d{13}$")
    private String timestamp;
    /**
     * 请求唯一标识，防重放攻击(32位无连接符uuid，redis实现)
     */
    @StringValueValid(paramName = "requestId", regexp = "^[a-z0-9]{32}$")
    private String requestId;
    /**
     * 签名算法(默认HmacSHA256)
     */
    @EnumStringValueValid(paramName = "signatureMethod", enumClass = SignatureMethodEnum.class)
    private String signatureMethod;
    /**
     * 签名值
     */
    @StringValueValid(paramName = "signature")
    private String signature;
```

数据加密字段：

```java
		/**
     * 是否加密
     */
    private Boolean encryption = true;
```

详见 `SignatureDO` 类。自定义注解校验说明：

枚举值注解 @EnumStringValueValid，校验逻辑详见 `EnumStringValueValidator` 类

字符串值注解(包括正则校验) @StringValueValid，校验逻辑详见 `StringValueValidator` 类

##### 签名算法

```
signatureHost + requestPath + "?" + apiService=xxx + &secretId=xxx + &timestamp=xxx + &requestId=xxx + &signatureMethod=xxx
例如：
localhost:8008/api/user?apiService=USER&secretId=dsfsrwef&timestamp=1686898416209&requestId=10cd83f9adc74ae5801658d6ccb31e61&signatureMethod=HmacSHA256
加密：
根据signatureMethod(默认HmacSHA256)指定的加密算法及secret_key加密上面的字符串，加密算法可选值：HmacSHA256、HmacSHA512
```

工具类见 `SignatureUtil` 

##### openapi自定义拦截器

`OpenapiInterceptor`

```java
// 校验
validator.validate(signatureDO);
// 校验通过
log.info("校验通过");

// 防重放攻击拦截
Object requestIdCache = redisUtil.get(REQUEST_ID_CACHE_PREFIX + requestId);
if (ObjectUtil.isNotNull(requestIdCache)) {
    throw new SdkException(SdkExceptionMessageEnum.INVALID_REQUEST_ID);
}

// 请求1min内有效性验证
float seconds = (float) (System.currentTimeMillis() - Long.parseLong(timestamp)) / 1000;
if (seconds > ONE_MIN_SECONDS) {
    throw new SdkException(SdkExceptionMessageEnum.INVALID_TIMESTAMP);
}

// 签名
String secretId = signatureDO.getSecretId();
String apiService = signatureDO.getApiService();
String signature = signatureDO.getSignature();
QueryWrapper<DeveloperDO> wrapper = new QueryWrapper<>();
wrapper.lambda().eq(DeveloperDO::getAppKey, secretId).eq(DeveloperDO::getAppService, apiService);
DeveloperDO developerDO = developerMapper.selectOne(wrapper);
if (ObjectUtil.isNull(developerDO)) {
    throw new SdkException(SdkExceptionMessageEnum.INVALID_APP_KEY);
}
if (developerDO.getType() != 0 || developerDO.getStatus() != 1) {
    throw new SdkException(SdkExceptionMessageEnum.INVALID_APP_KEY_STATUS);
}
String signatureHttpProduce = SignatureUtil.signatureHttpProduce(signatureDO, developerDO.getAppSecretKey());
if (!signature.equals(signatureHttpProduce)) {
    throw new SdkException(SdkExceptionMessageEnum.INVALID_SIGNATURE);
}
log.info("签名通过");
```

拦截openapi请求并进行校验及签名认证：

1. 参数校验，即上面的自定义注解校验
2. 防重放攻击拦截
3. 请求1min内有效性验证
4. app_key及secret_key状态验证
5. 签名生成及验证

#### 加密设计

默认数据是加密的，当encryption=false时不对数据加密。数据加密以app_key+app_secret_key通过SHA-256哈希函数转成固定长度字节数组，再截取32字节作为AES-256的密钥对数据进行加解密。响应util详见`SdkSingleResponseUtil`

#### 异常设计

1. SDK的异常定义都在SDK的包内，这里openApi抛出的SDK异常也需要在全局异常捕获中进行处理
2. 自定义参数校验枚举抛出的异常

```java
@ExceptionHandler(SdkException.class)
@ResponseStatus(value = HttpStatus.INTERNAL_SERVER_ERROR)
public SingleResponse handleSdkError(SdkException sdkException) {
    String errorCode = sdkException.errorCode();
    String errorMessage = sdkException.errorMessage();
    log.error(errorMessage);
    return SingleResponse.buildFailure(errorCode, errorMessage);
}

@ExceptionHandler(ValidationException.class)
@ResponseStatus(value = HttpStatus.INTERNAL_SERVER_ERROR)
public SingleResponse handleValidationError(ValidationException validationException) {
    Throwable cause = validationException.getCause();
    if (cause instanceof SdkException) {
        SdkException sdkException = (SdkException) cause;
        String errorCode = sdkException.errorCode();
        String errorMessage = sdkException.errorMessage();
        log.error(errorMessage);
        return SingleResponse.buildFailure(errorCode, errorMessage);
    }
    throw validationException;
}
```

###  SDK 设计

#### 配置设计

##### 基础配置

见 `SdkBaseProperties` 包括host、signatureMethod(可选，默认HmacSHA256)、encryption(可选，默认true)

##### app_key及app_secret_key配置

父类见 `SdkProperties` ，不同业务有着不同的app_key及app_secret_key，直接继承 `SdkProperties` 定义自己的配置前缀即可 。

#### 业务设计

`ApiServiceEnum` 枚举是关键的业务定义，这里定义了：

```java
USER("getUser", RequestMethodEnum.GET, "/api/openapi/getUser", TestUser.class)
```

1. USER：枚举本身name：对应s_developer表app_service字段，用来定义业务code
2. getUser：sdk提供服务的方法名，在代理的时候会与方法名进行匹配
3. RequestMethodEnum.GET：sdk提供服务的请求类型，用来决定调用openapi的请求类型
4. /api/openapi/getUser：sdk提供服务的请求路径，与host配置构成openapi的请求地址
5. TestUser.class：sdk提供服务的请求响应的数据类型，在openapi里如果选择数据加密，那返回的数据只能为字符串，但在sdk中openapi http请求结束后可根据此类型对数据进行解密和反序列化。如果不加密则没有这些解密和反序列化操作

#### 注解设计

自定义sdk使用注解提供使用，sdk会自动扫描使用了此注解的接口，并注入到spring容器中

```java
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface MaterialClientService {
}
```

#### 自动注入设计

##### 配置注入

在`spring.factories`配置自动配置类`SdkServiceRegisterAutoConfiguration`

```java
@Configuration
@ComponentScan(basePackageClasses = { GlobalExceptionHandler.class })
@EnableConfigurationProperties(value = { SdkBaseProperties.class,
        UserServiceProperties.class, AlertUserServiceProperties.class })
public class SdkServiceRegisterAutoConfiguration implements InitializingBean {}
```

1. sdk properties配置注入，由于不同服务有不同的app_key及app_secret_key，sdk properties会以不同client的bean注入容器中供使用
2. 全局异常注入，通过 `@ComponentScan` 将定义的`GlobalExceptionHandler`加入扫描，`GlobalExceptionHandler`需要定义一个独立唯一的bean名称(`materialSdkGlobalExceptionHandler`)，防止和sdk使用者的全局异常定义冲突

```java
@ResponseBody
@ControllerAdvice
@Component("materialSdkGlobalExceptionHandler")
public class GlobalExceptionHandler {}
```

##### 自定义注解注入

扫描注解并注入：`SdkServiceRegisterAutoConfiguration#SdkServiceRegister#registerBeanDefinitions`

bean代理实现：`SdkFactoryBean#getObject`

#### http请求设计

`SdkHttpUtil`

```java
public static <T> T doGet(String url, Map<String, Object> params, Class<T> clazz, String aesEncryptionKeyString) {
    String resp = HttpUtil.get(url, params, HTTP_TIMEOUT);
    return getResponseData(resp, clazz, aesEncryptionKeyString);
}

public static <T> T doPost(String url, Map<String, Object> params, Class<T> clazz, String aesEncryptionKeyString) {
    String resp = HttpUtil.post(url, params, HTTP_TIMEOUT);
    return getResponseData(resp, clazz, aesEncryptionKeyString);
}

private static <T> T getResponseData(String resp, Class<T> clazz, String aesEncryptionKeyString) {
    SingleResponse singleResponse = JSONObject.parseObject(resp, SingleResponse.class);
    boolean success = singleResponse.isSuccess();
    if (success) {
        if (StringUtils.isBlank(aesEncryptionKeyString)) {
            log.info("数据无需解密");
            JSONObject jsonObject = (JSONObject) singleResponse.getData();
            T data = jsonObject.toJavaObject(clazz);
            return data;
        } else {
            String data = (String) singleResponse.getData();
            T proto = ResponseProtoDecrypt.decode(data, clazz, aesEncryptionKeyString);
            return proto;
        }
    }
    throw new SdkException(singleResponse.getErrCode(), singleResponse.getErrMessage());
}
```

## 使用

### sdk服务生产者

1. `ApiServiceEnum`枚举中定义服务相关的信息
2. 继承`SdkProperties`定义服务的app_key、app_secret_key配置
3. 继承`SdkClient`定义服务app_key、app_secret_key配置的bean
4. `SdkServiceRegisterAutoConfiguration`中添加`@EnableConfigurationProperties`配置类、`@Bean client` bean初始化
5. `SdkFactoryBean<T>`中初始化服务缓存`SDK_CLIENT_MAP`，`SdkFactoryBean#getObject()`和`SdkHttpUtil`中目前只实现了`GET`和`POST`请求类型的http请求，如有其他类型的请求自行添加
6. `cn.pinming.microservice.material.client.management.controller.openapi`中定义业务的`openapi`服务及实现

### sdk服务使用者

1. 引入依赖（或jar包）

   ```xml
   dependency>
       <groupId>cn.pinming.microservice</groupId>
       <artifactId>material-service-sdk</artifactId>
       <version>0.0.1-SNAPSHOT</version>
   </dependency>
   ```

2. 配置

```yml
sdk:
  host: http://localhost:8080
#  signatureMethod: HmacSHA512
#  encryption: false
  user:
    secretId: abcc
    secretKey: qwerdf
  alert:
    secretId: abcd
    secretKey: qwerdffhdfgdf
```

3. 定义服务

```java
@MaterialClientService
public interface TestUserService {
    TestUser getUser(TestUserQuery query);
}
```

4. 调用服务

```java
@GetMapping("/getUser")
public SingleResponse<TestUser> getUser(TestUserQuery query) {
    TestUser user = testUserService.getUser(query);
    return SingleResponse.of(user);
}
```