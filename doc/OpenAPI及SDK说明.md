# 开发指南

## 查看业务设计简要说明

[基石称重产品业务设计简要说明](https://www.processon.com/view/link/6579040c545f9e4e33855de9)

## 访问域名

endpoint表示客户端对外服务的访问域名。

| endpoint                           | 环境     |
| ---------------------------------- | -------- |
| https://zz-test05-open.pinming.org/material-client-management | 测试环境 |
| http://weighmaster.pinming.cn      | 生产环境 |

## 发起请求

### 概述

物料客户端提供一个REST服务。你可以使用REST API或封装了REST API的SDK发起请求。收到请求后，会通过凭证验证请求的发送者身份。身份验证成功后，就可以进行相应的服务操作了。

#### 发起请求

你可以使用以下方式向物料客户端发起请求：

- SDK：使用SDK发起请求，可以免去手动签名的过程。目前只有Java语言版本的SDK，如果非Java语言发起请求，需要使用REST API的方式进行发起请求。
- REST API：直接发起REST API请求，需要手动编写签名计算代码并将签名添加到请求头中。

#### 身份验证

身份验证的实现如下：

1. 将需要发送的请求按指定的格式生成签名字符串。
2. 使用自己的appSecretKey对签名字符串进行对应加密算法的加密得到签名。
3. 收到请求后，客户端通过appKey查找到对应的appSecretKey，并以相同的方式进行加密获取签名。
   - 如果计算出来的签名和请求提供的一致，则认证通过。
   - 如果计算出来的签名和请求提供的不一致，则认证失败。

#### 密钥

密钥指的是访问身份验证中用到的appKey和appSecretKey。在后台的开发者菜单中可查看到，appKey是唯一标识你身份的，appSecretKey是用来身份认证的，其中appSecretKey需要保密保管。

## 使用SDK发起请求

使用SDK发起请求，可以免去手动签名的过程。目前只有Java语言版本的SDK。

### 配置依赖

1. 打开 maven 文件夹下的 setting.xml，添加如下配置：

```xml
<servers>
  ......
  <server>
    <id>rdc-releases</id>
    <username>6540c8b9e7be53b98da3eed4</username>
    <password>NJ[8Kby2giYs</password>
  </server>
<servers>
```

2. maven 项目下`pom.xml`中添加仓库地址,添加如下配置：

```xml
<repositories>
    ......
    <repository>
        <id>rdc-releases</id>
        <url>https://packages.aliyun.com/maven/repository/2429771-release-ykc3lH/</url>
        <releases>
            <enabled>true</enabled>
        </releases>
        <snapshots>
            <enabled>false</enabled>
        </snapshots>
    </repository>
</repositories>
```

3. maven 项目下`pom.xml`引入 sdk 模块：

```xml
<dependencies>
    <dependency>
        <groupId>cn.pinming.material</groupId>
        <artifactId>pinming-sdk-material</artifactId>
        <version>1.1.8.240709_release</version>
    </dependency>
</dependencies>
```

### 配置凭证

在使用SDK发起请求之前，你需要在项目中配置好访问凭证。下面以一个SpringBoot项目为例展示需要配置的凭证。

```yml
# application.yml
sdk:
  endpoint: 你需要访问的环境域名
  appKey: 你的appKey
  appSecretKey: 你的appSecretKey
```

```java
// 代码引入
@Value("${sdk.endpoint}")
private String endpoint;

@Value("${sdk.appKey}")
private String appKey;

@Value("${sdk.appSecretKey}")
private String appSecretKey;
```

### 使用

```java
// 创建MaterialClient实例
Material materialClient = new MaterialClientBuilder().build(endpoint, appKey, appSecretKey);
```

materialClient对象可操作SDK提供的所有服务，SDK提供的服务详见 `服务列表`

## 使用REST API发起请求

你可以直接发起REST API请求。需要手动编写代码计算签名并将签名添加到REST API请求中。

### 公共请求头定义

REST API请求使用了一些公共的请求头，包括其中需要认证的签名字段，详细说明如下。

| 名称                       | 是否必须 | 描述                                                     | 示例值                                       |
| -------------------------- | :------- | -------------------------------------------------------- | -------------------------------------------- |
| X-material-appKey          | Y        | appKey                                                   | 7DE4BD442893CF8F                             |
| X-material-timestamp       | Y        | 请求时间戳（13位毫秒数）                                 | 1687917754629                                |
| X-material-requestId       | Y        | 请求唯一标识（32位无连接符uuid, 小写字母、数字组合）     | 08da515877024c1784258e4ac5dfdeb3             |
| X-material-signatureMethod | N        | 签名算法（可选值HmacSHA256、HmacSHA512。默认HmacSHA256） | HmacSHA512                                   |
| X-material-signature       | Y        | 签名值                                                   | xzUKmF+D7yJGPqjgXqhUSD5q5dzb1VBOLWTjm9ml9Bw= |

### 签名

签名算法有两种HmacSHA256和HmacSHA512，默认为HmacSHA256。

#### 签名字符串

签名字符串为 `appKey=xxx + &timestamp=xxx + &requestId=xxx + &signatureMethod=xxx`

示例：`appKey=7DE4BD442893CF8F&timestamp=1686898416209&requestId=10cd83f9adc74ae5801658d6ccb31e61&signatureMethod=HmacSHA256`

#### 签名逻辑

根据签名算法对签名字符串进行加密得到签名值。Java语言的计算逻辑如下：

```java
private static String hmacSHAEncrypt(String signatureStr, String appSecretKey, String signatureMethod) {
        String hash;
        try {
            Mac sha_HMAC = Mac.getInstance(signatureMethod);
            SecretKeySpec secret_key = new SecretKeySpec(appSecretKey.getBytes(), signatureMethod);
            sha_HMAC.init(secret_key);
            byte[] bytes = sha_HMAC.doFinal(signatureStr.getBytes());
            hash = Base64.getEncoder().encodeToString(bytes);
        } catch (Exception e) {
            LogUtil.getLog().error(signatureMethod + " encrypt error." + e.getMessage());
            throw new MaterialException(MaterialExceptionMessage.SIGNATURE_ENCRYPT_ERROR);
        }
        return hash;
    }
```

### 快速入门

请求头示例：

1. 默认加密算法

```xml
X-material-appKey: 7DE4BD442893CF8F
X-material-timestamp: 1687917754629
X-material-requestId: 08da515877024c1784258e4ac5dfdeb3
X-material-signature: xzUKmF+D7yJGPqjgXqhUSD5q5dzb1VBOLWTjm9ml9Bw=
```

2. 指定加密算法

```xml
X-material-appKey: 7DE4BD442893CF8F
X-material-timestamp: 1687917754629
X-material-requestId: 08da515877024c1784258e4ac5dfdeb3
X-material-signatureMethod: HmacSHA512
X-material-signature: 2AiZ2VltNi/1MuMqIIYdHgK4qOfP75cf3Ixcseq7nJ69//Oee3Mo0KV8n7NTGgl0PyCQgp1KnuRJKskLYuFBig==
```

### 公共响应体定义

```java
boolean isSuccess;
String errCode;
String errMessage;
T data;
```

1. 请求正常

   isSuccess为true，errCode和errMessage为空，数据为data。
2. 请求异常

   isSuccess为false，errCode和errMessage为异常数据，数据为空。

# 服务列表

## 数据重组

数据重组分为两步，第一步校验称重数据，第二步确认数据重组

### 校验数据

#### 请求类型

```xml
POST
```

#### 请求路径

```xml
/api/openapi/weigh/data/assemble
```

#### 参数模型

| 参数名          | 是否必须 | 类型   | 描述                 |
| --------------- | -------- | ------ | -------------------- |
| first           | Y        | string | 第一条称重数据称重id |
| second          | Y        | string | 第二条称重数据称重id |
| attributionCode | Y        | string | 归属方code           |

#### 响应模型

| 字段名    | 类型          | 描述                        |
| --------- | ------------- | --------------------------- |
| typeStr   | String        | 1：载车称重，2：净货称重    |
| deviceSn  | String        | 终端sn                      |
| gmtCreate | String        | 上传时间                    |
| recordId  | String        | 终端记录id                  |
| truckNo   | String        | 车牌号                      |
| material  | String        | 材料名称                    |
| weight    | BigDecimal    | 重量                        |
| unit      | String        | 称重单位                    |
| weighTime | String        | 称重时间                    |
| combine   | Boolean       | 是否重组过 true 是 false 否 |
| pic       | List | 称重预览照片                 |
| urls       | List | 称重真实下载地址                   |
| uuids       | List | 图片uuId                  |

#### SDK调用

```java
// 创建MaterialClient实例
Material materialClient = new MaterialClientBuilder().build(endpoint, appKey, appSecretKey);
// 构造参数
WeighDataAssembleForm assembleForm = new WeighDataAssembleForm();
// 参数赋值
// ...
// 调用接口
WeighDataAssemble weighDataAssemble = materialClient.weighDataAssemble(assembleForm);
```

### 重组数据

#### 请求类型

```xml
POST
```

#### 请求路径

```xml
/api/openapi/weigh/data/confirm
```

#### 参数模型

| 参数名          | 是否必须 | 类型   | 描述                 |
| --------------- | -------- | ------ | -------------------- |
| first           | Y        | string | 第一条称重数据称重id |
| second          | Y        | string | 第二条称重数据称重id |
| attributionCode | Y        | string | 归属方code           |

#### 响应模型

```
true: 重组成功  false: 重组失败
```

#### SDK调用

```java
// 创建MaterialClient实例
Material materialClient = new MaterialClientBuilder().build(endpoint, appKey, appSecretKey);
// 构造参数
WeighDataAssembleForm assembleForm = new WeighDataAssembleForm();
// 参数赋值
// ...
// 调用接口
Boolean weighDataConfirm = materialClient.weighDataConfirm(assembleForm);
```

### 查询数据

分页接口，默认一次查询1000条数据，一次最大查询1000条数据。

#### 请求类型

```xml
POST
```

#### 请求路径

```xml
/api/openapi/weigh/data
```

#### 参数模型

| 参数名          | 是否必须 | 类型          | 描述                             |
| --------------- | -------- | ------------- | -------------------------------- |
| ids             | N        | List\ | 称重id集合(最大1000条)           |
| startTime       | Y        | LocalDateTime | 称重开始时间                     |
| endTime         | N        | LocalDateTime | 称重结束时间（为空表示当前时间） |
| attributionCode | N        | String        | 数据归属方code                   |
| deviceSn        | N        | String        | 设备机器码                       |
| truckNos        | N        | List\ | 车牌号列表(最大500条)            |

#### 响应模型

| 字段名          | 类型          | 描述                                      |
| --------------- | ------------- | ----------------------------------------- |
| id              | String        | 主键ID                                    |
| gmtCreate       | String        | 创建时间                                  |
| gmtModify       | String        | 修改时间                                  |
| createId        | String        | 创建人ID                                  |
| modifyId        | String        | 修改人ID                                  |
| recordId        | String        | 终端记录id                                |
| uid             | String        | 用户id                                    |
| attributionId   | String        | 归属方id                                  |
| attributionCode | String        | 归属方code                                |
| deviceSn        | String        | 设备机器码                                |
| truckNo         | String        | 车牌号                                    |
| material        | String        | 材料名称                                  |
| weight          | BigDecimal    | 重量                                      |
| unit            | String        | 称重单位                                  |
| type            | Byte          | 1：载车称重，2：净货称重                  |
| riskGrade       | String        | 风险等级（低：LOW，中：MIDDLE，高：HIGH） |
| weighTime       | String        | 称重时间                                  |
| lprTruckNo      | String        | 使用车牌识别事后checkout的车牌号          |
| pic       | List | 称重预览照片                 |
| urls       | List | 称重真实下载地址                   |
| uuids       | List | 图片uuId                 |

#### SDK调用

```java
// 创建MaterialClient实例
Material materialClient = new MaterialClientBuilder().build(endpoint, appKey, appSecretKey);
// 构造参数
QueryPage<WeighDataQuery> queryPage = new QueryPage<>();
// 参数赋值
// ...
// 调用接口
PageList<WeighDataDTO> pageList = materialClient.queryWeighData(queryPage)
```

### 接收简易称重数据

由第三方自定义接口，维护好后请告知“基石科技称重管理平台”相关人员进行内部的维护。
目的是接收来自“基石科技称重管理平台”发送的简易称重数据，接收到后根据简易称重数据中的数据id，通过SDK请求完整称重数据。
#### 请求类型

```xml
POST
Content-Type: application/json
```

#### 请求路径

```xml
请在基石科技称重管理平台配置数据推送的接口地址，如请求需要加签需配置appKey和appSecretKey
```

#### 参数模型

| 参数名          | 是否必须 | 类型          | 描述                             |
| --------------- | -------- | ------------- | -------------------------------- |
| uid             | Y        | String | 用户id     |
| recordId             | Y        | String | 称重记录id     |
| picId             | Y        | String | 称重照片ID（仅称重照片标签时存在）     |
| tag             | Y        | String | 数据标签(称重数据：data、称重照片:pic)     |
#### 请求参数示例
```json
称重数据：{"uid":"39578adc221c4643af80a58eefd5abc4","recordId":"7eefa5ad73e3474cb6e2573afb1205c3","tag":"data"}
称重照片：{"uid":"39578adc221c4643af80a58eefd5abc4","recordId":"7eefa5ad73e3474cb6e2573afb1205c3","picId":"1484","tag":"pic"}
```


### 请求完整称重数据

#### 请求类型

```xml
GET
```

#### 请求路径

```xml
/api/openapi/weigh/data/get
```

#### 参数模型

| 参数名          | 是否必须 | 类型          | 描述                             |
| --------------- | -------- | ------------- | -------------------------------- |
| recordId             | Y        | String | 称重记录id     |

#### 响应模型

| 字段名          | 类型          | 描述                                      |
| --------------- | ------------- | ----------------------------------------- |
| recordId              | String        | 终端记录id                                    |
| uid       | String        | 用户id                                    |
|name       | String        | 归属方名称                            |
| code        | String        | 归属方code                                 |
|deviceSn        | String        | 设备机器码                                  |
|truckNo       | String        | 车牌号                                |
| material             | String        | 材料名称                                 |
| weight   | BigDecimal        | 重量                                  |
| unit | String        | 称重单位（吨、千克、磅）                               |
| type        | Byte        | 1 载车称重 2 净货称重                                |
| weighTime         | LocalDateTime        | 称重时间|

#### SDK调用

```java
// 创建MaterialClient实例
Material materialClient = new MaterialClientBuilder().build(endpoint, appKey, appSecretKey);
// 调用接口
WeighPushDataDTO weighPushDataDTO = materialClient.weighDataPushGet(recordId);
// 第三方业务处理
...
```

### 请求完整称重照片

#### 请求类型

```xml
GET
```

#### 请求路径

```xml
/api/openapi/weigh/pic/get
```

#### 参数模型

| 参数名          | 是否必须 | 类型          | 描述                             |
| --------------- | -------- | ------------- | -------------------------------- |
| id             | Y        | Long | 称重数据照片id     |

#### 响应模型

| 字段名          | 类型          | 描述                                      |
| --------------- | ------------- | ----------------------------------------- |
| recordId              | String        | 终端记录id                                    |
| uid       | String        | 用户id                                    |
|name       | String        | 归属方名称                            |
| code        | String        | 归属方code                                 |
|filePath        | String        | 本地文件路径                                  |
|fileId       | String        | oss 文件id                                |
| type             | Integer        | 1 过磅照片 2 磅房、操作棚照片 3 操作人照片                                |
| size   | BigDecimal        | 照片大小(kb)                                  |
| localCTime | LocalDateTime        | 照片本地创建时间                               |
| localId        | String        | 照片本地id                                |
| previewUrl         | String        | 图片预览地址|
| downloadUrl         | String        | 图片真实地址|

#### SDK调用

```java
// 创建MaterialClient实例
Material materialClient = new MaterialClientBuilder().build(endpoint, appKey, appSecretKey);
// 调用接口
WeighPushPicDTO weighPushPicDTO = materialClient.weighPicPushGet(id);
// 第三方业务处理
...
```

### 确认推送数据被接收

#### 请求类型

```xml
GET
```

#### 请求路径

```xml
/api/openapi/weigh/data/pushConfirm
```

#### 参数模型

| 参数名          | 是否必须 | 类型          | 描述                             |
| --------------- | -------- | ------------- | -------------------------------- |
| recordId             | Y        | String | 称重记录id      |


#### 响应模型

```
true: 确认成功  false: 确认失败
```

#### SDK调用

```java
// 创建MaterialClient实例
Material materialClient = new MaterialClientBuilder().build(endpoint, appKey, appSecretKey);
// ...
// 调用接口
Boolean flag = materialClient.weighDataPushConfirm(recordId);
```


### 确认推送照片被接收

#### 请求类型

```xml
GET
```

#### 请求路径

```xml
/api/openapi/weigh/pic/pushConfirm
```

#### 参数模型

| 参数名          | 是否必须 | 类型          | 描述                             |
| --------------- | -------- | ------------- | -------------------------------- |
| id             | Y        | Long | 称重数据照片id    |


#### 响应模型

```
true: 确认成功  false: 确认失败
```

#### SDK调用

```java
// 创建MaterialClient实例
Material materialClient = new MaterialClientBuilder().build(endpoint, appKey, appSecretKey);
// ...
// 调用接口
Boolean flag = materialClient.weighPicPushConfirm(id);
```

### 根据图片uuId获取图片预览/下载地址

#### 请求类型

```xml
POST
```

#### 请求路径

```xml
/api/openapi/getPicUrl
```

#### 参数模型

| 参数名          | 是否必须 | 类型          | 描述                             |
| --------------- | -------- | ------------- | -------------------------------- |
| pic           | Y        | String | 称重数据照片uuid,英文逗号分割    |
| isPre           | Y        | boolean | true,获取预览图片地址;false,获取下载图片地址    |

#### 响应模型

```
true: 确认成功  false: 确认失败
```

#### SDK调用

```java
// 创建MaterialClient实例
Material materialClient = new MaterialClientBuilder().build(endpoint, appKey, appSecretKey);
// 构造参数
PicForm form = new PicForm();
// 参数赋值
// ...
// 调用接口
List<String> list = materialClient.getPicUrl(form);   
