# 开发指南

## 查看业务设计简要说明

[基石称重产品业务设计简要说明](https://www.processon.com/view/link/6579040c545f9e4e33855de9)

## 访问域名

endpoint 表示客户端对外服务的访问域名。

| endpoint                                                      | 环境     |
| ------------------------------------------------------------- | -------- |
| https://zz-test05-open.pinming.org/material-client-management | 测试环境 |
| http://weighmaster.pinming.cn                                 | 生产环境 |

## 发起请求

### 概述

物料客户端提供一个 REST 服务。你可以使用 REST API 或封装了 REST API 的 SDK 发起请求。收到请求后，会通过凭证验证请求的发送者身份。身份验证成功后，就可以进行相应的服务操作了。

#### 发起请求

你可以使用以下方式向物料客户端发起请求：

-   SDK：使用 SDK 发起请求，可以免去手动签名的过程。目前只有 Java 语言版本的 SDK，如果非 Java 语言发起请求，需要使用 REST API 的方式进行发起请求。
-   REST API：直接发起 REST API 请求，需要手动编写签名计算代码并将签名添加到请求头中。

#### 身份验证

身份验证的实现如下：

1. 将需要发送的请求按指定的格式生成签名字符串。
2. 使用自己的 appSecretKey 对签名字符串进行对应加密算法的加密得到签名。
3. 收到请求后，客户端通过 appKey 查找到对应的 appSecretKey，并以相同的方式进行加密获取签名。
   - 如果计算出来的签名和请求提供的一致，则认证通过。
   - 如果计算出来的签名和请求提供的不一致，则认证失败。

#### 密钥

密钥指的是访问身份验证中用到的 appKey 和 appSecretKey。在后台的开发者菜单中可查看到，appKey 是唯一标识你身份的，appSecretKey 是用来身份认证的，其中 appSecretKey 需要保密保管。

## 使用 SDK 发起请求

使用 SDK 发起请求，可以免去手动签名的过程。目前只有 Java 语言版本的 SDK。

### 配置依赖

1. 打开 maven 文件夹下的 setting.xml，添加如下配置：

```xml
<servers>
  ......
  <server>
    <id>rdc-releases</id>
    <username>6540c8b9e7be53b98da3eed4</username>
    <password>NJ[8Kby2giYs</password>
  </server>
<servers>
```

2. maven 项目下`pom.xml`中添加仓库地址,添加如下配置：

```xml
<repositories>
    ......
    <repository>
        <id>rdc-releases</id>
        <url>https://packages.aliyun.com/maven/repository/2429771-release-ykc3lH/</url>
        <releases>
            <enabled>true</enabled>
        </releases>
        <snapshots>
            <enabled>false</enabled>
        </snapshots>
    </repository>
</repositories>
```

3. maven 项目下`pom.xml`引入 sdk 模块：

```xml
<dependencies>
    <groupId>cn.pinming.material</groupId>
    <artifactId>material-client-sdk-spring-boot-starter</artifactId>
    <version>1.0.0-RELEASE</version>
</dependencies>
```

### 配置凭证

在使用 SDK 发起请求之前，你需要在项目中配置好访问凭证。下面以一个 SpringBoot 项目为例展示需要配置的凭证。

```yml
# application.yml
sdk:
    endpoint: 你需要访问的环境域名
    appKey: 你的appKey
    appSecretKey: 你的appSecretKey
```

```java
// 代码引入
@Value("${sdk.endpoint}")
private String endpoint;

@Value("${sdk.appKey}")
private String appKey;

@Value("${sdk.appSecretKey}")
private String appSecretKey;
```

### 使用

```java
// 在启动类上添加包扫描路径
@SpringBootApplication(scanBasePackages = {"cn.pinming.material.v2"})

// 创建MaterialClient实例
Material materialClient = new MaterialClientBuilder().build(endpoint, appKey, appSecretKey);
```

materialClient 对象可操作 SDK 提供的所有服务，SDK 提供的服务详见 `服务列表`

## 使用 REST API 发起请求

你可以直接发起 REST API 请求。需要手动编写代码计算签名并将签名添加到 REST API 请求中。

### 公共请求头定义

REST API 请求使用了一些公共的请求头，包括其中需要认证的签名字段，详细说明如下。

| 名称                       | 是否必须 | 描述                                                       | 示例值                                       |
| -------------------------- | :------- | ---------------------------------------------------------- | -------------------------------------------- |
| X-material-appKey          | Y        | appKey                                                     | 7DE4BD442893CF8F                             |
| X-material-timestamp       | Y        | 请求时间戳（13 位毫秒数）                                  | 1687917754629                                |
| X-material-requestId       | Y        | 请求唯一标识（32 位无连接符 uuid, 小写字母、数字组合）     | 08da515877024c1784258e4ac5dfdeb3             |
| X-material-signatureMethod | N        | 签名算法（可选值 HmacSHA256、HmacSHA512。默认 HmacSHA256） | HmacSHA512                                   |
| X-material-signature       | Y        | 签名值                                                     | xzUKmF+D7yJGPqjgXqhUSD5q5dzb1VBOLWTjm9ml9Bw= |

### 签名

签名算法有两种 HmacSHA256 和 HmacSHA512，默认为 HmacSHA256。

#### 签名字符串

签名字符串为 `appKey=xxx + &timestamp=xxx + &requestId=xxx + &signatureMethod=xxx`

示例：`appKey=7DE4BD442893CF8F&timestamp=1686898416209&requestId=10cd83f9adc74ae5801658d6ccb31e61&signatureMethod=HmacSHA256`

#### 签名逻辑

根据签名算法对签名字符串进行加密得到签名值。Java 语言的计算逻辑如下：

```java
private static String hmacSHAEncrypt(String signatureStr, String appSecretKey, String signatureMethod) {
        String hash;
        try {
            Mac sha_HMAC = Mac.getInstance(signatureMethod);
            SecretKeySpec secret_key = new SecretKeySpec(appSecretKey.getBytes(), signatureMethod);
            sha_HMAC.init(secret_key);
            byte[] bytes = sha_HMAC.doFinal(signatureStr.getBytes());
            hash = Base64.getEncoder().encodeToString(bytes);
        } catch (Exception e) {
            LogUtil.getLog().error(signatureMethod + " encrypt error." + e.getMessage());
            throw new MaterialException(MaterialExceptionMessage.SIGNATURE_ENCRYPT_ERROR);
        }
        return hash;
    }
```

### 快速入门

请求头示例：

1. 默认加密算法

```xml
X-material-appKey: 7DE4BD442893CF8F
X-material-timestamp: 1687917754629
X-material-requestId: 08da515877024c1784258e4ac5dfdeb3
X-material-signature: xzUKmF+D7yJGPqjgXqhUSD5q5dzb1VBOLWTjm9ml9Bw=
```

2. 指定加密算法

```xml
X-material-appKey: 7DE4BD442893CF8F
X-material-timestamp: 1687917754629
X-material-requestId: 08da515877024c1784258e4ac5dfdeb3
X-material-signatureMethod: HmacSHA512
X-material-signature: 2AiZ2VltNi/1MuMqIIYdHgK4qOfP75cf3Ixcseq7nJ69//Oee3Mo0KV8n7NTGgl0PyCQgp1KnuRJKskLYuFBig==
```

### 公共响应体定义

```java
boolean isSuccess;
String errCode;
String errMessage;
T data;
```

1. 请求正常

   isSuccess 为 true，errCode 和 errMessage 为空，数据为 data。

2. 请求异常

   isSuccess 为 false，errCode 和 errMessage 为异常数据，数据为空。

# 服务列表

## 数据重组

数据重组分为两步，第一步校验称重数据，第二步确认数据重组

### 校验数据

#### 请求类型

```xml
POST
```

#### 请求路径

```xml
/api/openapi/weigh/data/assemble
```

#### 参数模型

| 参数名          | 是否必须 | 类型   | 描述                  |
| --------------- | -------- | ------ | --------------------- |
| first           | Y        | string | 第一条称重数据称重 id |
| second          | Y        | string | 第二条称重数据称重 id |
| attributionCode | Y        | string | 归属方 code           |

#### 响应模型

| 字段名    | 类型       | 描述                        |
| --------- | ---------- | --------------------------- |
| typeStr   | String     | 1：载车称重，2：净货称重    |
| deviceSn  | String     | 终端 sn                     |
| gmtCreate | String     | 上传时间                    |
| recordId  | String     | 终端记录 id                 |
| truckNo   | String     | 车牌号                      |
| material  | String     | 材料名称                    |
| weight    | BigDecimal | 重量                        |
| unit      | String     | 称重单位                    |
| weighTime | String     | 称重时间                    |
| combine   | Boolean    | 是否重组过 true 是 false 否 |
| pic       | List       | 称重预览照片                |
| urls      | List       | 称重真实下载地址            |
| uuids     | List       | 图片 uuId                   |

#### SDK 调用

```java
// 创建MaterialClient实例
Material materialClient = new MaterialClientBuilder().build(endpoint, appKey, appSecretKey);
// 构造参数
WeighDataAssembleForm assembleForm = new WeighDataAssembleForm();
// 参数赋值
// ...
// 调用接口
WeighDataAssemble weighDataAssemble = materialClient.weighDataAssemble(assembleForm);
```

### 重组数据

#### 请求类型

```xml
POST
```

#### 请求路径

```xml
/api/openapi/weigh/data/confirm
```

#### 参数模型

| 参数名          | 是否必须 | 类型   | 描述                  |
| --------------- | -------- | ------ | --------------------- |
| first           | Y        | string | 第一条称重数据称重 id |
| second          | Y        | string | 第二条称重数据称重 id |
| attributionCode | Y        | string | 归属方 code           |

#### 响应模型

```
true: 重组成功  false: 重组失败
```

#### SDK 调用

```java
// 创建MaterialClient实例
Material materialClient = new MaterialClientBuilder().build(endpoint, appKey, appSecretKey);
// 构造参数
WeighDataAssembleForm assembleForm = new WeighDataAssembleForm();
// 参数赋值
// ...
// 调用接口
Boolean weighDataConfirm = materialClient.weighDataConfirm(assembleForm);
```

### 查询数据

分页接口，默认一次查询 1000 条数据，一次最大查询 1000 条数据。

#### 请求类型

```xml
POST
```

#### 请求路径

```xml
/api/openapi/weigh/data
```

#### 参数模型

| 参数名          | 是否必须 | 类型          | 描述                             |
| --------------- | -------- | ------------- | -------------------------------- |
| ids             | N        | List          | 称重 id 集合(最大 1000 条)       |
| startTime       | Y        | LocalDateTime | 称重开始时间                     |
| endTime         | N        | LocalDateTime | 称重结束时间（为空表示当前时间） |
| attributionCode | N        | String        | 数据归属方 code                  |
| deviceSn        | N        | String        | 设备机器码                       |
| truckNos        | N        | List\         | 车牌号列表(最大 500 条)          |

#### 响应模型

| 字段名          | 类型       | 描述                                      |
| --------------- | ---------- | ----------------------------------------- |
| id              | String     | 主键 ID                                   |
| gmtCreate       | String     | 创建时间                                  |
| gmtModify       | String     | 修改时间                                  |
| createId        | String     | 创建人 ID                                 |
| modifyId        | String     | 修改人 ID                                 |
| recordId        | String     | 终端记录 id                               |
| uid             | String     | 用户 id                                   |
| attributionId   | String     | 归属方 id                                 |
| attributionCode | String     | 归属方 code                               |
| deviceSn        | String     | 设备机器码                                |
| truckNo         | String     | 车牌号                                    |
| material        | String     | 材料名称                                  |
| weight          | BigDecimal | 重量                                      |
| unit            | String     | 称重单位                                  |
| type            | Byte       | 1：载车称重，2：净货称重                  |
| riskGrade       | String     | 风险等级（低：LOW，中：MIDDLE，高：HIGH） |
| weighTime       | String     | 称重时间                                  |
| lprTruckNo      | String     | 使用车牌识别事后 checkout 的车牌号        |
| pic             | List       | 称重预览照片                              |
| urls            | List       | 称重真实下载地址                          |
| uuids           | List       | 图片 uuId                                 |

#### SDK 调用

```java
// 创建MaterialClient实例
Material materialClient = new MaterialClientBuilder().build(endpoint, appKey, appSecretKey);
// 构造参数
QueryPage<WeighDataQuery> queryPage = new QueryPage<>();
// 参数赋值
// ...
// 调用接口
PageList<WeighDataDTO> pageList = materialClient.queryWeighData(queryPage)
```

### 接收简易称重数据

由第三方自定义接口，维护好后请告知“基石科技称重管理平台”相关人员进行内部的维护。
目的是接收来自“基石科技称重管理平台”发送的简易称重数据，接收到后根据简易称重数据中的数据 id，通过 SDK 请求完整称重数据。

#### 请求类型

```xml
POST
Content-Type: application/json
```

#### 请求路径

```xml
请在基石科技称重管理平台配置数据推送的接口地址，如请求需要加签需配置appKey和appSecretKey
```

#### 参数模型

| 参数名   | 是否必须 | 类型   | 描述                                   |
| -------- | -------- | ------ | -------------------------------------- |
| uid      | Y        | String | 用户 id                                |
| recordId | Y        | String | 称重记录 id                            |
| picId    | Y        | String | 称重照片 ID（仅称重照片标签时存在）    |
| tag      | Y        | String | 数据标签(称重数据：data、称重照片:pic) |

#### 请求参数示例

```json
称重数据：{"uid":"39578adc221c4643af80a58eefd5abc4","recordId":"7eefa5ad73e3474cb6e2573afb1205c3","tag":"data"}
称重照片：{"uid":"39578adc221c4643af80a58eefd5abc4","recordId":"7eefa5ad73e3474cb6e2573afb1205c3","picId":"1484","tag":"pic"}
```

### 请求完整称重数据

#### 请求类型

```xml
GET
```

#### 请求路径

```xml
/api/openapi/weigh/data/get
```

#### 参数模型

| 参数名   | 是否必须 | 类型   | 描述        |
| -------- | -------- | ------ | ----------- |
| recordId | Y        | String | 称重记录 id |

#### 响应模型

| 字段名    | 类型          | 描述                     |
| --------- | ------------- | ------------------------ |
| recordId  | String        | 终端记录 id              |
| uid       | String        | 用户 id                  |
| name      | String        | 归属方名称               |
| code      | String        | 归属方 code              |
| deviceSn  | String        | 设备机器码               |
| truckNo   | String        | 车牌号                   |
| material  | String        | 材料名称                 |
| weight    | BigDecimal    | 重量                     |
| unit      | String        | 称重单位（吨、千克、磅） |
| type      | Byte          | 1 载车称重 2 净货称重    |
| weighTime | LocalDateTime | 称重时间                 |

#### SDK 调用

```java
// 创建MaterialClient实例
Material materialClient = new MaterialClientBuilder().build(endpoint, appKey, appSecretKey);
// 调用接口
WeighPushDataDTO weighPushDataDTO = materialClient.weighDataPushGet(recordId);
// 第三方业务处理
...
```

### 请求完整称重照片

#### 请求类型

```xml
GET
```

#### 请求路径

```xml
/api/openapi/weigh/pic/get
```

#### 参数模型

| 参数名 | 是否必须 | 类型 | 描述            |
| ------ | -------- | ---- | --------------- |
| id     | Y        | Long | 称重数据照片 id |

#### 响应模型

| 字段名      | 类型          | 描述                                       |
| ----------- | ------------- | ------------------------------------------ |
| recordId    | String        | 终端记录 id                                |
| uid         | String        | 用户 id                                    |
| name        | String        | 归属方名称                                 |
| code        | String        | 归属方 code                                |
| filePath    | String        | 本地文件路径                               |
| fileId      | String        | oss 文件 id                                |
| type        | Integer       | 1 过磅照片 2 磅房、操作棚照片 3 操作人照片 |
| size        | BigDecimal    | 照片大小(kb)                               |
| localCTime  | LocalDateTime | 照片本地创建时间                           |
| localId     | String        | 照片本地 id                                |
| previewUrl  | String        | 图片预览地址                               |
| downloadUrl | String        | 图片真实地址                               |

#### SDK 调用

```java
// 创建MaterialClient实例
Material materialClient = new MaterialClientBuilder().build(endpoint, appKey, appSecretKey);
// 调用接口
WeighPushPicDTO weighPushPicDTO = materialClient.weighPicPushGet(id);
// 第三方业务处理
...
```

### 确认推送数据被接收

#### 请求类型

```xml
GET
```

#### 请求路径

```xml
/api/openapi/weigh/data/pushConfirm
```

#### 参数模型

| 参数名   | 是否必须 | 类型   | 描述        |
| -------- | -------- | ------ | ----------- |
| recordId | Y        | String | 称重记录 id |

#### 响应模型

```
true: 确认成功  false: 确认失败
```

#### SDK 调用

```java
// 创建MaterialClient实例
Material materialClient = new MaterialClientBuilder().build(endpoint, appKey, appSecretKey);
// ...
// 调用接口
Boolean flag = materialClient.weighDataPushConfirm(recordId);
```

### 确认推送照片被接收

#### 请求类型

```xml
GET
```

#### 请求路径

```xml
/api/openapi/weigh/pic/pushConfirm
```

#### 参数模型

| 参数名 | 是否必须 | 类型 | 描述            |
| ------ | -------- | ---- | --------------- |
| id     | Y        | Long | 称重数据照片 id |

#### 响应模型

```
true: 确认成功  false: 确认失败
```

#### SDK 调用

```java
// 创建MaterialClient实例
Material materialClient = new MaterialClientBuilder().build(endpoint, appKey, appSecretKey);
// ...
// 调用接口
Boolean flag = materialClient.weighPicPushConfirm(id);
```

### 根据图片 uuId 获取图片预览/下载地址

#### 请求类型

```xml
POST
```

#### 请求路径

```xml
/api/openapi/getPicUrl
```

#### 参数模型

| 参数名 | 是否必须 | 类型    | 描述                                         |
| ------ | -------- | ------- | -------------------------------------------- |
| pic    | Y        | String  | 称重数据照片 uuid,英文逗号分割               |
| isPre  | Y        | boolean | true,获取预览图片地址;false,获取下载图片地址 |

#### 响应模型

```
true: 确认成功  false: 确认失败
```

#### SDK 调用

```java
// 创建MaterialClient实例
Material materialClient = new MaterialClientBuilder().build(endpoint, appKey, appSecretKey);
// 构造参数
PicForm form = new PicForm();
// 参数赋值
// ...
// 调用接口
List<String> list = materialClient.getPicUrl(form);

```

### 新增、更新订单

#### 请求类型

```xml
POST
```

#### 请求路径

```xml
/api/openapi/purchase/add
```

#### 参数模型

| 参数名          | 是否必须 | 类型             | 描述                         |
| --------------- | -------- | ---------------- | ---------------------------- |
| attributionCode | Y        | String           | 归属方 code                  |
| orderExtId      | Y        | String           | 业务系统订单 id              |
| supplierExtId   | Y        | String           | 订单号                       |
| orderType       | Y        | String           | 外部系统供应商 id            |
| position        | Y        | String           | 计划使用部位                 |
| project         | Y        | String           | 收货项目                     |
| address         | Y        | String           | 收货地址                     |
| receiver        | Y        | String           | 收货人姓名                   |
| mobile          | Y        | String           | 收货人电话                   |
| receiveDate     | Y        | String           | 要货日期 yyyy-MM-dd          |
| remark          | N        | String           | 备注                         |
| > list          | Y        | List             | 订单明细                     |
| - extId         | Y        | String           | 业务系统订单明细 id          |
| - name          | Y        | String           | 货物名称                     |
| - spec          | Y        | String           | 规格型号                     |
| - brand         | Y        | String           | 品牌                         |
| - amount        | Y        | BigDecimal(20,4) | 采购数量                     |
| - unit          | Y        | String           | 采购单位                     |
| - deductRatio   | Y        | BigDecimal       | 扣杂                         |
| - scaleFactor   | Y        | BigDecimal       | 称重换算系数                 |
| - unitType      | Y        | int              | 参照重量单位 0 = 千克 1 = 吨 |
| - argument      | Y        | String           | 其他参数                     |
| - position      | Y        | String           | 计划使用部位                 |
| - remark        | Y        | String           | 备注                         |

#### 响应模型

```
true: 确认成功  false: 确认失败
```

#### SDK 调用

```java
// 创建MaterialClient实例
Material materialClient = new MaterialClientBuilder().build(endpoint, appKey, appSecretKey);
// 构造参数
PurchaseForm purchaseForm = new PurchaseForm();
// 参数赋值
// ...
// 调用接口
Boolean isSuccess = materialClient.purchaseOrderAdd(purchaseForm);
```

### 确认单详情

#### 请求类型

```xml
GET
```

#### 请求路径

```xml
/api/openapi//confirmDetail
```

#### 参数模型

| 参数名 | 是否必须 | 类型 | 描述      |
| ------ | -------- | ---- | --------- |
| id     | Y        | Long | 确认单 id |

#### 响应模型

| 参数名            | 类型              | 描述                                                        |
| ----------------- | ----------------- | ----------------------------------------------------------- |
| truckNo           | String            | 车牌号                                                      |
| weighType         | Integer           | 称重类型                                                    |
| weightGross       | BigDecimal        | 毛重                                                        |
| weightTare        | BigDecimal        | 皮重                                                        |
| weightDeduct      | BigDecimal        | 扣重                                                        |
| weightNet         | BigDecimal        | 净重                                                        |
| moistureContent   | BigDecimal        | 扣重                                                        |
| weightActual      | BigDecimal        | 实重                                                        |
| ratio             | BigDecimal        | 换算系数                                                    |
| actualCount       | BigDecimal        | 实际数量：实重 / 换算系数                                   |
| weightUnit        | String            | 结算单位                                                    |
| grossTime         | LocalDateTime     | 毛重时间                                                    |
| tareTime          | LocalDateTime     | 皮重时间                                                    |
| grossId           | String            | 毛重记录 id                                                 |
| tareId            | String            | 皮重记录 id                                                 |
| grossRiskGrade    | String            | 毛重风险等级                                                |
| grossPic          | String            | 毛重照片                                                    |
| tarePic           | String            | 皮重照片                                                    |
| tareRiskGrade     | String            | 皮重风险等级                                                |
| documentPic       | String            | 单据照片                                                    |
| signPic           | String            | 签名照片                                                    |
| signerPic         | String            | 签名人照片                                                  |
| printCount        | Integer           | 确认单打印次数                                              |
| > deliveryDetail  | DeliveryDetailDTO | 发货单详情                                                  |
| - id              | Long              | 发货单 id                                                   |
| - no              | String            | 发货单号                                                    |
| - purchaseOrderId | Long              | 订单 id                                                     |
| - truckNo         | String            | 车牌号                                                      |
| - driver          | String            | 司机姓名                                                    |
| - driverMobile    | String            | 司机手机号                                                  |
| - status          | Integer           | 状态 1 在途，2 到场确认中，3 已到场，4 已作废               |
| - pushState       | Integer           | 推送状态 1 未推送，2 推送中，3 已推送                       |
| - orderExtId      | String            | 业务系统订单 id                                             |
| - supplierExtId   | String            | 外部系统供应商 id                                           |
| - supplierId      | String            | 基石系统供应商 id                                           |
| - supplierName    | String            | 供应商名称                                                  |
| - project         | String            | 收货项目                                                    |
| - address         | String            | 收货地址                                                    |
| - receiver        | String            | 收货人姓名                                                  |
| - mobile          | String            | 收货人电话                                                  |
| - receiveDate     | LocalDate         | 要货日期                                                    |
| - gmtCreate       | LocalDateTime     | 发货单时间                                                  |
| - remark          | String            | 备注                                                        |
| > list            | List              | 发货单明细列表                                              |
| - id              | Long              | 订单明细 id                                                 |
| - name            | String            | 货物名称                                                    |
| - spec            | String            | 规格型号                                                    |
| - brand           | String            | 品牌                                                        |
| - amount          | BigDecimal(20,4)  | 采购数量                                                    |
| - unit            | String            | 采购单位                                                    |
| - orderAmount     | BigDecimal        | 客户下单数量                                                |
| - sendAmount      | BigDecimal        | 累计发货数量                                                |
| - truckAmount     | Integer           | 累计发货车次                                                |
| - currentAmount   | BigDecimal        | 本次发货数量                                                |
| - unitType        | int               | 参照重量单位 0 = 千克 1 = 吨                                |
| - argument        | String            | 其他参数                                                    |
| - position        | String            | 计划使用部位                                                |
| - remark          | String            | 备注                                                        |
| - status          | Integer           | 运单明细 1 在途，2 待确认，3 自动确认中，4 已确认，5 已作废 |

#### SDK 调用

```java
// 创建MaterialClient实例
Material materialClient = new MaterialClientBuilder().build(endpoint, appKey, appSecretKey);
// 调用接口
WeighDataConfirmDetailDTO result = materialClient.confirmDetail(id);
```
## 基石平台推送服务

为满足第三方用户获取基石平台数据，基石平台提供回调服务接口。在基石平台 SDK 中，提供了回调服务接口的定义，位于`cn.pinming.material.v2.service`包路径下，用户只需实现回调接口并将其部署至公网，然后在`基石平台-开发者-回调配置`中配置好 SDK 回调网关地址，最后开启对应的回调服务的接口即可。

### 推送服务注意事项

1.网关需要填写用户自己系统的真实地址，不能填写接口文档或 demo 上的示例地址。  
2.网关必须是以 `https://`或`http://`开头的完整全路径地址，并且确保 URL 中的域名和 IP 是外网可以访问的，不能填写 localhost、127.0.0.1、192.168.x.x 等本地或内网 IP。  
3.网关不能携带参数。  
4.网关的代码处理逻辑不能做登录态校验。  
5.用户系统收到推送接口请求，需要在5秒内返回应答报文，否则基石平台认为请求失败，后续会重复发送请求。

| 错误描述 | 错误示例 |
| ----------------------------- | ----------------------------- |
| URL中只有域名，缺少具体的路径     | `https://weighmaster.pinming.cn`      |
| URL不是以`https://`或`http://`开头，缺少域名或IP     | `./callback.aspx`      |
| URL中填写了本地或者内网IP     | `http://127.0.0.1/callback/notify.php`      |
| 填写了不是URL格式的字符串     | `xxxxxxx`，`1234567`，`test`     |

### 请求数据签名校验
```java
每一次请求，请求体都会携带signature和timestamp，通过比对请求体中携带的signature和自己生成的签名是否一致，来判断请求是否合法。
/**
 * 生成签名
 *
 * @param appKey       appKey
 * @param appSecretKey appSecretKey
 * @param timestamp    时间戳
 * @return 签名
 */
cn.pinming.material.v2.util.SignUtil#sign
```

### 推送服务接口定义
#### 接收到运单信息

```java

/**
 * 接收到运单信息
 * @return 已处理的jsDeliveryNo列表
 */
cn.pinming.material.v2.service.IDataDeliveryService#receiveDeliveryOrder
```

#### 接收确认单信息

```java
 /**
  * 接收确认单信息
  * @return 已处理的确认单id列表
  */
cn.pinming.material.v2.service.IDataConfirmService#receiveConfirmOrder
```
