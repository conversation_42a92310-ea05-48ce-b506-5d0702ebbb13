package cn.pinming.microservice.material.client.management.email.send;

import cn.pinming.microservice.material.client.management.email.message.EmailMessage;
import lombok.extern.slf4j.Slf4j;

import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import java.util.Properties;

/**
 * <AUTHOR>
 * @date 2023/6/13
 */
@Slf4j

public class EmailSender {

    private static final String SENDER_EMAIL = "<EMAIL>";
    private static final String SENDER_PASSWORD = "8C356jp9nnFBZESN";
    private static final String MAIL_SMTP_AUTH = "true";
    private static final String MAIL_SMTP_STARTTLS_ENABLE = "true";
    private static final String MAIL_SMTP_HOST = "smtp.exmail.qq.com";
    private static final String MAIL_SMTP_PORT = "465";

    public static Boolean sendEmail(String recipientEmail, EmailMessage emailMessage) {

        // 设置邮件发送的属性
        Properties props = new Properties();
        props.put("mail.smtp.auth", MAIL_SMTP_AUTH);
        props.put("mail.smtp.starttls.enable", MAIL_SMTP_STARTTLS_ENABLE);
        props.put("mail.smtp.host", MAIL_SMTP_HOST);
        props.put("mail.smtp.port", MAIL_SMTP_PORT);
        props.put("mail.smtp.socketFactory.port", MAIL_SMTP_PORT);
        props.put("mail.smtp.socketFactory.class", "javax.net.ssl.SSLSocketFactory");
        props.put("mail.smtp.socketFactory.fallback", "false");
        // 创建Session对象，并进行身份验证
        Session session = Session.getInstance(props, new javax.mail.Authenticator() {
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(SENDER_EMAIL, SENDER_PASSWORD);
            }
        });

        try {
            // 创建MimeMessage对象
            MimeMessage message = new MimeMessage(session);

            // 设置发件人
            message.setFrom(new InternetAddress(SENDER_EMAIL));

            // 设置收件人
            message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(recipientEmail));

            // 设置邮件主题和内容
            message.setSubject(emailMessage.getTitle());
            message.setText(emailMessage.getMessage());

            // 发送邮件
            Transport.send(message);

            log.info("邮件发送成功");
        } catch (MessagingException e) {
            log.error("邮件发送失败：" + e.getMessage());
            return false;
        }
        return true;
    }

}
