<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">

  	<modelVersion>4.0.0</modelVersion>
  	<parent>
    	<groupId>org.springframework.boot</groupId>
    	<artifactId>spring-boot-starter-parent</artifactId>
    	<version>2.5.2</version>
    	<relativePath/> <!-- lookup parent from repository -->
  	</parent>

    <groupId>cn.pinming.microservice</groupId>
  	<artifactId>material-client-management</artifactId>
  	<version>0.0.1-SNAPSHOT</version>
  	<name>material-client-management</name>
  	<description>基石科技称重管理平台</description>
  	<packaging>pom</packaging>

  	<modules>
    	<module>material-client-service</module>
        <module>email-service</module>
        <module>push-service</module>
        <module>api-service</module>
        <!--        <module>material-client-model</module>-->
    </modules>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <dubbo.version>3.2.1</dubbo.version>
        <curator.version>4.2.0</curator.version>
        <zookeeper.version>3.5.7</zookeeper.version>
<!--        <zhuang-api.version>2023.5-SNAPSHOT</zhuang-api.version>-->
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.pinming</groupId>
                <artifactId>pms-dependency</artifactId>
                <version>1.0-SNAPSHOT</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.24</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.boot</groupId>
                <artifactId>nacos-config-spring-boot-starter</artifactId>
                <version>0.2.12</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>2.7.5</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>3.4.2</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>8.0.23</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>2.7.5</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>3.4.1</version>
            </dependency>
            <dependency>
                <groupId>org.freemarker</groupId>
                <artifactId>freemarker</artifactId>
                <version>2.3.31</version>
            </dependency>
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>2.2</version>
            </dependency>
            <dependency>
                <groupId>cn.pinming</groupId>
                <artifactId>pms-redis-spring-boot-starter</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.83</version>
            </dependency>
<!--            <dependency>-->
<!--                <groupId>io.springfox</groupId>-->
<!--                <artifactId>springfox-swagger2</artifactId>-->
<!--                <version>2.9.2</version>-->
<!--            </dependency>-->
<!--            <dependency>-->
<!--                <groupId>io.springfox</groupId>-->
<!--                <artifactId>springfox-swagger-ui</artifactId>-->
<!--                <version>2.9.2</version>-->
<!--            </dependency>-->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-spring-boot-starter</artifactId>
                <version>1.34.0</version>
            </dependency>
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-dao-redis-jackson</artifactId>
                <version>1.34.0</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>5.8.25</version>
            </dependency>
<!--            <dependency>-->
<!--                <groupId>com.dyuproject.protostuff</groupId>-->
<!--                <artifactId>protostuff-core</artifactId>-->
<!--                <version>1.0.10</version>-->
<!--            </dependency>-->
<!--            <dependency>-->
<!--                <groupId>com.dyuproject.protostuff</groupId>-->
<!--                <artifactId>protostuff-runtime</artifactId>-->
<!--                <version>1.0.10</version>-->
<!--            </dependency>-->
            <dependency>
                <groupId>org.objenesis</groupId>
                <artifactId>objenesis</artifactId>
                <version>2.4</version>
            </dependency>
            <dependency>
                <groupId>com.github.penggle</groupId>
                <artifactId>kaptcha</artifactId>
                <version>2.3.2</version>
            </dependency>
            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>javax.servlet-api</artifactId>
                <version>4.0.1</version>
            </dependency>
            <dependency>
                <groupId>javax.mail</groupId>
                <artifactId>javax.mail-api</artifactId>
                <version>1.6.2</version>
            </dependency>
            <dependency>
                <groupId>com.sun.mail</groupId>
                <artifactId>javax.mail</artifactId>
                <version>1.6.2</version>
            </dependency>

            <!-- dubbo -->
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-dependencies-bom</artifactId>
                <version>${dubbo.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>cn.pinming.zhjz.cxptz.microservice</groupId>
                <artifactId>file-center-api</artifactId>
                <version>2.2.1-SNAPSHOT</version>
            </dependency>
<!--            <dependency>-->
<!--                <groupId>cn.pinming</groupId>-->
<!--                <artifactId>zhuang</artifactId>-->
<!--                <version>${zhuang-api.version}</version>-->
<!--            </dependency>-->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>5.2.3</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <repositories>
        <repository>
            <id>pms-nexus-releases</id>
            <name>Nexus Release Repository</name>
            <url>http://nexus.pinming.org/repository/maven-releases/</url>
            <releases><enabled>true</enabled></releases>
            <snapshots><enabled>false</enabled></snapshots>
        </repository>
        <repository>
            <id>pms-nexus-snapshots</id>
            <name>Nexus Snapshot Repository</name>
            <url>http://nexus.pinming.org/repository/maven-snapshots/</url>
            <releases><enabled>false</enabled></releases>
            <snapshots><enabled>true</enabled></snapshots>
        </repository>
        <repository>
            <id>pms-nexus-thirdparty</id>
            <name>PMS thirdparty Repository</name>
            <url>http://nexus.pinming.org/repository/3rd-party/</url>
            <releases><enabled>true</enabled></releases>
            <snapshots><enabled>true</enabled></snapshots>
        </repository>
        <repository>
            <id>rdc-releases</id>
            <url>https://packages.aliyun.com/maven/repository/2429771-release-ykc3lH/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>

    <distributionManagement>
        <repository>
            <id>pms-nexus-releases</id>
            <name>Nexus Release Repository</name>
            <url>http://nexus.pinming.org/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>pms-nexus-snapshots</id>
            <name>Nexus Snapshot Repository</name>
            <url>http://nexus.pinming.org/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <finalName>material-client-management</finalName>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>3.0.1</version>
                    <configuration>
                        <encoding>UTF-8</encoding>
                        <additionalOptions>
                            -Xdoclint:none
                        </additionalOptions>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>2.4</version>
                    <!-- 进行package命令时就可以将源码同时进行打包 -->
                    <!-- 所以我们需要绑定source插件到我们default生命周期的package阶段 -->
                    <executions>
                        <execution>
                            <!-- 定义一个阶段，这个阶段是package -->
                            <phase>package</phase>
                            <goals>
                                <goal>jar-no-fork</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.1</version>
                    <configuration>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.mapstruct</groupId>
                                <artifactId>mapstruct-processor</artifactId>
                                <version>1.4.2.Final</version>
                            </path>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>1.18.24</version>
                            </path>
                        </annotationProcessorPaths>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-archetype-plugin</artifactId>
                <version>3.2.0</version>
            </plugin>
        </plugins>
    </build>

</project>
