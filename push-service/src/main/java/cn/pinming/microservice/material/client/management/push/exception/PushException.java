package cn.pinming.microservice.material.client.management.push.exception;

/**
 * <AUTHOR>
 * 
 */
public class PushException extends RuntimeException {

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String errorMessage;

    public PushException(PushExceptionMessage bizExceptionMessageEnum) {
        this.errorCode = bizExceptionMessageEnum.errorCode();
        this.errorMessage = bizExceptionMessageEnum.errorMessage();
    }

    public PushException(PushExceptionMessage bizExceptionMessageEnum, String format) {
        this.errorCode = bizExceptionMessageEnum.errorCode();
        this.errorMessage = String.format(bizExceptionMessageEnum.errorMessage(), format);
    }

    public PushException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
        this.errorMessage = message;
    }

    public String errorCode() {
        return errorCode;
    }

    public String errorMessage() {
        return errorMessage;
    }
}
