//package cn.pinming.microservice.material.client.management.push;
//
//import com.alibaba.fastjson.JSONObject;
//
//import java.util.Arrays;
//
//public class PushTest {
//
//    public static void main(String[] args) {
//        test1();
//    }
//
//    private static void test2() {
//        String header = "[{\"headerName\":\"X-material-appKey\",\"isMust\":true,\"headerValue\":null,\"headerMapping\":\"appKey\"},{\"headerName\":\"X-material-timestamp\",\"isMust\":true,\"headerValue\":null,\"headerMapping\":\"timestamp\"},{\"headerName\":\"X-material-requestId\",\"isMust\":true,\"headerValue\":null,\"headerMapping\":\"requestId\"},{\"headerName\":\"X-material-signatureMethod\",\"isMust\":true,\"headerValue\":\"HmacSHA256\",\"headerMapping\":null},{\"headerName\":\"X-material-signature\",\"isMust\":true,\"headerValue\":null,\"headerMapping\":\"signature\"}]";
//        Push push = new PushClient("http://172.16.15.168:8080", "/api/openapi/weigh/data/get", header, "signature", "C71033004957E800", "D0FDD7277E05252542469E90F16B50A4",
//                "appKey={appKey}&timestamp={timestamp}&requestId={requestId}&signatureMethod=HmacSHA256", "HmacSHA256", null, null);
//        push.pushData(JSONObject.toJSONString(Arrays.asList("6878c02e5e4d4c08b8776c5bfd8f49fe", "********************************")));
//    }
//
//    private static void test1() {
//        String header = "[{\"headerName\":\"X-DM-Open-App-Id\",\"isMust\":true,\"headerValue\":null,\"headerMapping\":\"appKey\"},{\"headerName\":\"X-DM-Open-Ca-Timestamp\",\"isMust\":true,\"headerValue\":null,\"headerMapping\":\"timestamp\"},{\"headerName\":\"Accept\",\"isMust\":true,\"headerValue\":\"*/*\",\"headerMapping\":null},{\"headerName\":\"Content-Type\",\"isMust\":true,\"headerValue\":\"application/json;charset=UTF-8\",\"headerMapping\":null},{\"headerName\":\"X-DM-Open-Auth-Mode\",\"isMust\":true,\"headerValue\":\"Signature\",\"headerMapping\":null},{\"headerName\":\"X-DM-Open-Ca-Signature\",\"isMust\":true,\"headerValue\":null,\"headerMapping\":\"signature\"}]";
//        Push push = new PushClient("https://dev-hardware-api.hzdingmao.com", "/api/v2/thirdParty/postTest", header, "signature", "1496161423", "E64BAE54-CF50-DAFF-D900-9FF2872C4CF5",
//                "POST\n" +
//                        "{Accept}\n" +
//                        "{Content-Type}\n" +
//                        "{X-DM-Open-Ca-Timestamp}\n" +
//                        "{requestUri}", "HmacSHA256", "JSONObject", "code=1");
//        push.pushData("{\"code\": 201,\"evaWallScore\": \"50\",\"pageNum\": 1,\"maxCount\": 10}");
//    }
//
//    private static void test() {
//        String header = "[{\"headerName\":\"X-DM-Open-App-Id\",\"isMust\":true,\"headerValue\":null,\"headerMapping\":\"appKey\"},{\"headerName\":\"X-DM-Open-Ca-Timestamp\",\"isMust\":true,\"headerValue\":null,\"headerMapping\":\"timestamp\"},{\"headerName\":\"Accept\",\"isMust\":true,\"headerValue\":\"*/*\",\"headerMapping\":null},{\"headerName\":\"Content-Type\",\"isMust\":true,\"headerValue\":\"application/json;charset=UTF-8\",\"headerMapping\":null},{\"headerName\":\"X-DM-Open-Auth-Mode\",\"isMust\":true,\"headerValue\":\"Signature\",\"headerMapping\":null},{\"headerName\":\"X-DM-Open-Ca-Signature\",\"isMust\":true,\"headerValue\":null,\"headerMapping\":\"signature\"}]";
//        Push push = new PushClient("https://dev-hardware-api.hzdingmao.com", "/api/v2/thirdParty/postTest", header, "none", null, null);
//        push.pushData("{\"code\": 201,\"evaWallScore\": \"50\",\"pageNum\": 1,\"maxCount\": 10}");
//    }
//}
