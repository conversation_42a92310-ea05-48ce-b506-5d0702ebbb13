package cn.pinming.microservice.material.client.management.push.common.request;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.http.Method;
import cn.hutool.json.JSONUtil;
import cn.pinming.microservice.material.client.management.push.PushConfiguration;
import cn.pinming.microservice.material.client.management.push.signature.Signature;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public abstract class ServiceRequest {

    private String uri;
    private Method httpMethod;
    private Map<String, String> headers;
    private Map<String, Object> params;
    private String body;

    public ServiceRequest(PushConfiguration pushConfiguration) {
        this.setUri(pushConfiguration.getEndpoint() + pushConfiguration.getRequestUrl());
        this.setHttpMethod(Method.POST);
        buildRequestHeaders(pushConfiguration);
    }

    public void buildRequestHeaders(PushConfiguration pushConfiguration) {
        //构建headers
        Map<String, String> headers = new HashMap<>();
        if (StringUtils.isNotEmpty(pushConfiguration.getRequestHeader())) {
            List<RequestHeader> requestHeaders = JSONUtil.toList(pushConfiguration.getRequestHeader(), RequestHeader.class);
            if (CollectionUtil.isNotEmpty(requestHeaders)) {
                buildCustomRequestHeaders(pushConfiguration, requestHeaders, headers);
            }
        }
        this.setHeaders(headers);
    }

    public abstract void buildCustomRequestHeaders(PushConfiguration pushConfiguration, List<RequestHeader> requestHeaders, Map<String, String> headers);


    public Map<String, String> getRequestHeaders(List<RequestHeader> requestHeaders, Signature signature) {
        if (CollectionUtil.isEmpty(requestHeaders)) {
            return Collections.emptyMap();
        }
        Map<String, Object> signatureMap = BeanUtil.beanToMap(signature, false, false);
        requestHeaders.forEach(header -> {
            if (StringUtils.isNotEmpty(header.getHeaderMapping()) && signatureMap.containsKey(header.getHeaderMapping())) {
                Object value = signatureMap.get(header.getHeaderMapping());
                if (value != null) {
                    header.setHeaderValue(value.toString());
                }
            }
        });
        return requestHeaders.stream().collect(HashMap::new, (map, item) -> map.put(item.getHeaderName(), item.getHeaderValue()), HashMap::putAll);
    }

}
