package cn.pinming.microservice.material.client.management.push.common.request;

import cn.pinming.microservice.material.client.management.push.PushConfiguration;
import cn.pinming.microservice.material.client.management.push.signature.Signature;
import cn.pinming.microservice.material.client.management.push.util.UuidUtil;

import java.util.List;
import java.util.Map;

public class NoneRequest extends ServiceRequest {

    public NoneRequest(PushConfiguration pushConfiguration) {
        super(pushConfiguration);
    }

    @Override
    public void buildCustomRequestHeaders(PushConfiguration pushConfiguration, List<RequestHeader> requestHeaders, Map<String, String> headers) {
        Signature signature = buildSignature(pushConfiguration);
        headers.putAll(super.getRequestHeaders(requestHeaders, signature));
    }

    public Signature buildSignature(PushConfiguration pushConfiguration) {
        Signature signature = new Signature();
        signature.setEndpoint(pushConfiguration.getEndpoint());
        signature.setRequestUri(pushConfiguration.getRequestUrl());
        signature.setTimestamp(String.valueOf(System.currentTimeMillis()));
        signature.setRequestId(UuidUtil.uuidWithoutConnector());
        return signature;
    }

}


