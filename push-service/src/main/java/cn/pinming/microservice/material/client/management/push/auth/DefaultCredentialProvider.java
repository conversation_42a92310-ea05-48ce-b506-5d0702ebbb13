package cn.pinming.microservice.material.client.management.push.auth;

import cn.pinming.microservice.material.client.management.push.exception.PushException;
import cn.pinming.microservice.material.client.management.push.exception.PushExceptionMessage;

/**
 * <AUTHOR>
 * 
 */
public class DefaultCredentialProvider implements CredentialsProvider {

    private volatile Credentials creds;

    public DefaultCredentialProvider(String appKey, String appSecretKey, String signatureTemplate, String signatureAlgorithm) {
        checkCredentials(appKey, appSecretKey);
        setCredentials(new DefaultCredentials(appKey, appSecretKey, signatureTemplate, signatureAlgorithm));
    }

    @Override
    public synchronized void setCredentials(Credentials creds) {
        if (creds == null) {
            throw new PushException(PushExceptionMessage.EMPTY_CREDS);
        }

        checkCredentials(creds.getAppKey(), creds.getAppSecretKey());
        this.creds = creds;
    }

    @Override
    public Credentials getCredentials() {
        if (this.creds == null) {
            throw new PushException(PushExceptionMessage.INVALID_CREDENTIALS);
        }

        return this.creds;
    }

    private static void checkCredentials(String appKey, String appSecretKey) {
        if (appKey == null || appKey.equals("")) {
            throw new PushException(PushExceptionMessage.EMPTY_APP_KEY);
        }

        if (appSecretKey == null || appSecretKey.equals("")) {
            throw new PushException(PushExceptionMessage.EMPTY_APP_SECRET_KEY);
        }
    }
}
