package cn.pinming.microservice.material.client.management.push;

import cn.hutool.core.util.ObjectUtil;
import cn.pinming.microservice.material.client.management.push.auth.CredentialsProvider;
import cn.pinming.microservice.material.client.management.push.auth.DefaultCredentialProvider;
import cn.pinming.microservice.material.client.management.push.common.enums.RequestModeEnum;
import cn.pinming.microservice.material.client.management.push.common.enums.ResponseTypeEnum;
import cn.pinming.microservice.material.client.management.push.common.request.NoneRequest;
import cn.pinming.microservice.material.client.management.push.common.request.ServiceRequest;
import cn.pinming.microservice.material.client.management.push.common.request.SignatureRequest;
import cn.pinming.microservice.material.client.management.push.exception.PushException;
import cn.pinming.microservice.material.client.management.push.exception.PushExceptionMessage;
import cn.pinming.microservice.material.client.management.push.http.HttpClient;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */
@Slf4j
public class PushClient implements Push {

    private final PushConfiguration pushConfiguration = new PushConfiguration();

    public PushClient(String endpoint, String requestUrl, String requestHeader, String requestMode,
                      String responseType, String responseFlag) {
        this.pushConfiguration.setEndpoint(endpoint);
        this.pushConfiguration.setRequestUrl(requestUrl);
        this.pushConfiguration.setRequestHeader(requestHeader);
        this.pushConfiguration.setRequestMode(requestMode);
        this.pushConfiguration.setResponseType(responseType);
        this.pushConfiguration.setResponseFlag(responseFlag);
    }

    public PushClient(String endpoint, String requestUrl, String requestHeader, String requestMode,
                      String appKey, String appSecretKey, String signatureTemplate, String signatureAlgorithm,
                      String responseType, String responseFlag) {
        if (StringUtils.isEmpty(endpoint) || StringUtils.isEmpty(requestUrl)) {
            throw new PushException(PushExceptionMessage.EMPTY_HOST);
        }
        CredentialsProvider credentialsProvider = null;
        if (RequestModeEnum.SIGNATURE.getCode().equals(requestMode)) {
            credentialsProvider = new DefaultCredentialProvider(appKey, appSecretKey, signatureTemplate, signatureAlgorithm);
        }
        this.pushConfiguration.setEndpoint(endpoint);
        this.pushConfiguration.setRequestUrl(requestUrl);
        this.pushConfiguration.setRequestHeader(requestHeader);
        this.pushConfiguration.setRequestMode(requestMode);
        this.pushConfiguration.setCredentialsProvider(credentialsProvider);
        this.pushConfiguration.setResponseType(responseType);
        this.pushConfiguration.setResponseFlag(responseFlag);
    }

    @Override
    public void pushData(String jsonBody) throws Exception {
        ServiceRequest serviceRequest;
        if (RequestModeEnum.SIGNATURE.getCode().equals(pushConfiguration.getRequestMode())) {
            serviceRequest = new SignatureRequest(pushConfiguration);
        } else if (ObjectUtil.isNull(pushConfiguration.getRequestMode()) || RequestModeEnum.NONE.getCode().equals(pushConfiguration.getRequestMode())) {
            serviceRequest = new NoneRequest(pushConfiguration);
        } else {
            throw new PushException(PushExceptionMessage.REQUEST_MODE_NOT_FOUND);
        }
        serviceRequest.setBody(jsonBody);
        log.info("pushData入参jsonBody:{},header:{}", jsonBody, JSONObject.toJSONString(serviceRequest.getHeaders()));
        String response = HttpClient.doPost(serviceRequest);
        log.info("pushData结果response:{}", response);
        //校验
        if (StringUtils.isNotEmpty(pushConfiguration.getResponseType())
                && StringUtils.isNotEmpty(pushConfiguration.getResponseFlag())) {
            responseVerify(response);
        }
    }

    private void responseVerify(String response) {
        Boolean flag = null;
        if (ResponseTypeEnum.JSONObject.getCode().equals(pushConfiguration.getResponseType())) {
            JSONObject jsonObject = JSONObject.parseObject(response);
            String[] responseFlag = pushConfiguration.getResponseFlag().split("=");
            Object value = jsonObject.get(responseFlag[0].trim());
            flag = value != null && value.toString().equals(responseFlag[1].trim());
        } else if (ResponseTypeEnum.JSONArray.getCode().equals(pushConfiguration.getResponseType())) {
            JSONArray jsonArray = JSONArray.parseArray(response);
            String[] responseFlag = pushConfiguration.getResponseFlag().split("=");
            Object value = jsonArray.getJSONObject(0).get(responseFlag[0].trim());
            flag = value != null && value.toString().equals(responseFlag[1].trim());
        } else if (ResponseTypeEnum.STRING.getCode().equals(pushConfiguration.getResponseType())) {
            flag = pushConfiguration.getResponseFlag().equals(response);
        }
        if (flag == null || !flag) {
            log.info("pushData结果Verify失败:{}", response);
            throw new PushException(PushExceptionMessage.RESPONSE_VALUE_ERROR, response);
        }
        log.info("pushData结果Verify成功");
    }
}
