package cn.pinming.microservice.material.client.management.push.http;

import cn.hutool.http.*;
import cn.pinming.microservice.material.client.management.push.common.request.ServiceRequest;
import cn.pinming.microservice.material.client.management.push.exception.PushException;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
public class HttpClient {

    private static final int HTTP_TIMEOUT = 60000;

    public static String doGet(ServiceRequest request) throws PushException {
        HttpRequest getRequest = HttpUtil.createRequest(Method.GET, request.getUri());
        headersInit(request, getRequest);
        HttpResponse httpResponse = getRequest.form(request.getParams()).timeout(HTTP_TIMEOUT).execute();
        return responseVerify(httpResponse);
    }

    public static String doPost(ServiceRequest request) throws PushException {
//        log.error("doPost request:{}, body:{}", request.getUri(), request.getBody());
        HttpRequest postRequest = HttpUtil.createRequest(Method.POST, request.getUri());
//        headersInit(request, postRequest);
        HttpResponse httpResponse = postRequest.body(request.getBody()).timeout(HTTP_TIMEOUT).execute();
        return responseVerify(httpResponse);
    }

    private static void headersInit(ServiceRequest serviceRequest, HttpRequest httpRequest) {
        Map<String, String> headers = serviceRequest.getHeaders();
        headers.forEach(httpRequest::header);
    }

    private static String responseVerify(HttpResponse httpResponse) throws PushException {
        if (httpResponse.getStatus() == HttpStatus.HTTP_OK) {
            return httpResponse.body();
        }
        throw new PushException(String.valueOf(httpResponse.getStatus()), httpResponse.body());
    }

}
