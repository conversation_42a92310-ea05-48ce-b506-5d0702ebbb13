package cn.pinming.microservice.material.client.management.push.signature;


import lombok.Data;

import java.io.Serializable;


/**
 * <AUTHOR>
 */
@Data
public class Signature implements Serializable {

    /**
     * 请求Region
     */
    private String endpoint;
    /**
     * request uri
     */
    private String requestUri;
    /**
     * http method
     */
    private String httpMethod;
    /**
     * appKey
     */
    private String appKey;
    /**
     * 请求时间戳, 1分钟内的请求属于有效请求
     */
    private String timestamp;
    /**
     * 请求唯一标识(32位无连接符uuid, 小写字母、数字组合)
     */
    private String requestId;
    /**
     * 应用密钥key
     */
    private String appSecretKey;

    /**
     * 签名模板
     */
    private String signatureTemplate;

    /**
     * 签名算法
     */
    private String signatureAlgorithm;
}
