package cn.pinming.microservice.material.client.management.push.common.request;

import lombok.Data;

import java.io.Serializable;

@Data
public class RequestHeader implements Serializable {

    /**
     * 请求头名称
     */
    private String headerName;

    /**
     * 是否必须
     */
    private Boolean isMust;

    /**
     * 请求头值
     */
    private String headerValue;

    /**
     * 请求头映射字段
     */
    private String headerMapping;

    /**
     * 请求头扩展字段
     */
    private String headerExtend;

}
