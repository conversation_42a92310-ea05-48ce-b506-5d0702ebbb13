package cn.pinming.microservice.material.client.management.push.exception;

/**
 * <AUTHOR>
 * 
 */
public enum PushExceptionMessage {

    SYSTEM_ERROR("500", "系统异常"),
    REQUEST_MODE_NOT_FOUND("REQUEST_MODE_NOT_FOUND", "未知的请求模式"),
    SIGNATURE_ENCRYPT_ERROR("SIGNATURE_ENCRYPT_ERROR", "签名加密异常"),
    EMPTY_HOST("EMPTY_HOST", "请求地址不能为空"),
    EMPTY_APP_KEY("EMPTY_APP_KEY", "appKey不能为空"),
    EMPTY_APP_SECRET_KEY("EMPTY_APP_SECRET_KEY", "appSecretKey不能为空"),
    EMPTY_CREDS("EMPTY_CREDS", "Credentials不能为空"),
    INVALID_CREDENTIALS("INVALID_CREDENTIALS", "非法的credentials"),
    ENCODE_PARAM_VALUE_MAP_ERROR("ENCODE_PARAM_VALUE_MAP_ERROR", "参数编码异常"),
    RESPONSE_VALUE_ERROR("RESPONSE_VALUE_ERROR", "返回结果异常:%s"),
    ;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String errorMessage;

    PushExceptionMessage(String errorCode, String errorMessage) {
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }

    public String errorCode() {
        return errorCode;
    }

    public String errorMessage() {
        return errorMessage;
    }
}
