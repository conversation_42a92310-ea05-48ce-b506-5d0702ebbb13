package cn.pinming.microservice.material.client.management.push;


import cn.pinming.microservice.material.client.management.push.auth.CredentialsProvider;
import lombok.Data;

/**
 * <AUTHOR>
 * 
 */
@Data
public class PushConfiguration {

    private String endpoint;

    private String requestUrl;

    private String requestHeader;

    private String requestMode;

    private CredentialsProvider credentialsProvider;

    private String responseType;

    private String responseFlag;

    @Override
    public String toString() {
        return "PushConfiguration{" +
                "endpoint='" + endpoint + '\'' +
                ", requestUrl='" + requestUrl + '\'' +
                ", requestHeader='" + requestHeader + '\'' +
                ", requestMode='" + requestMode + '\'' +
                ", credentialsProvider=" + credentialsProvider +
                ", responseType='" + responseType + '\'' +
                ", responseFlag='" + responseFlag + '\'' +
                '}';
    }
}
