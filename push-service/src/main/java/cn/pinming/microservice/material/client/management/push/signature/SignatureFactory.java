package cn.pinming.microservice.material.client.management.push.signature;


import cn.hutool.core.util.StrUtil;
import cn.pinming.microservice.material.client.management.push.exception.PushException;
import cn.pinming.microservice.material.client.management.push.exception.PushExceptionMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.util.Base64;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
public class SignatureFactory {

    public static String signatureSdkProduce(Map<String, Object> requestHeaders,  Signature signature) {
        if (StringUtils.isEmpty(signature.getSignatureTemplate())) {
            return null;
        }
        log.info("签名模版:{}", signature.getSignatureTemplate());
        String signatureStr = StrUtil.format(signature.getSignatureTemplate(), requestHeaders);
        log.info("签名字符串:{}", signatureStr);
        return hmacSHAEncrypt(signatureStr, signature.getAppSecretKey(), signature.getSignatureAlgorithm());
    }

    private static String hmacSHAEncrypt(String signatureStr, String secretKey, String signatureMethod) {
        String hash;
        try {
            Mac sha_HMAC = Mac.getInstance(signatureMethod);
            SecretKeySpec secret_key = new SecretKeySpec(secretKey.getBytes(), signatureMethod);
            sha_HMAC.init(secret_key);
            byte[] bytes = sha_HMAC.doFinal(signatureStr.getBytes());
            hash = Base64.getEncoder().encodeToString(bytes);
        } catch (Exception e) {
            log.error(signatureMethod + " encrypt error." + e.getMessage());
            throw new PushException(PushExceptionMessage.SIGNATURE_ENCRYPT_ERROR);
        }
        return hash;
    }

}
