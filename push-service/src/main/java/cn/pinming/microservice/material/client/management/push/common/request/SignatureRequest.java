package cn.pinming.microservice.material.client.management.push.common.request;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.http.Method;
import cn.pinming.microservice.material.client.management.push.PushConfiguration;
import cn.pinming.microservice.material.client.management.push.auth.Credentials;
import cn.pinming.microservice.material.client.management.push.signature.Signature;
import cn.pinming.microservice.material.client.management.push.signature.SignatureFactory;
import cn.pinming.microservice.material.client.management.push.util.UuidUtil;

import java.util.List;
import java.util.Map;
import java.util.Optional;

public class SignatureRequest extends ServiceRequest {

    /**
     * 签名映射字段
     */
    private static final String signatureMapping ="signature";

    public void buildCustomRequestHeaders(PushConfiguration pushConfiguration, List<RequestHeader> requestHeaders, Map<String, String> headers) {
        Signature signature = buildSignature(pushConfiguration);
        headers.putAll(super.getRequestHeaders(requestHeaders, signature));

        Optional<RequestHeader> signatureRequestHeader = requestHeaders.stream().filter(requestHeader -> signatureMapping.equals(requestHeader.getHeaderMapping())).findFirst();
        if (!signatureRequestHeader.isPresent()) {
            return;
        }
        String sdkSignature = buildSignature(signature, headers);
        headers.put(signatureRequestHeader.get().getHeaderName(), sdkSignature);
    }
    public Signature buildSignature(PushConfiguration pushConfiguration) {
        Signature signature = new Signature();
        signature.setEndpoint(pushConfiguration.getEndpoint());
        signature.setRequestUri(pushConfiguration.getRequestUrl());
        signature.setHttpMethod(Method.POST.name());
        signature.setTimestamp(String.valueOf(System.currentTimeMillis()));
        signature.setRequestId(UuidUtil.uuidWithoutConnector());
        if (pushConfiguration.getCredentialsProvider() != null) {
            Credentials credentials = pushConfiguration.getCredentialsProvider().getCredentials();
            signature.setAppKey(credentials.getAppKey());
            signature.setAppSecretKey(credentials.getAppSecretKey());
            signature.setSignatureTemplate(credentials.getSignatureTemplate());
            signature.setSignatureAlgorithm(credentials.getSignatureAlgorithm());
        }
        return signature;
    }

    public String buildSignature(Signature signature, Map<String, String> headers) {
        //构建签名
        Map<String, Object> signatureMap = BeanUtil.beanToMap(signature, false, false);
        signatureMap.putAll(headers);
        return SignatureFactory.signatureSdkProduce(signatureMap, signature);
    }

    public SignatureRequest(PushConfiguration pushConfiguration) {
        super(pushConfiguration);
    }
}
