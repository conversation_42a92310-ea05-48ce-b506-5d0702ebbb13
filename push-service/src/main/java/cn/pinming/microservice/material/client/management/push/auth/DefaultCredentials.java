package cn.pinming.microservice.material.client.management.push.auth;

import cn.pinming.microservice.material.client.management.push.exception.PushException;
import cn.pinming.microservice.material.client.management.push.exception.PushExceptionMessage;

/**
 * <AUTHOR>
 * 
 */
public class DefaultCredentials implements Credentials {

    private final String appKey;
    private final String appSecretKey;
    private final String signatureTemplate;
    private final String signatureAlgorithm;

    @Override
    public String getAppKey() {
        return appKey;
    }

    @Override
    public String getAppSecretKey() {
        return appSecretKey;
    }

    @Override
    public String getSignatureTemplate() {
        return signatureTemplate;
    }

    @Override
    public String getSignatureAlgorithm() {
        return signatureAlgorithm;
    }

    public DefaultCredentials(String appKey, String appSecretKey, String signatureTemplate, String signatureAlgorithm) {
        if (appKey == null || appKey.equals("")) {
            throw new PushException(PushExceptionMessage.EMPTY_APP_KEY);
        }
        this.appKey = appKey;
        this.appSecretKey = appSecretKey;
        this.signatureTemplate = signatureTemplate;
        this.signatureAlgorithm = signatureAlgorithm;
    }


}
