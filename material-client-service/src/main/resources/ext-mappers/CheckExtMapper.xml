<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.client.management.common.mapper.CheckMapper">

<select id="checkList" resultType="cn.pinming.microservice.material.client.management.common.vo.CheckVO">
    select a.no,a.push_status,a.is_verify,d.name as attributionName,a.truck_time,a.id,d.code as attributionCode,b.actual_weight as receiveAmount,
    e.name as consumeName,a.phone_sn
    from d_check a
             left join d_check_info b on b.check_id = a.id and b.deleted = 0
             left join s_device_attribution d on a.attribution_id = d.id and d.deleted = 0
            left join s_user_consumer e on e.id = a.consume_id and e.deleted = 0
            left join d_check_detail c on c.check_id = a.id and c.deleted  = 0
            left join s_rebar_material f on f.id = c.material_id and f.deleted = 0
    where a.deleted = 0
    and a.uid = #{query.uid}
    <if test="query.attributionId != null">
        and a.attribution_id = #{query.attributionId}
    </if>
    <if test="query.consumeId != null">
        and a.consume_id = #{query.consumeId}
    </if>
    <if test="query.isVerify != null">
        and a.is_verify = #{query.isVerify}
    </if>
    <if test="query.pushStatus != null">
        and a.push_status = #{query.pushStatus}
    </if>
    <if test="query.type != null">
        and f.type = #{query.type}
    </if>
    <if test="query.no != null and query.no != ''">
        and a.no like concat('%', #{query.no}, '%')
    </if>
    <if test="query.phoneSn != null and query.phoneSn != ''">
        and a.phone_sn like concat('%', #{query.phoneSn}, '%')
    </if>
    <if test="query.attributionCode != null and query.attributionCode != ''">
        and d.code like concat('%', #{query.attributionCode}, '%')
    </if>
    <if test="query.attributionName != null and query.attributionName != ''">
        and d.name like concat('%', #{query.attributionName}, '%')
    </if>
    <if test="query.consumeName != null and query.consumeName != ''">
        and e.name like concat('%', #{query.consumeName}, '%')
    </if>
    group by a.id
    order by a.gmt_create desc
</select>

<select id="checkMaterialList"
            resultType="cn.pinming.microservice.material.client.management.common.vo.CheckMaterialVO">
    select b.type,a.confirm_weight,a.send_weight,a.send_amount,b.material_name,b.material_spec,a.check_id
    from d_check_detail a
    left join s_rebar_material b on b.id = a.material_id and b.deleted = 0
    where a.deleted = 0
    and a.check_id in <foreach collection="list" item="checkId" open="(" close=")" separator=","> #{checkId} </foreach>

    </select>

</mapper>
