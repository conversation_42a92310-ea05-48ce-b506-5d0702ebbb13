<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.client.management.common.mapper.ext.UserConsumerCombineExtMapper">

<select id="getInfoByPhoneSn"
            resultType="cn.pinming.microservice.material.client.management.common.vo.ConsumerVO">
    select a.uid,b.user_name,a.consume_id,c.name as consumeName,c.attributions,b.app_key,b.app_secret_key
    from s_user_consumer_combine a
             left join s_user b on b.uid = a.uid and b.deleted = 0
             left join s_user_consumer c on c.id = a.consume_id and c.deleted = 0
    where a.phone_sn = #{phoneSn}
    and a.deleted = 0
    </select>

<select id="loginByPhoneSn"
            resultType="cn.pinming.microservice.material.client.management.common.model.UserConsumerCombineDO">
    select *
    from s_user_consumer_combine a
             left join s_user_consumer b on b.id = a.consume_id and b.deleted = 0
    where a.uid = #{uId}
    and a.phone_sn = #{phoneSn}
    and find_in_set(#{attributionId},attributions)
    and a.deleted = 0
    </select>

<select id="bindHistoryList"
            resultType="cn.pinming.microservice.material.client.management.common.model.UserConsumerCombineDO">
    select consume_id,phone_sn,gmt_create
    from s_user_consumer_combine
    where consume_id = #{id}
    order by gmt_create desc
    </select>
</mapper>
