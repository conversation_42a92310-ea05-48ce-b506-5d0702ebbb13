<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.client.management.common.mapper.ext.ReceiptModuleDetailConfigExtMapper">

<select id="recycleModuleDetailFunctionShow"
            resultType="cn.pinming.microservice.material.client.management.common.vo.ReceiptModuleDetailConfigVO">
    select a.module_detail_id,b.key_name,b.group_name,a.key_order,a.group_order,b.id
    from d_receipt_module_detail_config a
             left join s_ocr_module_detail b on b.id = a.module_detail_id and b.deleted = 0
    where a.module_id = #{moduleId}
      and a.type = #{type}
      and a.deleted = 0
      and b.id is not null
    order by a.group_order asc ,a.key_order asc
</select>
    <select id="selectRequireList" resultType="java.lang.String">
        select b.key_name
        from d_receipt_module_detail_config a
        left join s_ocr_module_detail b on a.module_detail_id = b.id
        where a.deleted = 0 and b.deleted = 0
        and a.module_id = #{templateId}
        and a.type = 2

    </select>
</mapper>
