<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.client.management.common.mapper.ext.WeighDataPicExtMapper">

    <select id="selectTodayUsedSpace" resultType="java.lang.Long">
        select ifnull(sum(size), 0)
        from d_weigh_data_pic
        where uid = #{uid}
          and date (gmt_create) = current_date ()
    </select>

    <select id="needPushWeighPic"
            resultType="cn.pinming.microservice.material.client.management.common.dto.WeighPushPicDTO">
        select a.record_id,
               a.file_path,
               a.size,
               a.type,
               a.local_c_time,
               a.local_id,
               a.uid,
               c.name,
               c.code,
               a.file_id
        from d_weigh_data_pic a
                 left join s_user b on b.uid = a.uid
                 left join s_device_attribution c on c.id = a.attribution_id
        where a.id = #{id}
    </select>

    <select id="needPushWeighPicS"
            resultType="cn.pinming.microservice.material.client.management.common.dto.WeighSimpleDTO">
        select p.id as 'pic_id', p.record_id, p.uid
        from d_weigh_data_pic p,
        d_weigh_data d
        where p.record_id = d.record_id
        and d.push_status in (3, 4)
        and p.uid = #{uid}
        and p.attribution_id in
        <foreach collection="attributionIdList" item="id" open="(" close=")" separator="," index="">#{id}</foreach>
        and (p.push_status = 1 or (p.push_status = 2 and TIMESTAMPDIFF(MINUTE, p.wait_time, now()) > 60))
        limit 100
    </select>

    <select id="getWeighPics"
            resultType="cn.pinming.microservice.material.client.management.common.dto.push.PushPicDTO">
        select record_id,file_id,size,type as picType
        from d_weigh_data_pic a
        where deleted = 0
        and record_id in
        <foreach collection="recordIds" item="id" open="(" close=")" separator="," index="">
            #{id}
        </foreach>
    </select>

    <select id="selectPicToPush"
            resultType="cn.pinming.microservice.material.client.management.common.model.WeighDataPicDO">
        select id, record_id, file_path, file_id, size, type, local_c_time, local_id, uid, attribution_id, push_status
        from d_weigh_data_pic
        where deleted = 0
        and uid = #{uid}
        and attribution_id in
        <foreach collection="attributionIdList" item="id" open="(" close=")" separator="," index="">
            #{id}
        </foreach>
        and (push_status = 1)
        and file_id is not null
        and file_id != ''
        limit 100
    </select>

    <select id="selectPicsByRecordIds"
            resultType="cn.pinming.microservice.material.client.management.common.model.WeighDataPicDO">
        select id, record_id, file_path, file_id, size, type, local_c_time, local_id, uid, attribution_id, push_status
        from d_weigh_data_pic
        where deleted = 0
        and record_id in
        <foreach collection="recordIds" item="recordId" open="(" close=")" separator="," index="">
            #{recordId}
        </foreach>
        and file_id is not null
        and file_id != ''
        order by record_id, id
    </select>
</mapper>
