<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.client.management.common.mapper.ext.PushConfigurationExtMapper">

<select id="userPushConfigurationShow"
            resultType="cn.pinming.microservice.material.client.management.common.vo.PushConfigurationVO">
    select *
    from s_push_configuration
    where find_in_set(#{uId},user_ids)
    and deleted = 0
    </select>

<select id="checkConfiguration" resultType="integer">
    select count(id)
    from s_push_configuration
    where id != #{id}
    and
    <foreach collection="attributionIds" item="attributionId" open="(" close=")" separator="or" index="">
         find_in_set(#{attributionId},attribution_ids)
    </foreach>
    and deleted = 0
</select>
</mapper>
