<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.client.management.common.mapper.CheckInfoMapper">

<select id="totalCheckVO"
            resultType="cn.pinming.microservice.material.client.management.common.vo.CheckMaterialReverseVO">
    select actual_weight,send_weight,weight_dif as weightDif,weight_rate as weightRate,weight_check_result as weightResult,reverse_weight_type,check_type,is_input
    from d_check_info
    where check_id = #{id}
    and deleted = 0
    </select>
</mapper>
