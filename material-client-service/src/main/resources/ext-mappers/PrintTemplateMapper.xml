<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.client.management.common.mapper.PrintTemplateMapper">

    <update id="updateEnableStatusById">
        update s_print_template_config
        set enabled = enabled ^ 1,
        modify_id = #{uid},
        gmt_modify = now()
        where template_id = #{id} and create_id = #{uid}
    </update>
    <select id="selectByDeviceId"
            resultType="cn.pinming.microservice.material.client.management.common.model.PrintTemplateDO">
        SELECT content, print_limit
        FROM s_print_template a left join s_print_template_config b on a.id = b.template_id and b.create_id = #{uid}
        WHERE FIND_IN_SET(#{deviceId}, attribution_id) > 0
        and type = #{type}
        and enabled = 0
        and (a.create_id = #{uid} or a.create_id is null)
        and a.deleted = 0
    </select>
    <select id="selectTemplateConfigList"
            resultType="cn.pinming.microservice.material.client.management.common.vo.print.PrintTemplateVO">
        select a.id, a.name, a.gmt_create, b.gmt_modify, if(b.enabled is null ,0,b.enabled) as enabled, b.attribution_id, if(a.create_id is null, true, false) as isSystem
        from s_print_template a
        left join s_print_template_config b on a.id = b.template_id and b.create_id = #{uid}
        where a.deleted = 0
        and (a.create_id is null
        or a.create_id = #{uid})
        and a.type = #{type}
        <if test="style != null ">
            and a.style = #{style}
        </if>
        <if test="formType != null ">
            and a.form_type = #{formType}
        </if>
        order by isSystem desc, a.gmt_create desc
    </select>
    <select id="selectAttributionList"
            resultType="cn.pinming.microservice.material.client.management.common.model.PrintTemplateConfigDO">
        select b.id, b.attribution_id
        from s_print_template a
        left join s_print_template_config b on a.id = b.template_id and b.create_id = #{uid}
        where a.deleted = 0
        and (a.create_id is null
        or a.create_id = #{uid})
        and a.type = #{type}
        and a.style = #{style}
        and a.form_type = #{formType}
        and (
            <foreach collection="deviceIdList" item="deviceId" separator="or">
                FIND_IN_SET(#{deviceId}, attribution_id) > 0
            </foreach>
        )
        and a.id != #{id}
    </select>
    <select id="selectByDeviceIdAndType"
            resultType="cn.pinming.microservice.material.client.management.common.model.PrintTemplateDO">
        SELECT content, print_limit,a.name,a.style,a.type,a.form_type
        FROM s_print_template a left join s_print_template_config b on a.id = b.template_id and b.create_id = #{uid}
        WHERE FIND_IN_SET(#{deviceId}, attribution_id) > 0
        and type = #{type}
        and form_type = #{formType}
        and enabled = 0
        and (a.create_id = #{uid} or a.create_id is null)
        and a.deleted = 0
    </select>
    <select id="selectByDeviceIdAndStyle"
            resultType="cn.pinming.microservice.material.client.management.common.model.PrintTemplateDO">
        SELECT content, print_limit,a.name,a.style,a.type,a.form_type
        FROM s_print_template a left join s_print_template_config b on a.id = b.template_id and b.create_id = #{uid}
        WHERE FIND_IN_SET(#{deviceId}, attribution_id) > 0
        and type = #{type}
        <if test="style != null ">
            and style = #{style}
        </if>
        <if test="formType != null ">
            and form_type = #{formType}
        </if>
        and enabled = 0
        and (a.create_id = #{uid} or a.create_id is null)
        and a.deleted = 0
    </select>
</mapper>
