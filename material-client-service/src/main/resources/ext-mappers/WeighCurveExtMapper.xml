<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.client.management.common.mapper.ext.WeighCurveExtMapper">

    <select id="selectCurveToPush"
            resultType="cn.pinming.microservice.material.client.management.common.dto.push.PushCurveDataDTO">
        select a.*, b.primary_code as attributionCode
        from d_weigh_curve a
        left join s_device_attribution b on a.attribution_id = b.id
        where a.uid = #{uid}
        and a.attribution_id in
        <foreach collection="attributionList" item="id" open="(" close=")" separator="," index="">
            #{id}
        </foreach>
        and a.deleted = 0
        and (a.push_state = 1 or a.push_state = 3 or a.push_state is null)
        order by a.create_time desc
        limit 20
    </select>

</mapper>
