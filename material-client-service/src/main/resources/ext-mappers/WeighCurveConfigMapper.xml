<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.client.management.common.mapper.WeighCurveConfigMapper">

    <select id="getWeighCurveConfig"
            resultType="cn.pinming.microservice.material.client.management.common.model.WeighCurveConfigDO">
        select a.*
        from d_weigh_curve_config a
        left join s_device b on a.device_id = b.id
        left join s_device_binding c on c.device_id = b.id
        where a.device_id = #{deviceId} and a.attribution_id = #{attributionId}
        and a.device_binding_id = #{deviceBindingId}
        and a.deleted = 0
        and c.deleted = 0
    </select>
</mapper>
