<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.client.management.common.mapper.DeliveryDetailMapper">

    <select id="selectMaterialByIds"
            resultType="cn.pinming.microservice.material.client.management.common.dto.DeliveryDetailSimpleDTO">
        select a.id, concat( b.name , '/' , b.spec) as material
        from d_delivery_detail a
        left join d_purchase_order_detail b on b.id = a.purchase_order_detail_id
        where a.deleted = 0
        and a.id in
        <foreach collection="list" item="id" open="(" close=")" separator="," index="">
            #{id}
        </foreach>
    </select>
    <select id="selectListByDeliveryId"
            resultType="cn.pinming.microservice.material.client.management.common.vo.delivery.DeliveryItemDTO">
        select
        a.id,
        a.amount as currentAmount,
        a.status,
        b.name,
        b.spec,
        b.unit,
        b.brand,
        b.remark,
        b.position,
        b.argument
        from d_delivery_detail a
        left join d_purchase_order_detail b on a.purchase_order_detail_id = b.id
        where delivery_id = #{deliveryId}
        and a.deleted = 0
    </select>
    <select id="selectCargoListByDeliveryId"
            resultType="cn.pinming.microservice.material.client.management.common.vo.selfcheck.CargoVO">
        select
        a.id,
        b.ext_id as detailId,
        delivery_id,
        b.name,
        b.spec,
        b.argument,
        b.unit,
        b.brand,
        a.amount,
        b.remark,
        b.position,
        b.deduct_ratio,
        b.deduct_weight,
        b.scale_factor,
        b.unit_type,
        a.status
        from d_delivery_detail a
        left join d_purchase_order_detail b on a.purchase_order_detail_id = b.id
        where delivery_id in
        <foreach collection="list" item="id" open="(" close=")" separator="," index="">
            #{id}
        </foreach>
        and a.deleted = 0
    </select>

    <select id="selectInfoForConfirmPush"
            resultType="cn.pinming.microservice.material.client.management.common.dto.push.PushConfirmMaterialDTO">
        select a.id,a.amount as waybillCounts,b.ext_id as cargoId,b.name,b.spec,b.brand,b.amount,b.deduct_ratio,b.scale_factor,b.unit_type,b.unit as waybillUnit,b.remark,c.source
        from d_delivery_detail a
                 left join d_purchase_order_detail b on b.id = a.purchase_order_detail_id
        left join d_purchase_order c on c.id = b.order_id
        where a.deleted = 0
        and a.id in
        <foreach collection="list" item="id" open="(" close=")" separator="," index="">
            #{id}
        </foreach>
    </select>
</mapper>
