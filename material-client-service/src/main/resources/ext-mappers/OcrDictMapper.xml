<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.client.management.common.mapper.OcrDictMapper">

    <select id="pageByQuery"
            resultType="cn.pinming.microservice.material.client.management.common.vo.OCRDictVO">
        select id,name,value from s_ocr_dict where deleted = 0 order by gmt_create desc
    </select>
</mapper>
