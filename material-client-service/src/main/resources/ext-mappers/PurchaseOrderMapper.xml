<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.client.management.common.mapper.PurchaseOrderMapper">

    <select id="pageByQuery"
            resultType="cn.pinming.microservice.material.client.management.common.vo.purchase.PurchaseVO">

        select a.id,
        order_ext_id,
        a.project,
        c.name as supplierName,
        b.user_name as accountName,
        a.sync_time as gmtModify,
        group_concat(distinct d.name,'/', d.spec SEPARATOR ',') as materialCategory,
        a.status,
        a.delivery_status,
        a.position
        from d_purchase_order a
        left join s_user b on a.supplier_id = b.uid
        left join s_supplier_config c on c.supplier_ext_id = a.supplier_ext_id
        left join d_purchase_order_detail d on a.id = d.order_id
        where a.deleted = 0 and (a.create_id = #{uid} or a.supplier_id = #{uid})
        <if test="orderExtId!= null and orderExtId!= ''">
            and order_ext_id like concat('%', #{orderExtId}, '%')
        </if>
        <if test="project!= null and project!= ''">
            and a.project like concat('%', #{project}, '%')
        </if>
        <if test="supplierName!= null and supplierName!= ''">
            and c.name like concat('%', #{supplierName}, '%')
        </if>
        <if test="accountName!= null and accountName!= ''">
            and b.user_name like concat('%', #{accountName}, '%')
        </if>
        <if test="status != null and status.size > 0">
            AND a.status in
            <foreach collection="status" item="item" open="(" close=")" separator="," index="">
                #{item}
            </foreach>
        </if>
        <if test="deliveryStatus != null and deliveryStatus.size > 0">
            AND a.delivery_status in
            <foreach collection="deliveryStatus" item="item" open="(" close=")" separator="," index="">
                #{item}
            </foreach>
        </if>
        group by id
        <if test="materialCategory!= null and materialCategory!= ''">
            having materialCategory like concat('%', #{materialCategory}, '%')
        </if>
        order by a.sync_time desc
    </select>
    <select id="selectDetailById"
            resultType="cn.pinming.microservice.material.client.management.common.vo.purchase.PurchaseDetailVO">
        select a.id,
        order_ext_id,
        c.name      as supplierName,
        b.user_name as accountName,
        a.project,
        a.supplier_ext_id,
        a.position,
        a.address,
        a.receiver,
        a.mobile,
        a.receive_date,
        a.remark,
        a.delivery_status
        from d_purchase_order a
        left join s_user b on a.supplier_id = b.uid
        left join s_supplier_config c on c.supplier_ext_id = a.supplier_ext_id and c.deleted = 0 and c.create_id = b.uid
        where a.deleted = 0
        and a.id = #{id}
    </select>
    <select id="selectDetailListById"
            resultType="cn.pinming.microservice.material.client.management.common.vo.purchase.PurchaseItemVO">
        select name,
        spec,
        argument,
        brand,
        a.amount,
        unit,
        deduct_ratio,
        sum(b.amount)        as sendAmount,
        sum(c.actual_count)  as actualAmount,
        sum(c.weight_actual) as weightAmount,
        weight_unit,
        unit_type,
        a.position,
        a.remark
        from d_purchase_order x
        left join d_purchase_order_detail a on x.id = a.order_id
        left join d_delivery_detail b on a.id = b.purchase_order_detail_id and b.deleted = 0
        left join d_weigh_data_confirm c on c.delivery_detail_ids = b.id and c.deleted = 0
        where x.id = #{id}
        and (x.create_id = #{uid} or x.supplier_id = #{uid})
        and a.deleted = 0
        group by a.id
    </select>
    <select id="selectH5PurchaseOrderList"
            resultType="cn.pinming.microservice.material.client.management.common.vo.h5.SimplePurchaseVO">
        select a.id, receive_date, user_name as owner, a.remark,a.position
        from d_purchase_order a
        left join s_user b on a.create_id = b.uid
        where a.deleted = 0
        and status != 2
        and delivery_status != 3
        and a.supplier_ext_id = #{supplierExtId}
        and a.attribution_id = #{attributionId}
        order by receive_date desc
    </select>
    <select id="selectH5PurchaseOrder"
            resultType="cn.pinming.microservice.material.client.management.common.vo.h5.SimplePurchaseVO">
        select a.id, receive_date, user_name as owner, a.remark
        from d_purchase_order a
        left join s_user b on a.create_id = b.uid
        where a.deleted = 0
        and status != 2
        and delivery_status != 3
        and a.attribution_id = #{attributionId}
        and a.id = #{purchaseId}
    </select>

    <select id="selectPurchaseSync"
            resultType="cn.pinming.microservice.material.client.management.common.dto.push.PurchaseSyncDTO">
        select a.*,b.primary_code
        from d_purchase_order a
        left join s_device_attribution b on b.id = a.attribution_id and b.deleted = 0
        where a.create_id = #{uid}
        and a.deleted = 0
        <if test="list != null and list.size > 0">
            AND a.attribution_id not in
            <foreach collection="list" item="item" open="(" close=")" separator="," index="">
                #{item}
            </foreach>
        </if>
        and a.push_status = 1
        and a.order_ext_id is not null
    </select>
</mapper>
