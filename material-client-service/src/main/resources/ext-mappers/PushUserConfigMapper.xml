<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.client.management.common.mapper.PushUserConfigMapper">

    <select id="selectOpenedRouteConfigList"
            resultType="cn.pinming.microservice.material.client.management.common.vo.push.PushRouteConfigVO">
        select b.gateway, a.method, a.end_point, a.description, b.exclude_id, b.create_id as uid
        from d_push_route_config a
                 left join d_push_user_config b on a.id = b.route_config_id and b.is_open = 2
        where a.id = #{routeConfigId}
        and a.deleted = 0
        and b.deleted = 0
        and b.is_open = 2
        and b.gateway is not null
    </select>
</mapper>
