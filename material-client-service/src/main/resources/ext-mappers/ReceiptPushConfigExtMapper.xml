<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.client.management.common.mapper.ext.ReceiptPushConfigExtMapper">


    <select id="findSdkPushConfig"
            resultType="cn.pinming.microservice.material.client.management.common.model.ReceiptPushConfigDO">
        select bc.*
        from d_receipt_push_config bc,
             s_developer d
        where d.create_id = bc.uid
          and d.app_id = 8
          and d.type = 0
          and bc.push_type = 1
          and bc.push_status = 2
          and bc.push_url is not null
    </select>

    <select id="findReceiptPushConfig"
            resultType="cn.pinming.microservice.material.client.management.common.model.ReceiptPushConfigDO">
        select bc.*
        from d_receipt_push_config bc,
             s_developer d
        where d.create_id = bc.uid
          and d.app_id = 8
          and d.type = 0
          and bc.uid = #{uid}
          limit 1
    </select>
</mapper>
