<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.client.management.common.mapper.ext.UserBusinessConfigExtMapper">


    <select id="findSdkPushConfig"
            resultType="cn.pinming.microservice.material.client.management.common.model.UserBusinessConfigDO">
        select bc.*
        from s_user_business_config bc,
             s_developer d
        where d.create_id = bc.uid
          and d.app_id = 4
          and d.type = 0
          and bc.push_type = 1
          and bc.weigh_data_push_status = 2
          and bc.weigh_data_push_url is not null
    </select>

    <select id="findUserBusinessConfig"
            resultType="cn.pinming.microservice.material.client.management.common.model.UserBusinessConfigDO">
        select bc.*
        from s_user_business_config bc,
             s_developer d
        where d.create_id = bc.uid
          and d.app_id = 4
          and d.type = 0
          and bc.uid = #{uid}
          limit 1
    </select>
</mapper>
