<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.client.management.common.mapper.ext.DeviceExtMapper">

    <select id="occupiedCheck" resultType="java.lang.String">
        select c.user_name
        from s_device a
        left join s_device_binding b on b.device_id = a.id and b.deleted = 0
        left join s_user c on c.uid = b.uid and c.deleted = 0
        where a.device_sn = #{deviceSn} and a.device_type = #{deviceType}
        and a.deleted = 0
    </select>

    <select id="selectInfoByDeviceSn" resultType="cn.pinming.microservice.material.client.management.common.dto.WeighDataCheckDTO">
        select a.is_used,
        a.id as deviceId,
        b.id as deviceBindingId,
        b.uid,
        b.attribution_id,
        b.auxiliary_code,
        b.receive,
        d.type,
        e.space_expire,
        e.space_size,
        e.space_use_size,
        e.api_total,
        e.api_use_total
        from s_device a
        left join s_device_binding b on b.device_id = a.id and b.deleted = 0
        left join s_developer d on d.create_id = b.uid and d.app_id = #{appId}
        left join s_user_config e on e.uid = d.create_id
        where a.device_sn = #{deviceSn}
        <if test="deviceType != null and deviceType != ''">
            and a.device_type = #{deviceType}
        </if>
        <if test="deviceType == null">
            and a.device_type = 'WEIGH'
        </if>
        and a.deleted = 0
    </select>

    <select id="syncInfo" resultType="cn.pinming.microservice.material.client.management.common.vo.DataSyncVO">
        select n.user_name,n.logo_pic as userPic,d.name as attributionName,m.password,d.logo_pic as attributionPic,d.is_name,d.is_logo
        from s_device a
                 left join s_device_binding m on m.device_id = a.id and m.deleted = 0
                 left join s_user n on n.uid = m.uid and n.deleted = 0
                 left join s_device_attribution d on d.id = m.attribution_id and d.deleted = 0
        where a.deleted = 0
        and m.attribution_id is not null
        and a.device_sn = #{deviceSn}
        <if test="deviceType != null and deviceType != ''">
            and a.device_type = #{deviceType}
        </if>
        <if test="deviceType == null">
            and a.device_type = 'WEIGH'
        </if>
    </select>

    <select id="selectPassDeviceList"
            resultType="cn.pinming.microservice.material.client.management.common.vo.DeviceVO">
        select b.device_sn, a.id,a.device_id,b.device_type
        from s_device_binding a
                 left join s_device b on b.id = a.device_id and b.deleted = 0
        where a.deleted = 0
          and a.attribution_id is null
          and a.uid = #{uId}
    </select>

    <select id="selectListByDeviceType"
            resultType="cn.pinming.microservice.material.client.management.common.vo.DeviceVO">
        select a.device_sn,a.device_type,c.user_name,d.name as attributionName,a.gmt_create,a.is_used,a.id,a.id as deviceId,a.source,a.name,
        e.user_name as createName, a.expire_date, a.remark
        from s_device a
        left join s_device_binding b on b.device_id = a.id and b.deleted = 0
        left join s_user c on c.uid = b.uid and c.deleted = 0
        left join s_device_attribution d on d.id = b.attribution_id and d.deleted = 0
        left join s_user e on a.create_id = e.uid
        where a.deleted = 0
        <if test="deviceType != null and deviceType != ''">
            and a.device_type = #{deviceType}
        </if>
    </select>
</mapper>
