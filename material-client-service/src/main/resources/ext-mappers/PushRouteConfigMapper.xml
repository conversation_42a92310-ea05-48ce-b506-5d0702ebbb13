<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.client.management.common.mapper.PushRouteConfigMapper">

    <select id="selectRouteList"
            resultType="cn.pinming.microservice.material.client.management.common.vo.push.PushRouteConfigVO">
        select
        a.id,
        a.end_point,
        b.gateway,
        a.method,
        a.description,
        ifnull(b.is_open,1) as is_open,
        b.exclude_id
        from d_push_route_config a
        left join d_push_user_config b on a.id = b.route_config_id and b.create_id = #{uid}
        where a.deleted = 0
        order by a.id
    </select>
</mapper>
