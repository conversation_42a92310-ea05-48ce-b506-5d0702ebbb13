<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.client.management.common.mapper.ClientMapper">

    <select id="selectPageList"
            resultType="cn.pinming.microservice.material.client.management.common.vo.ClientVO">
        select a.id, file_id, version, user_name, a.gmt_modify, a.type, a.remark
        from s_client a
        left join s_user b on a.modify_id = b.uid
        where a.deleted = 0
        order by a.gmt_create desc
    </select>
</mapper>
