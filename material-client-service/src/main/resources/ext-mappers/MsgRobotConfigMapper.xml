<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.client.management.common.mapper.MsgRobotConfigMapper">

    <select id="selectPageByQuery"
            resultType="cn.pinming.microservice.material.client.management.common.vo.MsgRobotVO">
        select
        *
        from s_msg_robot_config
        where deleted = 0
        and create_id = #{query.uid}
        <if test="query.name!= null and query.name!= ''">
            and name like concat('%',#{query.name},'%')
        </if>
        <if test="query.type!= null">
            and type = #{query.type}
        </if>
    </select>
    <select id="selectCountByMsgRobotId" resultType="java.lang.Integer">
        SELECT count(*)
        FROM s_device_binding
        WHERE FIND_IN_SET(#{id}, msg_robot)
    </select>
</mapper>
