<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.client.management.common.mapper.ext.WeighDataExtMapper">

    <select id="list" resultType="cn.pinming.microservice.material.client.management.common.model.ext.WeighDataExtDO">
        select * from d_weigh_data
    </select>

    <sql id="queryWeighDataSql">
        select a.id, a.record_id,a.attribution_id,a.device_sn,a.auxiliary_code,a.truck_no,a.material,a.weight,a.unit,a.type,DATE_FORMAT(a.weigh_time, '%Y-%m-%d') weighDate,
               DATE_FORMAT(a.weigh_time, '%H:%i:%s') weighTime,b.name,b.code,DATE_FORMAT(a.gmt_create, '%Y-%m-%d %H:%i:%s') gmt_create,a.lpr_truck_no, a.risk_grade,a.push_status
        from d_weigh_data a
                 left join s_device_attribution b on b.id = a.attribution_id and b.deleted = 0
                 left join s_user c on c.uid = b.uid and c.deleted = 0
    </sql>

    <select id="getByRecordId"
            resultType="cn.pinming.microservice.material.client.management.common.vo.WeighDataDetailVO">
        <include refid="queryWeighDataSql"/>
        where a.deleted = 0
        AND a.record_id=#{recordId}
    </select>

    <select id="listByRecordId"
            resultType="cn.pinming.microservice.material.client.management.common.vo.WeighDataDetailVO">
        <include refid="queryWeighDataSql"/>
        where a.deleted = 0
        AND a.record_id in <foreach collection="recordIdList" item="recordId" open="(" close=")" separator="," index="">#{recordId}</foreach>
    </select>

    <select id="select" resultType="cn.pinming.microservice.material.client.management.common.vo.WeighDataVO">
       <include refid="queryWeighDataSql"/>
        where a.uid = #{uId}
        and a.deleted = 0
        <if test="query.recordId != null and query.recordId != ''">
            AND a.record_id like CONCAT('%',#{query.recordId},'%')
        </if>
        <if test="query.deviceSn != null and query.deviceSn != ''">
            AND a.device_sn like CONCAT('%',#{query.deviceSn},'%')
        </if>
        <if test="query.truckNo != null and query.truckNo != ''">
            AND (a.truck_no like CONCAT('%',#{query.truckNo},'%')
            or a.material like CONCAT('%',#{query.truckNo},'%'))
        </if>
        <if test="query.lprTruckNo != null and query.lprTruckNo != ''">
            AND a.lpr_truck_no like CONCAT('%',#{query.lprTruckNo},'%')
        </if>
        <if test="query.type != null and query.type.size > 0">
            AND a.type in
            <foreach collection="query.type" item="item" open="(" close=")" separator="," index="">
                #{item}
            </foreach>
        </if>
        <if test="query.idList != null and query.idList.size > 0">
            AND a.id in
            <foreach collection="query.idList" item="item" open="(" close=")" separator="," index="">
                #{item}
            </foreach>
        </if>
        <if test="query.gmtCreate != null and query.gmtCreate != ''">
            AND a.gmt_create = #{query.gmtCreate}
        </if>
        <if test="query.name != null and query.name != ''">
            AND b.name like CONCAT('%',#{query.name},'%')
        </if>
        <if test="query.code != null and query.code != ''">
            AND b.code like CONCAT('%',#{query.code},'%')
        </if>
        <if test="query.startDate != null and query.startDate != ''">
            AND a.weigh_time BETWEEN CONCAT(#{query.startDate},' 00:00:00') AND CONCAT(#{query.endDate},' 23:59:59')
        </if>
        <if test="query.time != null and query.time != ''">
            AND a.weigh_time like CONCAT('%',#{query.time},'%')
        </if>
        <if test="query.riskGrade != null and query.riskGrade.size > 0">
            AND a.risk_grade in
            <foreach collection="query.riskGrade" item="item" open="(" close=")" separator="," index="">
                #{item}
            </foreach>
        </if>
        order by a.gmt_create desc
    </select>

    <select id="card" resultType="cn.pinming.microservice.material.client.management.common.vo.WeighDataVO">
        select a.record_id,a.attribution_id,a.device_sn,a.truck_no,a.material,a.weight,a.unit,a.type,a.weigh_time
             ,b.name,b.code,a.gmt_create
        from d_weigh_data a
                 left join s_device_attribution b on b.id = a.attribution_id and b.deleted = 0
                 left join s_user c on c.uid = b.uid and c.deleted = 0
        where a.record_id = #{weighDataId}
          and a.deleted = 0
    </select>

    <select id="needPushWeighData"
            resultType="cn.pinming.microservice.material.client.management.common.dto.WeighPushDataDTO">
        select a.record_id,c.name,c.code,a.device_sn,a.truck_no,a.material,a.weight,a.unit,a.type,a.weigh_time,a.uid
        from d_weigh_data a
        left join s_user b on b.uid = a.uid
        left join s_device_attribution c on c.id = a.attribution_id
        where a.record_id = #{recordId}
    </select>

    <select id="needPushWeighDataS"
            resultType="cn.pinming.microservice.material.client.management.common.dto.WeighSimpleDTO">
        select record_id,uid
        from d_weigh_data
        where deleted = 0
        and uid = #{uid}
        and attribution_id in
        <foreach collection="attributionList" item="id" open="(" close=")" separator="," index="">
            #{id}
        </foreach>
        and (push_status = 1 or (push_status = 2 and TIMESTAMPDIFF(MINUTE ,wait_time, now()) > 60))
    </select>

    <select id="getPushDatas"
            resultType="cn.pinming.microservice.material.client.management.common.dto.push.PushDTO">
        select a.record_id,c.user_name,b.code as attributionCode,a.device_sn,a.auxiliary_code,a.truck_no,a.material
             ,a.weight,a.unit,a.type as weighType,a.risk_grade,a.weigh_time,a.lpr_truck_no,c.uid
        from d_weigh_data a
                 left join s_device_attribution b on b.id = a.attribution_id and b.deleted = 0
                 left join s_user c on c.uid = b.uid and c.deleted = 0
        where a.deleted = 0
          and a.attribution_id in
        <foreach collection="attributionList" item="id" open="(" close=")" separator="," index="">
            #{id}
        </foreach>
        and a.gmt_create &lt; date_sub(curdate(),INTERVAL 5 MINUTE)
        and a.push_status = 1
    </select>
    <select id="countByDeviceSn" resultType="java.lang.Long">
        select count(1) from d_weigh_data where deleted = 0
        and attribution_id = #{attributionId}
        and device_sn = #{deviceSn}
    </select>

    <select id="expirePicFix"
            resultType="cn.pinming.microservice.material.client.management.common.dto.ExpirePicFixDTO">
        select a.record_id,a.weigh_time,b.file_id
        from d_weigh_data a
        left join d_weigh_data_pic b on a.record_id = b.record_id and b.deleted = 0
        where a.record_id in
        <foreach collection="list" item="id" open="(" close=")" separator="," index="">
            #{id}
        </foreach>
    </select>

    <select id="dataQuery"
            resultType="cn.pinming.microservice.material.client.management.common.vo.WeighDataConfirmDetailDTO">
        select a.record_id as localId,a.truck_no,a.weight as weightActual,a.weigh_time as localCreateTime,a.device_sn
        ,b.name as attributionName,b.code as attributionCode
        from d_weigh_data a
        left join s_device_attribution b on b.id = a.attribution_id
        where a.deleted = 0
        and a.uid = #{uid}
        <if test="query.truckNos != null and query.truckNos.size > 0">
            and a.truck_no in
            <foreach collection="query.truckNos" item="truckNo" open="(" close=")" separator="," index="">
                #{truckNo}
            </foreach>
        </if>
        <if test="query.startTime != null">
            and a.weigh_time &gt; #{query.startTime}
        </if>
        <if test="query.endTime != null">
            and a.weigh_time &lt; #{query.endTime}
        </if>
        <if test="query.truckNo != null and query.truckNo != ''">
            and a.truck_no like concat('%', #{query.truckNo}, '%')
        </if>
        <if test="query.attributionCode != null and query.attributionCode != ''">
            and FIND_IN_SET(#{query.attributionCode}, b.code)
        </if>
        order by a.weigh_time desc
    </select>

    <select id="selectDataToPush"
            resultType="cn.pinming.microservice.material.client.management.common.dto.push.PushWeighDataDTO">
        select a.*,b.primary_code as attributionCode
        from d_weigh_data a
        left join s_device_attribution b on a.attribution_id = b.id
        where a.uid = #{uid}
        and a.attribution_id in
        <foreach collection="attributionList" item="id" open="(" close=")" separator="," index="">
            #{id}
        </foreach>
        and a.deleted = 0
        and a.push_status = 1
        order by a.weigh_time desc
<!--        limit 20-->
    </select>

    <select id="getDataPull"
            resultType="cn.pinming.microservice.material.client.management.common.dto.WeighDataPullDTO">
        select a.*,b.primary_code as attributionCode
        from d_weigh_data a
        left join s_device_attribution b on a.attribution_id = b.id
        where a.uid = #{query.uid}
        and a.deleted = 0
        <if test="query.truckNos != null and query.truckNos.size > 0">
            and a.truck_no in
            <foreach collection="query.truckNos" item="truckNo" open="(" close=")" separator="," index="">
                #{truckNo}
            </foreach>
        </if>
        <if test="query.truckNo != null and query.truckNo != ''">
            and a.truck_no like concat('%', #{query.truckNo}, '%')
        </if>
        <if test="query.startTime != null">
            and a.weigh_time &gt; #{query.startTime}
        </if>
        <if test="query.endTime != null">
            and a.weigh_time &lt; #{query.endTime}
        </if>
        <if test="query.attributionCode != null and query.attributionCode != ''">
            and FIND_IN_SET(#{query.attributionCode}, b.code)
        </if>
        <if test="query.deviceSn != null and query.deviceSn != ''">
            AND a.device_sn = #{query.deviceSn}
        </if>
        <if test="query.pushStatus != null">
            AND a.push_status = #{query.pushStatus}
        </if>
        order by a.weigh_time desc
    </select>
</mapper>
