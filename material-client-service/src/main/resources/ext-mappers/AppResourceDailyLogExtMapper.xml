<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.client.management.common.mapper.AppResourceDailyLogMapper">

    <select id="selectUserUsedSpaceList"
            resultType="cn.pinming.microservice.material.client.management.common.dto.AppDailyLogDTO">
        select a.id,
        a.uid as create_id,
        a.space_size,
        a.space_expire,
        ifnull(b.used_space,0) as used_space
        from s_user_config a
        left join (select create_id, sum(used_space) as used_space  from s_app_resource_daily_log group by create_id) b
        on a.uid = b.create_id
    </select>
</mapper>
