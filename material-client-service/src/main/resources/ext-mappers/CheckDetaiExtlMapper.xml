<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.client.management.common.mapper.ext.CheckDetailExtMapper">

<select id="theoryCheckVO"
            resultType="cn.pinming.microservice.material.client.management.common.vo.CheckMaterialReverseVO">
    select a.send_amount,a.theory_amount,a.theory_amount_dif as amountDif,a.theory_amount_result as amountResult,b.material_spec,b.material_name
        ,a.send_weight,a.theory_weight,a.theory_weight_dif as weightDif,a.theory_weight_result as weightResult
    from d_check_detail a
    left join s_rebar_material b on b.id = a.material_id and b.deleted = 0
    where a.check_id = #{id}
      and a.deleted = 0
    </select>

<select id="reverseCheckVO"
            resultType="cn.pinming.microservice.material.client.management.common.vo.CheckMaterialReverseVO">
    select a.send_amount,a.reverse_theory_amount,a.reverse_theory_amount_dif as amountDif,a.reverse_theory_amount_result as amountResult,b.material_spec,b.material_name
        ,a.send_weight,a.reverse_theory_weight,a.reverse_theory_weight_dif as weightDif,a.reverse_theory_weight_result as weightResult
    from d_check_detail a
             left join s_rebar_material b on b.id = a.material_id and b.deleted = 0
    where a.check_id = #{id}
      and a.deleted = 0
    </select>

<select id="confirmCheckVO"
            resultType="cn.pinming.microservice.material.client.management.common.vo.CheckMaterialConfirmVO">
    select a.length,a.send_weight,a.send_amount,a.actual_amount,a.reverse_theory_weight,a.confirm_amount,a.confirm_weight,b.material_spec,b.material_name
        ,a.actual_amount,a.reverse_theory_amount,a.id,a.material_id,b.type
    from d_check_detail a
             left join s_rebar_material b on b.id = a.material_id and b.deleted = 0
    where a.check_id = #{id}
    and a.deleted = 0
    </select>
</mapper>
