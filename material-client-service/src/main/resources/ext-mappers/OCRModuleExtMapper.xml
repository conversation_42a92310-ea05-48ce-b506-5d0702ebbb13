<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.client.management.common.mapper.ext.OCRModuleExtMapper">

<select id="selectList"
            resultType="cn.pinming.microservice.material.client.management.common.model.OcrModuleDO">
    select *
    from s_ocr_module
    where deleted = 0
    and uid = #{query.uid}
    <if test="query.name != null and query.name != ''">
        AND name like CONCAT('%',#{query.name},'%')
    </if>
    <if test="query.type != null and query.type.size > 0">
        AND type in
        <foreach collection="query.type" item="item" open="(" close=")" separator="," index="">
            #{item}
        </foreach>
    </if>
    <if test="query.type == null ">
        AND 1 = 0
    </if>
    <if test="query.isEnable != null and query.isEnable != ''">
        AND is_enable = #{query.isEnable}
    </if>
    <if test="query.attributionId != null ">
        and find_in_set(#{query.attributionId},attribution_ids)
    </if>
    <if test="query.billMatchType != null ">
        and bill_match_type = #{query.billMatchType}
    </if>

    union all

    select *
    from s_ocr_module
    where deleted = 0
    <if test="query.name != null and query.name != ''">
        AND name like CONCAT('%',#{query.name},'%')
    </if>
    <if test="query.clientType != null and query.clientType.size > 0">
        AND type in
        <foreach collection="query.clientType" item="item" open="(" close=")" separator="," index="">
            #{item}
        </foreach>
    </if>
    <if test="query.clientType == null ">
        and 1 = 0
    </if>
    <if test="query.isEnable != null and query.isEnable != ''">
        AND is_enable = #{query.isEnable}
    </if>
    <if test="query.billMatchType != null ">
        and bill_match_type = #{query.billMatchType}
    </if>
    order by gmt_create desc
</select>

<select id="selectModuleList"
            resultType="cn.pinming.microservice.material.client.management.common.vo.ocr.OCRConvertVO">
        select id,ext_id
        from s_ocr_module
        where uid = #{uid}
        and bill_match_type = #{billMatchType}
        and find_in_set(#{attributionId},attribution_ids)
        and deleted = 0
        and is_enable = 1

    </select>

<select id="recycleModuleDetailShow"
            resultType="cn.pinming.microservice.material.client.management.common.vo.ReceiptModuleDetailConfigVO">
    select b.group_name,b.key_name,b.group_id,b.id as moduleDetailId,c.group_order,c.key_order,c.type as recycleDetailType,b.type
    from s_ocr_module a
             left join s_ocr_module_detail b on b.module_id = a.id and b.deleted = 0
             left join d_receipt_module_detail_config c on c.module_detail_id = b.id and c.deleted = 0 and c.type = #{type}
    where a.id = #{moduleId}
    and a.deleted = 0
    order by c.key_order is null,c.group_order asc,c.key_order asc
</select>

    <select id="checkMainPoint" resultType="java.lang.Integer">
        select count(*)
        from s_ocr_module a
        left join s_ocr_module_detail b on a.id = b.module_id
        where a.deleted = 0
        and b.deleted
        and b.type = 1
        and b.key_name = #{mainPointKeyName}
        and a.uid = #{uid}
        <if test="id != null">
            and a.id != #{id}
        </if>
    </select>
</mapper>
