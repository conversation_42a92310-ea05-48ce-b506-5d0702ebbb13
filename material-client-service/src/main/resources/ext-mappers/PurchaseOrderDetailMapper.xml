<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.client.management.common.mapper.PurchaseOrderDetailMapper">

    <select id="selectPurchaseDetailSync"
            resultType="cn.pinming.microservice.material.client.management.common.dto.push.PurchaseDetailSyncDTO">
        select a.*
        from d_purchase_order_detail a
        where a.deleted = 0
        and a.order_id in
        <foreach collection="list" item="id" open="(" close=")" separator="," index="">
            #{id}
        </foreach>
        and a.deleted = 0
    </select>
</mapper>
