<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.client.management.common.mapper.ext.ReceiptRecycleExtMapper">


    <select id="pageList"
            resultType="cn.pinming.microservice.material.client.management.common.vo.ReceiptRecycleVO"
            parameterType="cn.pinming.microservice.material.client.management.common.query.ReceiptRecycleQuery">
        select r.*, a.name 'attribution_name', a.code 'attribution_code',b.name as batchName,
        b.id as batchId,
        b.status as isBatchArchive
        from d_receipt_recycle r
        left join s_device_attribution a on r.attribution_id = a.id and a.deleted = 0
        left join d_receipt_recycle_batch b on r.batch_id = b.id
        where r.deleted = 0 and r.uid = #{query.uid}
        <if test="query.id != null">
            and r.id = #{query.id}
        </if>
        <if test="query.batchId != null">
            and r.batch_id = #{query.batchId}
        </if>
        <if test="query.deviceSn != null and query.deviceSn != ''">
            and a.device_sn like '%${query.deviceSn}%'
        </if>
        <if test="query.attributionName != null and query.attributionName != ''">
            and a.name like '%${query.attributionName}%'
        </if>
        <if test="query.attributionCode != null and query.attributionCode != ''">
            and a.code like '%${query.attributionCode}%'
        </if>
        <if test="query.recycleSource != null and query.recycleSource.size > 0">
            and r.recycle_source in
            <foreach collection="query.recycleSource" item="item" open="(" close=")" separator="," index="">#{item}
            </foreach>
        </if>
        <if test="query.recycleStatus != null and query.recycleStatus.size > 0">
            and r.recycle_status in
            <foreach collection="query.recycleStatus" item="item" open="(" close=")" separator="," index="">#{item}
            </foreach>
        </if>
        <if test="query.recycleTimeStart != null and query.recycleTimeEnd != null">
            and r.recycle_time between #{query.recycleTimeStart} and #{query.recycleTimeEnd}
        </if>
        <if test="query.gmtCreateStart != null and query.gmtCreateEnd != null">
            and r.gmt_create between #{query.gmtCreateStart} and #{query.gmtCreateEnd}
        </if>
        <if test="query.receiptFailTypes != null and query.receiptFailTypes.size > 0">
            and
            <foreach collection="query.receiptFailTypes" item="receiptFailType" open="(" close=")" separator="or"
                     index="">find_in_set(#{receiptFailType}, r.receipt_fail_types)
            </foreach>
        </if>
        order by r.gmt_create desc
    </select>
    <select id="resultList"
            resultType="cn.pinming.microservice.material.client.management.common.vo.ReceiptRecycleResultVO"
            parameterType="cn.pinming.microservice.material.client.management.common.query.ReceiptRecycleResultQuery">
        select r.*, a.name 'attribution_name', a.code 'attribution_code'
        from d_receipt_recycle r
        left join s_device_attribution a on r.attribution_id = a.id and a.deleted = 0
        where r.deleted = 0 and r.recycle_status = 2 and r.uid = #{query.uid} and r.module_id = #{query.moduleId}
        <if test="query.weighType != null and query.weighType.size > 0">
            and r.weigh_type in
            <foreach collection="query.weighType" item="item" open="(" close=")" separator="," index="">#{item}
            </foreach>
        </if>
        <if test="query.truckNo != null and query.truckNo != ''">
            and r.truck_no like '%${query.truckNo}%'
        </if>
        <if test="query.attributionName != null and query.attributionName != ''">
            and a.name like '%${query.attributionName}%'
        </if>
        <if test="query.attributionCode != null and query.attributionCode != ''">
            and a.code like '%${query.attributionCode}%'
        </if>
        <if test="query.pushStatus != null and query.pushStatus.size > 0">
            and r.push_status in
            <foreach collection="query.pushStatus" item="item" open="(" close=")" separator="," index="">#{item}
            </foreach>
        </if>
        order by r.gmt_create desc
    </select>

    <select id="resultExport" resultType="java.util.Map">
        select r.id, r.truck_no 'truckNo', r.weight_gross 'weightGross', r.weight_tare 'weightTare', r.weight_net
        'weightNet',
        r.weight_gross_time 'weightGrossTime', r.weigh_type 'weighType', r.weight_tare_time 'weightTareTime', a.name
        'attributionName', a.code 'attributionCode'
        from d_receipt_recycle r
        left join s_device_attribution a on r.attribution_id = a.id and a.deleted = 0
        where r.deleted = 0 and r.recycle_status = 2 and r.uid = #{query.uid} and r.module_id = #{query.moduleId}
        <if test="query.weighTypeList != null and query.weighTypeList.size > 0">
            and r.weigh_type in
            <foreach collection="query.weighTypeList" item="item" open="(" close=")" separator="," index="">#{item}
            </foreach>
        </if>
        <if test="query.attributionIdList != null and query.attributionIdList.size > 0">
            and r.attribution_id in
            <foreach collection="query.attributionIdList" item="item" open="(" close=")" separator="," index="">
                #{item}
            </foreach>
        </if>
        <if test="query.weightGrossTimeList != null and query.weightGrossTimeList.size > 1">
            and r.weight_gross_time between #{query.weightGrossTimeList[0]} and #{query.weightGrossTimeList[1]}
        </if>
        <if test="query.weightTareTimeList != null and query.weightTareTimeList.size > 1">
            and r.weight_tare_time between #{query.weightTareTimeList[0]} and #{query.weightTareTimeList[1]}
        </if>
        order by r.gmt_create desc
    </select>
    <select id="countByDeviceSn" resultType="java.lang.Long">
        select count(1)
        from d_receipt_recycle
        where deleted = 0
          and attribution_id = #{attributionId}
          and device_sn = #{deviceSn}
    </select>
    <select id="needPushDatas"
            resultType="cn.pinming.microservice.material.client.management.common.model.ReceiptRecycleDO">
        select *
        from d_receipt_recycle
        where deleted = 0
        and uid = #{uid}
        and recycle_status = 2
        and attribution_id in
        <foreach collection="attributionList" item="id" open="(" close=")" separator="," index="">
            #{id}
        </foreach>
        and (push_status = 1 or (push_status = 2 and TIMESTAMPDIFF(MINUTE ,wait_time, now()) > 5))
        limit 100
    </select>
    <select id="ocrRecycleList"
            resultType="cn.pinming.material.v2.model.vo.ReceiptRecycleVO">
        select a.id,
        truck_no,
        weight_gross,
        weight_tare,
        weight_gross_time,
        weight_tare_time,
        weight_net,
        recycle_time,
        weigh_type,
        b.name as batchName,
        push_status,
        recycle_pic as uuid,
        c.ext_id as contractId
        from d_receipt_recycle a
        left join d_receipt_recycle_batch b on a.batch_id = b.id
        left join s_ocr_module c on c.id = a.module_id
        left join s_device_attribution d on d.id = a.attribution_id
        where a.deleted = 0
        and recycle_status = 2
        and a.uid = #{uid}
        and a.module_id = #{query.moduleId}
        and find_in_set(#{query.attributionCode}, d.code)
        <if test="query.weighType != null">
            and a.weigh_type = #{query.weighType}
        </if>
        <if test="query.batchName != null and query.batchName != ''">
            and b.name like CONCAT('%',#{query.batchName},'%')
        </if>
        <if test="query.grossStartTime != null and query.grossEndTime != null">
            and a.weight_gross_time <![CDATA[ >= ]]> #{query.grossStartTime}
            and a.weight_gross_time <![CDATA[ <= ]]> #{query.grossEndTime}
        </if>
        <if test="query.tareStartTime != null and query.tareEndTime != null">
            and a.weight_tare_time <![CDATA[ >= ]]> #{query.tareStartTime}
            and a.weight_tare_time <![CDATA[ <= ]]> #{query.tareEndTime}
        </if>
        <if test="query.recycleStartTime != null and query.recycleEndTime != null">
            and a.recycle_time <![CDATA[ >= ]]> #{query.recycleStartTime}
            and a.recycle_time <![CDATA[ <= ]]> #{query.recycleEndTime}
        </if>
        <if test="query.createTimeStart != null and query.createTimeEnd != null">
            and a.gmt_create <![CDATA[ >= ]]> #{query.createTimeStart}
            and a.gmt_create <![CDATA[ <= ]]> #{query.createTimeEnd}
        </if>
        order by a.gmt_create desc

    </select>


</mapper>
