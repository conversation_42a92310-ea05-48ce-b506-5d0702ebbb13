<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.client.management.common.mapper.ext.WeighDataConfirmExtMapper">

<select id="col" resultType="cn.pinming.microservice.material.client.management.common.vo.WeighDataConfirmVO">
    select a.id,a.confirm_no,a.truck_no,a.delivery_detail_ids,a.weight_actual,a.actual_count,a.device_sn,a.gmt_create,a.weigh_type,a.weight_unit,a.delivery_id,
    a.weight_gross,a.weight_tare,a.weight_deduct,a.moisture_content,a.weight_actual,a.ratio,a.weight_send,a.actual_count,a.deviation_count,a.deviation_rate,a.enter_time,a.leave_time,a.push_state,a.weight_unit,
           b.driver,
           c.user_name as supplierName,
           d.name as attributionName
    from d_weigh_data_confirm a
             left join d_delivery b on b.id = a.delivery_id and b.deleted = 0
             left join s_user c on c.uid = b.supplier_id and c.deleted = 0
             left join s_device_attribution d on d.id = a.attribution_id and d.deleted = 0
    left join d_purchase_order e on e.id = b.purchase_order_id and e.deleted = 0
    where a.deleted = 0
    and (a.create_id = #{query.uid} or b.supplier_id = #{query.uid} or e.create_id = #{query.uid} )
    and a.is_confirmed = 1
    <if test="query.isGrossAndTare == null">
        and a.delivery_detail_ids is not null
    </if>
    <if test="query.isGrossAndTare != null and query.isGrossAndTare == 1">
        and  a.purchase_order_id is null
    </if>
    <if test="query.weighType != null">
        and a.weigh_type = #{query.weighType}
    </if>
    <if test="query.deviceSn != null">
        and a.device_sn like concat('%', #{query.deviceSn}, '%')
    </if>
    <if test="query.no != null and query.no != ''">
        and a.confirm_no like concat('%', #{query.no}, '%')
    </if>
    <if test="query.truckNo != null and query.truckNo != ''">
        and a.truck_no like concat('%', #{query.truckNo}, '%')
    </if>
    <if test="query.startDate != null and query.endDate != null">
        and a.gmt_create between #{query.startDate} and #{query.endDate}
    </if>
    <if test="query.purchaseOrderId != null and query.purchaseOrderId != ''">
        and a.purchase_order_id = #{query.purchaseOrderId}
    </if>
    <if test="query.deliveryId != null and query.deliveryId != ''">
        and a.delivery_id = #{query.deliveryId}
    </if>
    <if test="query.materialType != null and query.materialType == 2">
        and a.delivery_detail_ids like '%,%'
    </if>
    order by a.gmt_create desc
</select>

<select id="getConfirmPushData"
            resultType="cn.pinming.microservice.material.client.management.common.dto.push.PushConfirmDbDTO">
    select  a.id,a.local_id as confirmId,a.truck_no,a.moisture_content as deductRatio,a.ratio as scaleFactor,a.weigh_type as weighingType,a.document_pic,a.sign_pic,a.signer_pic,a.delivery_detail_ids,
            a.actual_count as convertValue,a.weight_unit as unitOfMeasurement,(a.actual_count - a.weight_send) as deviation,a.weight_deduct as deductWeight,
            a.weight_net as netWeight,a.weight_actual as actualWeight,'吨' as unitInuse,a.record_id_1,a.record_id_2,a.weight_gross,a.weight_tare,a.enter_time,a.leave_time,
            b.no as deliveryNo,b.driver as driverName,b.driver_mobile as driverPhoneNo,b.gmt_create as deliveryTime,b.truck_no as driverTruckNo,
            c.order_ext_id as orderId,c.source,
            d.primary_code,
            f.ext_code
    from d_weigh_data_confirm a
             left join d_delivery b on b.id = a.delivery_id
             left join d_purchase_order c on c.id = b.purchase_order_id
    left join s_device_attribution d on d.id = a.attribution_id and d.uid = a.create_id and d.deleted = 0
    left join d_receipt_recycle e on e.local_id = a.local_id and e.deleted = 0
    left join d_receipt_recycle_batch f on f.id = e.batch_id and f.deleted = 0
    where a.deleted = 0
    and a.push_state in (1,3)
    and a.is_confirmed = 1
    <if test="ids != null and ids.size > 0">
        and a.id in
        <foreach collection="ids" item="id" open="(" close=")" separator="," index="">
            #{id}
        </foreach>
    </if>
</select>

<select id="selectConfirmToPush"
            resultType="cn.pinming.microservice.material.client.management.common.model.WeighDataConfirmDO">
    select a.id,a.attribution_id
    from d_weigh_data_confirm a
    left join d_delivery b on b.id = a.delivery_id
    where a.create_id = #{uid}
    and a.deleted = 0
    and a.is_confirmed = 1
    <if test="attributionIdList != null and attributionIdList.size > 0">
        and a.attribution_id in
        <foreach collection="attributionIdList" item="id" open="(" close=")" separator="," index="">
            #{id}
        </foreach>
    </if>
    <if test="supplierIdList != null and supplierIdList.size > 0">
        and b.supplier_id in
        <foreach collection="supplierIdList" item="id" open="(" close=")" separator="," index="">
            #{id}
        </foreach>
    </if>
    and a.push_state in (1,3)
    and a.delivery_detail_ids not regexp '[,]'
    and b.no not like concat('3','%')

    union all

    select a.id,a.attribution_id
    from d_weigh_data_confirm a
    left join d_delivery b on b.id = a.delivery_id
    where a.create_id = #{uid}
    and a.deleted = 0
    and a.is_confirmed = 1
    <if test="attributionIdList != null and attributionIdList.size > 0">
        and a.attribution_id in
        <foreach collection="attributionIdList" item="id" open="(" close=")" separator="," index="">
            #{id}
        </foreach>
    </if>
    and a.push_state in (1,3)
    and (a.delivery_detail_ids not regexp '[,]' or a.delivery_detail_ids is null)
    and b.no like concat('3','%')
    </select>

<select id="selectConfirmToRecycle"
            resultType="cn.pinming.microservice.material.client.management.common.model.WeighDataConfirmDO">
    select a.*
    from d_weigh_data_confirm a
             left join d_delivery b on b.id = a.delivery_id and b.deleted = 0
             left join d_receipt_recycle c on c.local_id = a.local_id and c.deleted = 0
    where b.type = 2
      and c.id is null
      and a.deleted = 0
      and a.document_pic is not null
      and a.is_confirmed = 1
    limit 10
    </select>

    <select id="confirmQuery"
            resultType="cn.pinming.microservice.material.client.management.common.vo.WeighDataConfirmDetailDTO">
        select a.local_id,a.truck_no,a.weight_actual,a.local_create_time,a.device_sn
             ,b.name as attributionName,b.code as attributionCode
        from d_weigh_data_confirm a
                 left join s_device_attribution b on b.id = a.attribution_id
        where a.deleted = 0
          and a.create_id = #{uid}
        and a.record_id_1 is not null
        and a.record_id_2 is not null
        <if test="query.weighType != null and query.weighType != ''">
            and a.weigh_type = #{query.weighType}
        </if>
        <if test="query.truckNos != null and query.truckNos.size > 0">
            and a.truck_no in
            <foreach collection="query.truckNos" item="truckNo" open="(" close=")" separator="," index="">
                #{truckNo}
            </foreach>
        </if>
        <if test="query.startTime != null">
            and a.local_create_time &gt; #{query.startTime}
        </if>
        <if test="query.endTime != null">
            and a.local_create_time &lt; #{query.endTime}
        </if>
        <if test="query.confirmType != null and query.confirmType == 1">
            and a.purchase_order_id is null
        </if>
        <if test="query.truckNo != null and query.truckNo != ''">
            and a.truck_no like concat('%', #{query.truckNo}, '%')
        </if>
        <if test="query.attributionCode != null and query.attributionCode != ''">
            and FIND_IN_SET(#{query.attributionCode}, b.code)
        </if>
        order by a.local_create_time desc
    </select>

    <select id="getConfirm"
            resultType="cn.pinming.microservice.material.client.management.common.dto.WeighConfirmPullDTO">
        select a.local_id,
               a.confirm_no,
               a.device_sn,
               a.truck_no,
               a.weigh_type,
               a.delivery_detail_ids,
               a.weight_gross,
               a.weight_tare,
               a.weight_deduct,
               a.weight_net,
               a.moisture_content,
               a.weight_actual,
               a.ratio,
               a.actual_count,
               a.weight_unit,
               a.weight_send,
               a.deviation_count,
               a.deviation_rate,
               a.enter_time,
               a.leave_time,
               a.push_state,
               a.document_pic,
               a.sign_pic,
               a.signer_pic,
               a.signature_time,
               a.record_id_1,
               a.record_id_2,
               '吨'           as unitInuse,
               b.no           as deliveryNo,
               b.order_ext_id,
               b.supplier_ext_id,
               b.position     as deliveryPosition,
               b.project      as deliveryProject,
               b.address      as deliveryAddress,
               b.receiver     as deliveryReceiver,
               b.mobile       as deliveryMobile,
               b.receive_date as deliveryReceiveDate,
               b.remark       as deliveryRemark,
               b.truck_no  as deliveryTruckNo,
               b.driver            as deliveryDriver,
               b.driver_mobile     as deliveryDriverMobile,
               b.gmt_create        as deliveryCreateTime,
               d.primary_code
        from d_weigh_data_confirm a
            left join d_delivery b on b.id = a.delivery_id
            left join s_device_attribution d on d.id = a.attribution_id and d.uid = a.create_id and d.deleted = 0
        where a.deleted = 0
        <if test="query.truckNos != null and query.truckNos.size > 0">
            and a.truck_no in
            <foreach collection="query.truckNos" item="truckNo" open="(" close=")" separator="," index="">
                #{truckNo}
            </foreach>
        </if>
        <if test="query.truckNo != null and query.truckNo != ''">
            and a.truck_no like concat('%', #{query.truckNo}, '%')
        </if>
        <if test="query.startTime != null">
            and a.local_create_time &gt; #{query.startTime}
        </if>
        <if test="query.endTime != null">
            and a.local_create_time &lt; #{query.endTime}
        </if>
        <if test="query.attributionCode != null and query.attributionCode != ''">
            and FIND_IN_SET(#{query.attributionCode}, d.code)
        </if>
        <if test="query.deviceSn != null and query.deviceSn != ''">
            AND a.device_sn = #{query.deviceSn}
        </if>
        <if test="query.pushStatus != null">
            AND a.push_state = #{query.pushStatus}
        </if>
        <if test="query.weighType != null">
            AND a.weigh_type = #{query.weighType}
        </if>
        order by a.local_create_time desc
    </select>
</mapper>
