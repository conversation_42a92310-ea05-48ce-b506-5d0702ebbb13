<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.client.management.common.mapper.AppInvocationDailyLogMapper">

    <select id="selectExtAppInvocation"
            resultType="cn.pinming.microservice.material.client.management.common.vo.AppInvocationVO">
        select ifnull(sum(used_api_total), 0) as usedApiTotal, b.name
        from s_app_invocation_daily_log a
                 left join s_device_attribution b on b.uid = a.create_id and b.id = a.attribution_id
        where a.create_id = #{uid}
          and a.app_id = #{appId}
          and a.attribution_id != -1
          and a.deleted = 0
        group by a.attribution_id
    </select>
    <select id="sumUsedApiTotal" resultType="java.lang.Long">
        select ifnull(sum(used_api_total), 0)
        from s_app_invocation_daily_log
        where create_id = #{uid}
        and app_id in <foreach collection="appIdList" item="appId" open="(" close=")" separator=",">#{appId}</foreach>
          and attribution_id = -1
          and deleted = 0
    </select>
</mapper>
