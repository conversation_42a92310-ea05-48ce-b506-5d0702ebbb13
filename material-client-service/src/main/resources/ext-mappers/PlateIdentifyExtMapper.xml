<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.client.management.common.mapper.ext.PlateIdentifyExtMapper">

    <select id="selectTaskList"
            resultType="cn.pinming.microservice.material.client.management.common.dto.PlateIdentifyDTO">
        select b.id, a.record_id, b.truck_no, b.lpr_truck_no, group_concat(c.file_id) as fileIds, b.risk_grade as oldRisk
        from d_plate_identify a
        left join d_weigh_data b on a.record_id = b.record_id
        left join d_weigh_data_pic c on c.record_id = b.record_id and c.deleted = 0 and c.type = 1
        where is_checked = 0
        and a.deleted = 0
        and a.gmt_create <![CDATA[ <= ]]> now()
        group by a.record_id limit 20
    </select>
</mapper>
