<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.client.management.common.mapper.ReceiptRecycleBatchMapper">

    <select id="selectByQuery"
            resultType="cn.pinming.microservice.material.client.management.common.vo.ReceiptRecycleBatchVO">
        select a.id, a.name, c.name as attribution_name, a.status, a.ext_code, a.gmt_create, a.attribution_id,
        a.device_id,
        ifnull(count(b.id), 0) as amount
        from d_receipt_recycle_batch a
        left join d_receipt_recycle b on a.id = b.batch_id and b.deleted = 0
        left join s_device_attribution c on a.attribution_id = c.id
        where a.deleted = 0
        and a.create_id = #{uid}
        <if test="keyword!= null and keyword!= ''">
            and ( a.name like #{keyword} or a.id like #{keyword} or a.ext_code like #{keyword} )
        </if>
        group by a.id
        order by a.gmt_create desc
    </select>
    <select id="selectDeviceIdListByBatchId"
            resultType="cn.pinming.microservice.material.client.management.common.model.ReceiptRecycleBatchDO">
        select id, device_id
        from d_receipt_recycle_batch
        where deleted = 0
        and create_id = #{uid}
        and (
        <foreach collection="deviceIdList" item="deviceId" separator="or">
            FIND_IN_SET(#{deviceId}, device_id) > 0
        </foreach>
        )
        and id != #{id}
    </select>
    <select id="selectBatchByDeviceId"
            resultType="cn.pinming.microservice.material.client.management.common.model.ReceiptRecycleBatchDO">
        select attribution_id,status,id
        from d_receipt_recycle_batch
        where deleted = 0
        and create_id = #{uid}
        and FIND_IN_SET(#{deviceId}, device_id)
    </select>
</mapper>
