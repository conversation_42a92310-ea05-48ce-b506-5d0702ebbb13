<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.client.management.common.mapper.ext.DeviceAttributionExtMapper">

    <select id="getDeviceAttributionIdByCode" resultType="java.lang.Long">
        select id from s_device_attribution where uid = #{uid} and find_in_set(#{code},code)
    </select>

    <select id="getDeviceAttributionsByIds"
            resultType="cn.pinming.microservice.material.client.management.common.model.DeviceAttributionDO">
        select * from s_device_attribution where id in
        <foreach collection="ids" item="id" open="(" close=")" separator="," index="">
            #{id}
        </foreach>
    </select>
    <select id="listAttributionDevice"
            resultType="cn.pinming.microservice.material.client.management.common.vo.AttributionDeviceVO">
        select b.name as attributionName, a.device_name, c.id as deviceId
        from s_device_binding a
        left join s_device_attribution b on a.attribution_id = b.id
        left join s_device c on a.device_id = c.id
        where a.uid = #{uid}
        and c.device_type = #{type}
        <if test="attributionId!= null">
            and a.attribution_id =#{attributionId}
        </if>
        and c.deleted = 0
        and b.deleted = 0
        and a.deleted = 0
    </select>
    <select id="getDeviceAttributionsByCode"
            resultType="cn.pinming.microservice.material.client.management.common.model.DeviceAttributionDO">
        select name,code,id
        from s_device_attribution a
        where a.uid = #{uid}
        and find_in_set(#{code},code)
        and a.deleted = 0
    </select>

</mapper>
