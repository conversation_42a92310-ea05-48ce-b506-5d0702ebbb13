<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.client.management.common.mapper.DeliveryMapper">

    <select id="selectListByPurchaseOrderId"
            resultType="cn.pinming.microservice.material.client.management.common.vo.delivery.DeliveryItemDTO">
        select a.id,
        name,
        spec,
        argument,
        a.amount                      as orderAmount,
        sum(b.amount)                 as sendAmount,
        count(distinct b.delivery_id) as truckAmount,
        a.brand,
        a.unit,
        position,
        a.remark
        from d_purchase_order_detail a
        left join d_delivery_detail b on a.id = b.purchase_order_detail_id and b.status != 5
        where order_id = #{purchaseOrderId}
        and a.deleted = 0
        group by a.id
    </select>
    <select id="selectTruckList"
            resultType="cn.pinming.microservice.material.client.management.common.vo.delivery.DeliveryTruckVO">
        SELECT truck_no,
        driver,
        mobile
        FROM d_delivery
        WHERE (truck_no, gmt_create) IN (
        SELECT truck_no,
        MAX(gmt_create)
        FROM d_delivery
        WHERE deleted = 0
        and (create_id = #{uid} or supplier_id = #{uid})
        GROUP BY truck_no
        ) and (create_id = #{uid} or supplier_id = #{uid})
        ORDER BY gmt_create desc
        limit 100
    </select>
    <select id="pageByQuery"
            resultType="cn.pinming.microservice.material.client.management.common.vo.delivery.DeliveryVO">
        select
        a.id,
        no,
        a.project,
        b.user_name                                     as account_name,
        c.name                                          as supplier_name,
        a.truck_no,
        a.driver,
        group_concat(distinct e.name, '/', e.spec SEPARATOR ',') as materialCategory,
        a.status,
        a.push_state,
        a.gmt_create
        from d_delivery a
        left join s_user b on a.supplier_id = b.uid
        left join s_supplier_config c on a.supplier_ext_id = c.supplier_ext_id
        left join d_delivery_detail d on a.id = d.delivery_id
        left join d_purchase_order_detail e on d.purchase_order_detail_id = e.id
        left join d_purchase_order f on f.id = e.order_id
        where (a.create_id = #{uid} or a.supplier_id = #{uid} or f.create_id = #{uid} )
        and a.deleted = 0 and a.type = 1
        <if test="no!= null and no!= ''">
            and no like concat('%', #{no}, '%')
        </if>
        <if test="project!= null and project!= ''">
            and a.project like concat('%', #{project}, '%')
        </if>
        <if test="supplierName!= null and supplierName!= ''">
            and c.name like concat('%', #{supplierName}, '%')
        </if>
        <if test="accountName!= null and accountName!= ''">
            and b.user_name like concat('%', #{accountName}, '%')
        </if>
        <if test="truckNo!= null and truckNo!= ''">
            and a.truck_no like concat('%', #{truckNo}, '%')
        </if>
        <if test="driver!= null and driver!= ''">
            and a.driver like concat('%', #{driver}, '%')
        </if>
        <if test="status != null and status.size > 0">
            AND a.status in
            <foreach collection="status" item="item" open="(" close=")" separator="," index="">
                #{item}
            </foreach>
        </if>
        <if test="pushState != null and pushState.size > 0">
            AND a.push_state in
            <foreach collection="pushState" item="item" open="(" close=")" separator="," index="">
                #{item}
            </foreach>
        </if>
        group by a.id, a.gmt_create
        <if test="materialCategory!= null and materialCategory!= ''">
            having materialCategory like concat('%', #{materialCategory}, '%')
        </if>
        order by a.gmt_create desc
    </select>
    <select id="selectListByDeliveryId"
            resultType="cn.pinming.microservice.material.client.management.common.vo.delivery.DeliveryItemDTO">
        select
        b.id,
        name,
        spec,
        argument,
        b.amount                      as orderAmount,
        a.amount                      as currentAmount,
        sum(a.amount)                 as sendAmount,
        count(distinct a.delivery_id) as truckAmount,
        b.brand,
        b.unit,
        position,
        b.remark
        from d_delivery_detail a
        left join d_purchase_order_detail b on b.id = a.purchase_order_detail_id
        where delivery_id = #{deliveryId}
        and a.deleted = 0
    </select>
    <select id="getPurchaseOrder"
            resultType="cn.pinming.microservice.material.client.management.common.model.PurchaseOrderDO">
        select f.*
        from d_delivery a
        left join s_user b on a.supplier_id = b.uid
        left join s_supplier_config c on a.supplier_ext_id = c.supplier_ext_id
        left join d_purchase_order f on f.id = a.purchase_order_id
        where (a.create_id = #{uid} or a.supplier_id = #{uid} or f.create_id = #{uid} )
        and a.deleted = 0
        and f.id = #{purchaseOrderId}
        group by f.id
    </select>


</mapper>
