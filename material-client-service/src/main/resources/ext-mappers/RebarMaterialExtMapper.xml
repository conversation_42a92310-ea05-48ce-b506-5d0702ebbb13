<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.client.management.common.mapper.ext.RebarMaterialExtMapper">

<select id="rebarMaterialList"
            resultType="cn.pinming.microservice.material.client.management.common.model.RebarMaterialDO">
    select *
    from s_rebar_material
    where deleted = 0
    <if test="query.materialName != null and query.materialName != ''">
        and material_name like concat('%', #{query.materialName}, '%')
    </if>
    <if test="query.materialSpec != null and query.materialSpec != ''">
        and material_spec like concat('%', #{query.materialSpec}, '%')
    </if>
    <if test="query.extCode != null and query.extCode != ''">
        and ext_code like concat('%', #{query.extCode}, '%')
    </if>
    <if test="query.type != null ">
        and type = #{query.type}
    </if>
</select>
</mapper>
