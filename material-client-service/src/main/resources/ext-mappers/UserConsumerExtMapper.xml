<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.client.management.common.mapper.ext.UserConsumerExtMapper">

<select id="consumerList"
            resultType="cn.pinming.microservice.material.client.management.common.vo.UserConsumerVO">
    select b.consume_id, a.name, b.is_enable, a.gmt_create, b.phone_sn, a.attributions,a.id
    from s_user_consumer a
             left join s_user_consumer_combine b on b.consume_id = a.id and b.deleted = 0
    where a.uid = #{uId}
      and a.deleted = 0
</select>
</mapper>
