<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.client.management.common.mapper.ext.UserConfigExtMapper">

    <select id="selectSubscribeList"
            resultType="cn.pinming.microservice.material.client.management.common.vo.UserSubscribeVO">
        select b.id,
        a.uid,
        a.email,
        a.user_name,
        b.space_size,
        b.space_use_size,
        if(b.api_total >= b.api_use_total, b.api_total, 0) as api_total,
        if(b.api_total >= b.api_use_total, b.api_use_total, 0) as api_use_total
        from s_user a
        left join s_user_config b on a.uid = b.uid
        where a.deleted = 0
        <if test="query.email != null">
            and a.email like concat('%', #{query.email}, '%')
        </if>
        <if test="query.userName != null">
            and a.user_name like concat('%', #{query.userName}, '%')
        </if>
        order by a.gmt_create desc
    </select>
    <select id="selectAppInvocationList"
            resultType="cn.pinming.microservice.material.client.management.common.vo.AppInvocationVO">
        select a.name, sum(ifnull(b.used_api_total, 0)) as used_api_total
        from s_developer_app a
        left join s_app_invocation_daily_log b on a.id = b.app_id and b.create_id = #{uid}
        where a.deleted = 0
        and a.id in <foreach collection="appIdList" item="appId" open="(" close=")" separator=","> #{appId} </foreach>
        group by a.id
    </select>
</mapper>
