<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.client.management.common.mapper.ext.DeviceBindingExtMapper">

    <select id="appAttributionCheck" resultType="java.lang.String">
        select c.code
        from s_device a
        left join s_device_binding b on b.device_id = a.id and b.deleted = 0
        left join s_device_attribution c on c.id = b.attribution_id and c.deleted = 0
        where a.device_sn = #{deviceSn} and a.device_type = #{deviceType}
        and a.deleted = 0
    </select>

    <select id="userList" resultType="cn.pinming.microservice.material.client.management.common.vo.DeviceUserVO">
        select b.device_sn, b.device_type, c.name, a.gmt_create, a.receive,a.id,a.device_id
        from s_device_binding a
        left join s_device b on b.id = a.device_id and b.deleted = 0
        left join s_device_attribution c on c.id = a.attribution_id and c.deleted = 0
        where a.deleted = 0
        and a.uid = #{uId}
        order by a.gmt_create desc
    </select>

    <select id="attributionList"
            resultType="cn.pinming.microservice.material.client.management.common.vo.DeviceAttributionVO">
        select a.id,
        b.device_sn,
        b.id as device_id,
        a.attribution_id,
        a.uid,
        b.device_type,
        a.binding_time,
        a.device_name,
        if(b.device_type = 'SELF_CHECK', count(d.id), count(c.id)) as num,
        a.password,
        a.auxiliary_code,
        a.self_check_mode,
        a.timeout,
        a.signature,
        a.skip_scan_code,
        a.auto_weight,
        a.msg_robot,
        a.msg_receiver,
        e.platform_duration,
        e.platform_count,
        e.sustain_duration,
        e.weight,
        e.alarm_time,
        e.alarm_item,
        a.same_truck_min_duration,
        a.local_ip
        from s_device_binding a
        left join s_device b on b.id = a.device_id and b.deleted = 0
        left join d_weigh_data c on c.device_sn = b.device_sn and c.attribution_id = a.attribution_id and c.deleted = 0
        left join d_weigh_data_confirm d on d.device_sn = b.device_sn and d.attribution_id = a.attribution_id
        left join d_weigh_curve_config e on e.device_id = a.device_id and e.attribution_id = a.attribution_id and a.id = e.device_binding_id
        where a.attribution_id = #{attributionId}
        and a.deleted = 0
        group by a.id
        order by a.binding_time desc
    </select>
    <select id="getDeviceBindingList" resultType="cn.pinming.material.v2.model.dto.DeviceBindingDTO">
        select a.device_name as name, a.local_ip, a.password
        from s_device_binding a
        left join s_device b on a.device_id = b.id
        left join s_device_attribution c on c.id = a.attribution_id
        where device_type = 'WEIGH'
        and find_in_set(#{code}, code)
        and a.deleted = 0
        and b.deleted = 0
        and c.deleted = 0
        and local_ip is not null
    </select>

</mapper>
