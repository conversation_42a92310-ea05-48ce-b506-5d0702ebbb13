<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.client.management.common.mapper.SelfQrcodeMapper">
    <update id="updateEnableStatusById">
        update s_self_qrcode
        set enabled = enabled ^ 1,
        modify_id = #{uid}
        where id = #{id} and create_id = #{uid}
    </update>

    <select id="selectListConfig"
            resultType="cn.pinming.microservice.material.client.management.common.vo.SelfQrcodeVO">
        select a.id, a.biz_id, a.name, a.attribution_id, a.supplier_id, a.enabled, b.name as
        attributionName,a.gmt_create
        from s_self_qrcode a
        left join s_device_attribution b on a.attribution_id = b.id
        where a.create_id = #{uid} and a.type = #{type}
        and a.deleted = 0
    </select>
    <select id="selectSupplierList"
            resultType="cn.pinming.microservice.material.client.management.common.vo.SupplierConfigVO">
        select name, a.supplier_ext_id, b.attribution_id, a.group, a.multi_cargo,a.same_truck_min_duration
        from s_supplier_config a
        left join d_purchase_order b on a.supplier_ext_id = b.supplier_ext_id and b.deleted = 0
        where
        a.deleted = 0
        <if test="supplierIdList != null and supplierIdList.size > 0">
            AND a.id in
            <foreach collection="supplierIdList" item="item" open="(" close=")" separator="," index="">
                #{item}
            </foreach>
        </if>
        AND b.attribution_id = #{attributionId}
        group by a.id
        order by b.receive_date desc
    </select>
</mapper>
