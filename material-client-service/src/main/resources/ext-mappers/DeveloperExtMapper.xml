<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.client.management.common.mapper.DeveloperMapper">

    <select id="selectAppList"
            resultType="cn.pinming.microservice.material.client.management.common.model.ext.DeveloperExtDO">
        select b.id,
        a.name,
        a.app_service,
        a.id as appId,
        <!--        ,b.state as status,-->
        b.type,
        b.is_input_enable
        from s_developer_app a
        left join s_developer b on a.id = b.app_id and b.create_id = #{uId}
        where a.deleted = 0
    </select>
    <select id="selectOneByAppKeyService"
            resultType="cn.pinming.microservice.material.client.management.common.dto.DeveloperDTO">
        select a.type, c.app_secret_key
        from s_developer a
        left join s_developer_app b on a.app_id = b.id
        left join s_user c on a.create_id = c.uid
        where c.app_key = #{appKey}
        limit 1
    </select>

</mapper>
