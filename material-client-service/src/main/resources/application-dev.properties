nacos.config.username=${nacos_username:nacos}
nacos.config.password=${nacos_password:nacos}
nacos.config.bootstrap.enable=true
nacos.config.bootstrap.logEnable=true
nacos.config.data-ids=material-common,material-client-management-service
nacos.config.group=material
nacos.config.type=properties
nacos.config.max-retry=10
nacos.config.auto-refresh=true
nacos.config.config-retry-time=2333
nacos.config.config-long-poll-timeout=46000
nacos.config.enable-remote-sync-config=false
nacos.config.server-addr=${nacos_server-addr:${nacos_server_addr:***********:18848}}
nacos.config.namespace=${nacos_namespace:bf644fca-1276-415a-89de-428331e96a46}


imatchocr.url=http://************:8080/api/match
imatchocr.ocr=http://************:8080/api/ocr

temporary.path = /Users/<USER>/Desktop/

# 定时任务执行开关本地启动默认关闭
plate.anpr.enable = false
rocketmq.isEnable = false
receiptRecycleTask.isEnable = false
