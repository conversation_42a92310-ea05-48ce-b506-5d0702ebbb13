package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * <p>
 * 发货单(运单)
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("d_delivery")
public class DeliveryDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 发货单号
     */
    private String no;

    /**
     * 类型 1 - 运单模式的发货单(运单);2 - OCR单据回收预生成单据; 3 - 仅毛皮重运单;
     */
    private Integer type;

    /**
     * 归属方id
     */
    private Long attributionId;

    /**
     * 订单id
     */
    private Long purchaseOrderId;

    /**
     * 打印模板id
     */
    private Long printTemplateId;

    /**
     * 车牌号
     */
    private String truckNo;

    /**
     * 司机
     */
    private String driver;

    /**
     * 司机手机号
     */
    private String driverMobile;

    /**
     * 1 在途，2 到场确认中，3 已到场，4 已作废  5 到场确认中-待闭合
     */
    private Integer status;

    /**
     * 运单来源(1 拌合站 2 基石)(同步前置条件)
     */
    private Integer sourceType;

    /**
     * 推送状态 1 未推送 2 已推送 3 推送失败
     */
    private Integer pushState;

    /**
     * 业务系统订单id
     */
    private String orderExtId;

    /**
     * 外部系统供应商id
     */
    private String supplierExtId;

    /**
     * 供应商ID(租户id)
     */
    private String supplierId;

    /**
     * 计划使用部位
     */
    private String position;

    /**
     * 收货项目
     */
    private String project;

    /**
     * 收货地址
     */
    private String address;

    /**
     * 收货人姓名
     */
    private String receiver;

    /**
     * 收货人电话
     */
    private String mobile;

    /**
     * 要货日期
     */
    private LocalDate receiveDate;

    /**
     * 备注
     */
    private String remark;
}
