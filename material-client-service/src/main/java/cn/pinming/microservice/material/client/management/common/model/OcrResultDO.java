package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * ocr识别记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("s_ocr_result")
public class OcrResultDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 模版id
     */
    private Long moduleId;

    /**
     * 识别结果
     */
    private String result;


}
