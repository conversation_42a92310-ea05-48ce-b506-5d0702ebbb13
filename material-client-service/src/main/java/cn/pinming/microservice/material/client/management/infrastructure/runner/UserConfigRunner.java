package cn.pinming.microservice.material.client.management.infrastructure.runner;

import cn.hutool.core.collection.CollUtil;
import cn.pinming.microservice.material.client.management.common.model.UserConfigDO;
import cn.pinming.microservice.material.client.management.common.model.UserDO;
import cn.pinming.microservice.material.client.management.service.biz.AppInvocationDailyLogService;
import cn.pinming.microservice.material.client.management.service.biz.DeveloperAppService;
import cn.pinming.microservice.material.client.management.service.biz.UserConfigService;
import cn.pinming.microservice.material.client.management.service.biz.UserService;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2023/8/8 10:26
 */
@Component
public class UserConfigRunner implements CommandLineRunner {

    @Resource
    private UserService userService;
    @Resource
    private UserConfigService userConfigService;
    @Resource
    private DeveloperAppService developerAppService;
    @Resource
    private AppInvocationDailyLogService appInvocationDailyLogService;

    @Override
    public void run(String... args) throws Exception {
        //根据s_user 初始化s_user_config
        List<UserDO> userList = userService.lambdaQuery().select(UserDO::getUid).list();
        List<UserConfigDO> configList = userConfigService.lambdaQuery().select(UserConfigDO::getUid).list();
        List<String> configUidList = configList.stream().map(UserConfigDO::getUid).collect(Collectors.toList());
        List<String> uidList = userList.stream().map(UserDO::getUid).collect(Collectors.toList());
        uidList.removeAll(configUidList);
        if (CollUtil.isNotEmpty(uidList)) {
            List<UserConfigDO> userConfigList = new ArrayList<>();
            LocalDateTime expireDateTime = LocalDateTime.now().withHour(23).withMinute(59).withSecond(59).plusYears(1);
            for (String uid : uidList) {
                UserConfigDO userConfigDO = new UserConfigDO();
                userConfigDO.setUid(uid);
                userConfigDO.setSpaceExpire(expireDateTime);
                userConfigDO.setCreateId(uid);
                userConfigDO.setModifyId(uid);
                userConfigList.add(userConfigDO);
            }
            userConfigService.saveBatch(userConfigList);
        }
    }
}
