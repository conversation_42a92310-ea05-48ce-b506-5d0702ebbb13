package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 开发者文档管理
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("s_developer_document")
public class DeveloperDocumentDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 文件类型(FRONTEND，BACKEND)
     */
    private String fileType;

    /**
     * 文件内容
     */
    private String fileContent;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件说明
     */
    private String fileDesc;


}
