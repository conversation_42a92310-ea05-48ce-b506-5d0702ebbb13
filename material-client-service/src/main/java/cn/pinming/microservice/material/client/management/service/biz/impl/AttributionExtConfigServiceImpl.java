package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.pinming.microservice.material.client.management.common.form.AttributionExtConfigForm;
import cn.pinming.microservice.material.client.management.common.mapper.AttributionExtConfigMapper;
import cn.pinming.microservice.material.client.management.common.model.AttributionExtConfigDO;
import cn.pinming.microservice.material.client.management.common.model.DeviceAttributionDO;
import cn.pinming.microservice.material.client.management.common.vo.AttributionExtConfigVO;
import cn.pinming.microservice.material.client.management.service.biz.AttributionExtConfigService;
import cn.pinming.microservice.material.client.management.service.biz.DeviceAttributionService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class AttributionExtConfigServiceImpl extends ServiceImpl<AttributionExtConfigMapper, AttributionExtConfigDO> implements AttributionExtConfigService {

    @Resource
    private DeviceAttributionService deviceAttributionService;

    @Override
    @Transactional
    public void updateAttributionExtConfig(AttributionExtConfigForm form) {
        if (ObjUtil.isNotNull(form.getAttributionId()) && ObjUtil.isNotNull(form.getEndTime())) {
            AttributionExtConfigDO attributionExtConfigDO = new AttributionExtConfigDO();
            attributionExtConfigDO.setAttributionId(form.getAttributionId());
            attributionExtConfigDO.setUid(form.getUid());
            attributionExtConfigDO.setAppId(form.getAppId());
            attributionExtConfigDO.setDateExpire(form.getEndTime() != null ? form.getEndTime().with(LocalTime.of(23, 59, 59)) : null);

            AttributionExtConfigDO one = this.lambdaQuery()
                    .eq(AttributionExtConfigDO::getUid, form.getUid())
                    .eq(AttributionExtConfigDO::getAttributionId, form.getAttributionId())
                    .eq(AttributionExtConfigDO::getAppId,form.getAppId())
                    .one();
            if (ObjUtil.isNotNull(one)) {
                attributionExtConfigDO.setId(one.getId());
            }
            this.saveOrUpdate(attributionExtConfigDO);
        }
    }

    @Override
    public List<AttributionExtConfigVO> showAttributionExtConfig(String uid,Long appId) {
        // 归属方维度
        List<DeviceAttributionDO> attributionDOList = deviceAttributionService.lambdaQuery()
                .eq(DeviceAttributionDO::getUid, uid)
                .list();
        List<AttributionExtConfigDO> configDOList = this.lambdaQuery()
                .eq(AttributionExtConfigDO::getUid, uid)
                .eq(AttributionExtConfigDO::getAppId,appId)
                .list();
        Map<Long, LocalDateTime> extConfigMap = new HashMap<>();
        if (CollUtil.isNotEmpty(configDOList)) {
            extConfigMap = configDOList.stream().collect(Collectors.toMap(AttributionExtConfigDO::getAttributionId, AttributionExtConfigDO::getDateExpire));
        }
        Map<Long, LocalDateTime> finalExtConfigMap = extConfigMap;
        return attributionDOList.stream().map(e -> {
            AttributionExtConfigVO attributionExtConfigVO = new AttributionExtConfigVO();
            BeanUtils.copyProperties(e, attributionExtConfigVO);
            attributionExtConfigVO.setAttributionId(e.getId());
            if (CollUtil.isNotEmpty(finalExtConfigMap) && ObjUtil.isNotNull(finalExtConfigMap.get(e.getId()))) {
                attributionExtConfigVO.setEndTime(finalExtConfigMap.get(e.getId()));
            }
            return attributionExtConfigVO;
        }).collect(Collectors.toList());
    }
}
