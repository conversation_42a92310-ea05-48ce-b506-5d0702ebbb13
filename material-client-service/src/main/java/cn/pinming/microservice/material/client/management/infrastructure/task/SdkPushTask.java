package cn.pinming.microservice.material.client.management.infrastructure.task;


import cn.pinming.microservice.material.client.management.common.enums.SdkPushTypeEnum;
import cn.pinming.microservice.material.client.management.infrastructure.strategy.AbstractSkdPushStrategy;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

@Component
@Slf4j
public class SdkPushTask {

    @NacosValue(value = "${push.delivery.enable:false}", autoRefreshed = true)
    private Boolean pushDeliveryEnable;
    @NacosValue(value = "${push.confirm.enable:false}", autoRefreshed = true)
    private Boolean pushConfirmEnable;
    @NacosValue(value = "${push.data.enable:false}", autoRefreshed = true)
    private Boolean pushDataEnable;
    @NacosValue(value = "${push.curve.enable:false}", autoRefreshed = true)
    private Boolean pushCurveEnable;
    @NacosValue(value = "${push.weigh.data.pic.enable:false}", autoRefreshed = true)
    private Boolean pushWeighDataPicEnable;

    @Resource
    private Map<String, AbstractSkdPushStrategy> sdkPushMap;

    @Scheduled(cron = "0/30 * * * * ? ")
    public void deliveryTask() {
        if (!pushDeliveryEnable) {
            log.info("运单推送功能未开启");
            return;
        }
        sdkPushMap.get(SdkPushTypeEnum.DELIVERY.name()).execute();
    }

    @Scheduled(cron = "0/30 * * * * ? ")
    public void confirmTask() {
        if (!pushConfirmEnable) {
            log.info("确认单推送功能未开启");
            return;
        }
        sdkPushMap.get(SdkPushTypeEnum.CONFIRM.name()).execute();
    }

    @Scheduled(cron = "0/30 * * * * ? ")
    public void dataTask() {
        if (!pushDataEnable) {
            log.info("原始数据推送功能未开启");
            return;
        }
        sdkPushMap.get(SdkPushTypeEnum.DATA.name()).execute();
    }

    @Scheduled(cron = "0/30 * * * * ? ")
    public void curveTask() {
        if (!pushCurveEnable) {
            log.info("称重曲线推送功能未开启");
            return;
        }
        sdkPushMap.get(SdkPushTypeEnum.CURVE.name()).execute();
    }

    @Scheduled(cron = "0/30 * * * * ? ")
    public void weighDataPicTask() {
        if (!pushWeighDataPicEnable) {
            log.info("称重照片推送功能未开启");
            return;
        }
        sdkPushMap.get(SdkPushTypeEnum.WEIGH_DATA_PIC.name()).execute();
    }
}
