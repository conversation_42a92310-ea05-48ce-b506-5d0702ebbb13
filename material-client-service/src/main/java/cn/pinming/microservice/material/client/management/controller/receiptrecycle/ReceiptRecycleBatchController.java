package cn.pinming.microservice.material.client.management.controller.receiptrecycle;


import cn.pinming.microservice.material.client.management.infrastructure.response.SingleResponse;
import cn.pinming.microservice.material.client.management.common.form.ReceiptRecycleBatchEditForm;
import cn.pinming.microservice.material.client.management.common.form.ReceiptRecycleBatchForm;
import cn.pinming.microservice.material.client.management.common.form.ReceiptRecycleDataBatchEditForm;
import cn.pinming.microservice.material.client.management.common.vo.ReceiptRecycleBatchVO;
import cn.pinming.microservice.material.client.management.service.biz.IReceiptRecycleBatchService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 单据回收批次表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-11
 */
@Api(value = "单据批次回收", tags = {"recycleBatch"})
@RestController
@RequestMapping("/api/recycleBatch")
public class ReceiptRecycleBatchController {

    @Resource
    private IReceiptRecycleBatchService recycleBatchService;

    @ApiOperation(value = "批次列表", response = ReceiptRecycleBatchVO.class, nickname = "batchList")
    @GetMapping("/list")
    public SingleResponse<?> list(@RequestParam(required = false) String keyword) {
        List<ReceiptRecycleBatchVO> result = recycleBatchService.listByQuery(keyword);
        return SingleResponse.of(result);
    }

    @ApiOperation(value = "新增", nickname = "batchAdd")
    @PostMapping("/add")
    public SingleResponse<?> add(@Validated @RequestBody ReceiptRecycleBatchForm form) {
        recycleBatchService.add(form);
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "删除", nickname = "batchDelete")
    @DeleteMapping("/{id}")
    public SingleResponse<?> delete(@PathVariable Long id) {
        recycleBatchService.deleteById(id);
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "归档", nickname = "batchArchive")
    @PostMapping("/{id}/archive")
    public SingleResponse<?> archive(@PathVariable Long id) {
        recycleBatchService.archiveById(id);
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "修改扩展编码", nickname = "extCodeEdit")
    @PutMapping("/extCode")
    public SingleResponse<?> extCodeEdit(@Validated @RequestBody ReceiptRecycleBatchEditForm form) {
        recycleBatchService.extCodeEdit(form);
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "修改批次", nickname = "batchEdit")
    @PutMapping("/batchEdit")
    public SingleResponse<?> batchEdit(@Validated @RequestBody ReceiptRecycleDataBatchEditForm form) {
        recycleBatchService.dataBatchEdit(form);
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "终端绑定", nickname = "deviceBindEdit")
    @PutMapping("/deviceBind")
    public SingleResponse<?> deviceBindEdit(@Validated @RequestBody ReceiptRecycleBatchEditForm form) {
        recycleBatchService.deviceBindEdit(form);
        return SingleResponse.buildSuccess();
    }

    //查看详情

}

