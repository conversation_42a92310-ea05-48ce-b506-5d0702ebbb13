package cn.pinming.microservice.material.client.management.common.enums;

import cn.pinming.microservice.material.client.management.common.vo.TemplateParamVO;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
public enum DriverCheckParamEnum {

    TITLE("title", "确认单标题"),
    LOCAL_ID("localId", "确认单终端ID"),
    PRINT_TIME("printTime", "终端打印时间"),
    DEVICE_SN("deviceSn", "打印设备SN"),
    TRUCK_NO("truckNo", "车牌号"),
    SIGNATURE("signature", "司机签名"),
    DRIVER_NAME("driverName", "司机姓名"),
    SIGNATURE_TIME("signatureTime", "签字时间"),
    SUPPLIER("supplier", "供应商"),
    ATTRITION("attrition", "归属方"),
    WEIGHT_GROSS("weightGross", "毛重"),
    WEIGHT_TARE("weightTare", "皮重"),
    WEIGHT_NET("weightNet", "净重"),
    WEIGHT_DEDUCT("weightDeduct", "扣重"),
    MOISTURE_CONTENT("moistureContent", "扣杂"),
    WEIGHT_ACTUAL("weightActual", "实重"),
    WEIGHT_UNIT("weightUnit", "称重单位"),
    GROSS_TIME("grossTime", "毛重时间"),
    TARE_TIME("tareTime", "皮重时间"),
    CHECK_TIME("checkTime", "确认时间"),
    RATIO("ratio", "换算系数"),
    ACTUAL_COUNT("actualCount", "换算数量"),
    DEVIATION_COUNT("deviationCount", "偏差数量"),
    WAY_BILL_COUNT("waybillCounts", "运单数量"),
    WAY_BILL_UNIT("waybillUnit", "运单数量单位"),
//    MATERIAL_NAME("materialName", "货物名称"),
//    MATERIAL_SPEC("materialSpec", "货物规格"),

    WEIGH_MODE("weighMode", "称重类型"),
    WEIGH_VALUE("weighValue", "重量"),
    WEIGH_TIME("weighTime", "称重时间"),
    RECORD_ID("recordId", "原始记录ID"),
    ;

    public static final Map<String, String> KEY_MAP = Arrays.stream(values()).collect(Collectors.toMap(DriverCheckParamEnum::getVal, DriverCheckParamEnum::getDesc));

    public static final Map<String, String> VAL_MAP = Arrays.stream(values()).collect(Collectors.toMap(DriverCheckParamEnum::getDesc, DriverCheckParamEnum::getVal));

    public static final List<TemplateParamVO> LIST = Arrays.stream(values()).map(e -> new TemplateParamVO(e.getVal(), e.getDesc())).collect(Collectors.toList());

    private final String val;

    private final String desc;

    DriverCheckParamEnum(String val, String desc) {
        this.val = val;
        this.desc = desc;
    }

}
