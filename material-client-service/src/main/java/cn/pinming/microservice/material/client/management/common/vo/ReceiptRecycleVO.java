package cn.pinming.microservice.material.client.management.common.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class ReceiptRecycleVO implements Serializable {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("用户id")
    private String uid;

    @ApiModelProperty("回收来源(1-单据回收终端、2-高拍仪、3-外部推送)")
    private Integer recycleSource;

    @ApiModelProperty("状态(1-待回收、2-回收成功、3-回收失败)")
    private Integer recycleStatus;

    @ApiModelProperty("归属方id")
    private Long attributionId;

    @ApiModelProperty("归属方名称")
    private String attributionName;

    @ApiModelProperty("归属方code")
    private String attributionCode;

    @ApiModelProperty("模块id")
    private Long moduleId;

    @ApiModelProperty("单据识别类型(1-自助称重类单据、2-非称重类单据)")
    private Integer ocrType;

    @ApiModelProperty("设备机器码")
    private String deviceSn;

    @ApiModelProperty("外部辅助码")
    private String auxiliaryCode;

    @ApiModelProperty("过磅类型(1-收料、2-发料)")
    private Integer weighType;

    @ApiModelProperty("车牌号")
    private String truckNo;

    @ApiModelProperty("毛重")
    private BigDecimal weightGross;

    @ApiModelProperty("皮重")
    private BigDecimal weightTare;

    @ApiModelProperty("净重")
    private BigDecimal weightNet;

    @ApiModelProperty("毛重时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime weightGrossTime;

    @ApiModelProperty("皮重时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime weightTareTime;

    @ApiModelProperty("称重单位（吨）")
    private String unit;

    @ApiModelProperty("回收时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime recycleTime;

    @ApiModelProperty("回收图片")
    private String recyclePic;

    @ApiModelProperty("回收失败类型数组")
    private String receiptFailTypes;

    @ApiModelProperty("推送状态 1 未推送 2 队列中 3 已推送")
    private Byte pushStatus;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreate;

    @ApiModelProperty("回收图片地址列表")
    private List<String> recyclePicUrls;

    @ApiModelProperty("回收失败类型描述集合")
    private List<String> receiptFailTypeDescList;

    @ApiModelProperty("批次名称")
    private String batchName;

    @ApiModelProperty("批次id")
    private Long batchId;

    @ApiModelProperty("是否为批次归档数据  true 批次归档数据 false 非批次归档数据")
    private Boolean isBatchArchive;
}
