package cn.pinming.microservice.material.client.management.common.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class WeighDataUpdateLogVO {

    @ApiModelProperty("修改项")
    private String updateType;

    @ApiModelProperty("修改前的值")
    private String oldValue;

    @ApiModelProperty("修改后的值")
    private String newValue;

    @ApiModelProperty("操作账号")
    private String email;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("修改时间")
    private LocalDateTime gmtCreate;

}
