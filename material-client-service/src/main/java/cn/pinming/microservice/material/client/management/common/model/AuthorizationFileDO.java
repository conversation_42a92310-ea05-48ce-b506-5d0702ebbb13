package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 单机版授权文件
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("s_authorization_file")
public class AuthorizationFileDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private String uid;

    /**
     * 归属方code
     */
    private String attributionCode;


}
