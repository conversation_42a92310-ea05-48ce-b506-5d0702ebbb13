package cn.pinming.microservice.material.client.management.common.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class CheckMaterialVO implements Serializable {
    private static final long serialVersionUID = -2850355143565177215L;
    @ApiModelProperty(value = "检查id")
    private Long checkId;

    @ApiModelProperty(value = "材料规格型号")
    private String materialSpec;

    @ApiModelProperty(value = "材料名称")
    private String materialName;

    @ApiModelProperty(value = "送货重量")
    private BigDecimal sendWeight;

    @ApiModelProperty(value = "送货根数")
    private Long sendAmount;

    @ApiModelProperty(value = "钢筋类型 1 直螺纹,2 盘螺")
    private Integer type;

    @ApiModelProperty(value = "确认重量")
    private BigDecimal confirmWeight;
}
