package cn.pinming.microservice.material.client.management.service.biz;

import cn.pinming.microservice.material.client.management.common.model.ReceiptRecycleDO;
import cn.pinming.microservice.material.client.management.common.model.ReceiptRecycleWeighDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 单据回收称重表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-03
 */
public interface ReceiptRecycleWeighService extends IService<ReceiptRecycleWeighDO> {

    /**
     * 更新完整称重数据
     */
    void updateCompleteWeigh(List<String> recordIdList, ReceiptRecycleDO receiptRecycleDO);
}
