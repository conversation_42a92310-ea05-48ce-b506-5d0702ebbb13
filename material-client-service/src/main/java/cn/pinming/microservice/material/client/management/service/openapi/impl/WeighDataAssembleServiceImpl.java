package cn.pinming.microservice.material.client.management.service.openapi.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.pinming.material.v2.model.*;
import cn.pinming.material.v2.model.dto.WeighDataDTO;
import cn.pinming.material.v2.model.form.WeighDataAssembleForm;
import cn.pinming.material.v2.model.query.WeighDataQuery;
import cn.pinming.microservice.material.client.management.common.model.*;
import cn.pinming.microservice.material.client.management.infrastructure.constant.SdkQueryConstant;
import cn.pinming.microservice.material.client.management.common.enums.DeveloperAppEnum;
import cn.pinming.microservice.material.client.management.common.enums.PushLogStatusEnum;
import cn.pinming.microservice.material.client.management.common.enums.PushStatusEnum;
import cn.pinming.microservice.material.client.management.common.enums.WeighDataUnitEnum;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizErrorException;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizExceptionMessageEnum;
import cn.pinming.microservice.material.client.management.infrastructure.util.AttributionCodeShowUtil;
import cn.pinming.microservice.material.client.management.infrastructure.util.CheckUtil;
import cn.pinming.microservice.material.client.management.infrastructure.util.DateUtil;
import cn.pinming.microservice.material.client.management.common.dto.WeighPushDataDTO;
import cn.pinming.microservice.material.client.management.common.dto.WeighPushPicDTO;
import cn.pinming.microservice.material.client.management.common.form.MatchForm;
import cn.pinming.microservice.material.client.management.common.form.PicForm;
import cn.pinming.microservice.material.client.management.common.mapper.ext.DeviceAttributionExtMapper;
import cn.pinming.microservice.material.client.management.common.mapper.ext.WeighDataExtMapper;
import cn.pinming.microservice.material.client.management.common.mapper.ext.WeighDataPicExtMapper;
import cn.pinming.microservice.material.client.management.service.biz.*;
import cn.pinming.microservice.material.client.management.service.business.CombineService;
import cn.pinming.microservice.material.client.management.service.business.OcrService;
import cn.pinming.microservice.material.client.management.service.openapi.WeighDataAssembleService;
import cn.pinming.zhjz.cxptz.microservice.filecenter.api.FileCenterService;
import cn.pinming.zhjz.cxptz.microservice.filecenter.api.model.DownloadUrlOptionsDto;
import cn.pinming.zhjz.cxptz.microservice.filecenter.api.model.FileIdentityDto;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.assertj.core.util.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class WeighDataAssembleServiceImpl implements WeighDataAssembleService {
    @Resource
    private WeighDataPicService weighDataPicService;
    @Resource
    private WeighDataService weighDataService;
    @Resource
    private DeviceAttributionService deviceAttributionService;
    @Resource
    private UserService userService;
    @Resource
    private CheckUtil checkUtil;
    @Resource
    private AppInvocationDailyLogService appInvocationDailyLogService;
    @Resource
    private DeviceAttributionExtMapper deviceAttributionExtMapper;
    @Resource
    private WeighDataPicExtMapper weighDataPicExtMapper;
    @DubboReference
    private FileCenterService fileCenterService;
    @Resource
    private PushLogService pushLogService;
    @Value("${imatchocr.ocr}")
    private String ocrUrl;
    @Resource
    private OcrService ocrService;
    @Resource
    private CombineService combineService;
    @Resource
    private WeighDataExtMapper weighDataExtMapper;
    @Resource
    private UserBusinessConfigService userBusinessConfigService;
    @Resource
    private FileOssService fileOssService;
    @Resource
    private ReceiptRecycleService receiptRecycleService;
    @Resource
    private AttributionCodeShowUtil attributionCodeShowUtil;

    @Override
    public WeighDataAssemble weighDataAssemble(String appKey, WeighDataAssembleForm assembleForm) {
        WeighDataAssemble result = new WeighDataAssemble();
        if (ObjectUtil.isNull(assembleForm)) {
            return result;
        }
        UserDO userDO = userService.getUserByAppKey(appKey);
        // 服务校验
        checkUtil.appCheck(userDO.getUid(), DeveloperAppEnum.ASSEMBLE);
        return combineService.weighDataAssemble(Arrays.asList(assembleForm.getFirst(), assembleForm.getSecond()), assembleForm.getAttributionCode(), userDO.getUid());
    }

    @Override
    public boolean confirm(String appKey, WeighDataAssembleForm assembleForm) {
        UserDO userDO = userService.getUserByAppKey(appKey);
        return combineService.confirmAssemble(Arrays.asList(assembleForm.getFirst(), assembleForm.getSecond()), assembleForm.getAttributionCode(),null, userDO.getUid(), appKey);
    }

    @Override
    public WeighDataAssemble weighDataQuery(String appKey, WeighDataAssembleForm assembleForm) {
        // 因为前有组装接口，已通过校验，该接口就不校验了，直接返回结果
        WeighDataAssemble weighDataAssemble = new WeighDataAssemble();

        UserDO userDO;
        if (StringUtils.isNotBlank(appKey)) {
            userDO = userService.getUserByAppKey(appKey);
        } else {
            String uid = assembleForm.getUid();
            userDO = userService.getByUid(uid);
        }
        if (ObjectUtil.isNotEmpty(userDO)) {
            List<DeviceAttributionDO> list = deviceAttributionService.lambdaQuery()
                    .eq(DeviceAttributionDO::getUid, userDO.getUid())
                    .list();
            if (CollUtil.isEmpty(list)) {
                return weighDataAssemble;
            }

            Long attributionId = null;
            for (int i = 0; i < list.size(); i++) {
                List<String> split = StrUtil.split(list.get(i).getCode(), ",");
                long count = split.stream().filter(e -> e.equals(assembleForm.getAttributionCode())).count();
                if (count == 1) {
                    attributionId = list.get(i).getId();
                    break;
                }
            }
            if (attributionId == null) {
                return weighDataAssemble;
            }

            String first = assembleForm.getFirst();
            String second = assembleForm.getSecond();
            List<String> formRecords = new ArrayList<>();
            formRecords.add(first);
            formRecords.add(second);
            LocalDateTime startTime = assembleForm.getStartTime().isBefore(assembleForm.getEndTime()) ? assembleForm.getStartTime() : assembleForm.getEndTime();
            LocalDateTime endTime = assembleForm.getStartTime().isBefore(assembleForm.getEndTime()) ? assembleForm.getEndTime() : assembleForm.getStartTime();
            List<WeighDataDO> dbList = weighDataService.lambdaQuery()
                    .eq(WeighDataDO::getUid, userDO.getUid())
                    .eq(WeighDataDO::getAttributionId, attributionId)
                    .notIn(WeighDataDO::getRecordId, formRecords)
                    .eq(WeighDataDO::getTruckNo, assembleForm.getTruckNo())
                    .between(WeighDataDO::getWeighTime, startTime, endTime)
                    .orderByAsc(WeighDataDO::getWeighTime)
                    .list();
            if (CollUtil.isEmpty(dbList)) {
                return weighDataAssemble;
            }
            List<String> recordingIds = dbList.stream().map(WeighDataDO::getRecordId).collect(Collectors.toList());
            Map<String, List<String>> map = weighDataPicService.getPic(recordingIds, true);
            Map<String, List<String>> uuidMap = weighDataPicService.getUuid(recordingIds);

            List<WeighData> result = dbList.stream().map(e -> {
                WeighData weighData = new WeighData();
                BeanUtils.copyProperties(e, weighData);
                weighData.setWeighTime(DateUtil.parseLocalDateTime(e.getWeighTime()));
                if (CollUtil.isNotEmpty(map)) {
                    weighData.setPic(map.get(e.getRecordId()));
                }
                if (CollUtil.isNotEmpty(uuidMap)) {
                    weighData.setUuids(uuidMap.get(e.getRecordId()));
                }
                return weighData;
            }).collect(Collectors.toList());
            weighDataAssemble.setWeighData(result);
        }

        return weighDataAssemble;
    }

    @Override
    public PageList<WeighDataDTO> pageQuery(String appKeyHeader, QueryPage<WeighDataQuery> queryPage) {
        UserDO userDO = userService.getUserByAppKey(appKeyHeader);
        // 服务校验
        String uid = userDO.getUid();
        checkUtil.appCheck(uid, DeveloperAppEnum.QUERY);
        Page page = queryPage.getPage();
        Integer current = page.getCurrent();
        int size = page.getSize() > SdkQueryConstant.DATA_MAX_SIZE ? SdkQueryConstant.DATA_MAX_SIZE : page.getSize();
        PageList<WeighDataDTO> pageList = new PageList<>();
        pageList.setCurrent(current);
        pageList.setSize(size);
        WeighDataQuery weighDataQuery = queryPage.getT();
        // 校验
        validQueryParams(weighDataQuery);
        List<String> ids = weighDataQuery.getIds();
        LocalDateTime startTime = weighDataQuery.getStartTime();
        LocalDateTime endTime = weighDataQuery.getEndTime();
        String deviceSn = weighDataQuery.getDeviceSn();
        String attributionCode = weighDataQuery.getAttributionCode();
        List<String> truckNos = weighDataQuery.getTruckNos();
        QueryWrapper<WeighDataDO> wrapper = getWeighDataDOQueryWrapper(uid, ids, startTime, endTime, deviceSn, attributionCode, truckNos);
        IPage<WeighDataDO> pageParam = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(current, size);
        IPage<WeighDataDO> weighDataDOIPage = weighDataService.page(pageParam, wrapper);
        if (ObjectUtil.isNotNull(weighDataDOIPage) && CollectionUtil.isNotEmpty(weighDataDOIPage.getRecords())) {
            List<WeighDataDO> records = weighDataDOIPage.getRecords();
            // 记录服务使用次数--按查询到的数据量累加
            appInvocationDailyLogService.saveInvokeLog(DeveloperAppEnum.QUERY.value(), uid, -1L, records.size());
            List<WeighDataDTO> resultRecords = Lists.newArrayList();
            pageList.setTotal((int) weighDataDOIPage.getTotal());
            pageList.setPages((int) weighDataDOIPage.getPages());
            // 查询设备归属方code
            Set<Long> attributionIds = records.stream().map(WeighDataDO::getAttributionId).collect(Collectors.toSet());
            List<DeviceAttributionDO> deviceAttributionsByIds = deviceAttributionExtMapper.getDeviceAttributionsByIds(attributionIds);
            Map<Long, String> deviceAttributionMap = deviceAttributionsByIds.stream().collect(Collectors.toMap(DeviceAttributionDO::getId, DeviceAttributionDO::getCode));
            // 查询照片
            List<String> recordIds = records.stream().map(WeighDataDO::getRecordId).collect(Collectors.toList());
            Map<String, List<String>> prePicMap = weighDataPicService.getPic(recordIds, true);
            Map<String, List<String>> downloadPicMap = weighDataPicService.getPic(recordIds, false);
            Map<String, List<String>> uuidMap = weighDataPicService.getUuid(recordIds);
            for (WeighDataDO record : records) {
                WeighDataDTO weighDataDTO = getWeighDataDTO(record, deviceAttributionMap, prePicMap, downloadPicMap,attributionCode,uuidMap);
                resultRecords.add(weighDataDTO);
            }
            pageList.setDataList(resultRecords);
        }
        return pageList;
    }

    @Override
    public WeighPushDataDTO getPushData(String recordId) {
        WeighPushDataDTO weighPushDataDTO = weighDataExtMapper.needPushWeighData(recordId);
        if (ObjectUtil.isNull(weighPushDataDTO)) {
            return weighPushDataDTO;
        }
        UserBusinessConfigDO userBusinessConfigDO = userBusinessConfigService.getByUid(weighPushDataDTO.getUid());
        if (userBusinessConfigDO != null && userBusinessConfigDO.getWeighUnit() != 1) {
            WeighDataUnitEnum weighDataUnitEnum = WeighDataUnitEnum.valueOfType(userBusinessConfigDO.getWeighUnit());
            if (weighDataUnitEnum.value().equals(weighPushDataDTO.getUnit())) {
                return weighPushDataDTO;
            } else if (weighDataUnitEnum == WeighDataUnitEnum.TON && weighPushDataDTO.getUnit().equals(WeighDataUnitEnum.KG.value())) {
                weighPushDataDTO.setWeight(weighPushDataDTO.getWeight().divide(new BigDecimal(1000), 4, RoundingMode.HALF_UP));
                weighPushDataDTO.setUnit(WeighDataUnitEnum.TON.value());
                return weighPushDataDTO;
            }else if (weighDataUnitEnum == WeighDataUnitEnum.KG && weighPushDataDTO.getUnit().equals(WeighDataUnitEnum.TON.value())) {
                weighPushDataDTO.setWeight(weighPushDataDTO.getWeight().multiply(new BigDecimal(1000)).setScale(4, RoundingMode.HALF_UP));
                weighPushDataDTO.setUnit(WeighDataUnitEnum.KG.value());
                return weighPushDataDTO;
            }
        }
        return weighPushDataDTO;
    }

    public WeighPushPicDTO getPushPic(Long id) {
        WeighPushPicDTO weighPicDTO = weighDataPicExtMapper.needPushWeighPic(id);
        // 获取照片预览、真实地址
        if (ObjectUtil.isNotNull(weighPicDTO)) {
            String ossId = weighPicDTO.getFileId();
            DownloadUrlOptionsDto options = new DownloadUrlOptionsDto();
            Map<String, String> previewMap = new HashMap<>();
            Map<String, String> downloadMap = new HashMap<>();
            FileIdentityDto dto = new FileIdentityDto();
            dto.setFileUuid(ossId);
            List<FileIdentityDto> fileIdentities = CollUtil.toList(dto);
            LocalDate currentDate = LocalDate.now();
            LocalDate futureDate = currentDate.plusYears(10);
            Date date = Date.from(futureDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
            options.setTime(date);

            // 预览地址
            options.setPreviewUrl(true);
            Map<FileIdentityDto, String> preview = fileCenterService.acquireFileDownloadUrls(fileIdentities, options);
            if (CollUtil.isNotEmpty(preview)) {
                preview.forEach((k, v) -> previewMap.put(k.getFileUuid(), v));
            }

            // 真实地址
            options.setPreviewUrl(false);
            Map<FileIdentityDto, String> download = fileCenterService.acquireFileDownloadUrls(fileIdentities, options);
            if (CollUtil.isNotEmpty(download)) {
                download.forEach((k, v) -> downloadMap.put(k.getFileUuid(), v));
            }

            weighPicDTO.setPreviewUrl(previewMap.get(weighPicDTO.getFileId()));
            weighPicDTO.setDownloadUrl(downloadMap.get(weighPicDTO.getFileId()));
        }
        return weighPicDTO;
    }

    @Override
    public boolean PushDataConfirm(String recordId) {
        WeighDataDO one = weighDataService.lambdaQuery()
                .eq(WeighDataDO::getRecordId, recordId)
                .one();
        if (ObjectUtil.isNotNull(one)) {
            // 数据确认
            weighDataService.lambdaUpdate()
                    .eq(WeighDataDO::getRecordId, recordId)
                    .set(WeighDataDO::getPushStatus, PushStatusEnum.PUSH.getVal())
                    .update();
            // 日志
            pushLogService.createLog(one.getUid(), recordId + "----称重数据", PushLogStatusEnum.THREE.value(), null);
            return true;
        }

        return false;
    }

    @Override
    public boolean PushPicConfirm(Long id) {
        WeighDataPicDO one = weighDataPicService.lambdaQuery()
                .eq(WeighDataPicDO::getId, id)
                .one();
        if (ObjectUtil.isNotNull(one)) {
            // 照片确认
            weighDataPicService.lambdaUpdate()
                    .eq(WeighDataPicDO::getId, id)
                    .set(WeighDataPicDO::getPushStatus, PushStatusEnum.PUSH.getVal())
                    .update();
            // 日志
            pushLogService.createLog(one.getUid(), one.getRecordId() + "----"+ id + "----照片", PushLogStatusEnum.THREE.value(), null);
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String matchModule(String appKeyHeader, MatchForm form) {
        UserDO userDO = userService.getUserByAppKey(appKeyHeader);
        DeviceAttributionDO one = deviceAttributionService.lambdaQuery()
                .eq(DeviceAttributionDO::getUid, userDO.getUid())
                .apply("find_in_set({0},code)",form.getAttributionCode())
                .one();
        // 归属方校验
        if (ObjectUtil.isNull(one)) {
            throw new BizErrorException(BizExceptionMessageEnum.ATTRIBUTION_IS_ERROR);
        }
        return ocrService.ocrMatch(one, form);
    }

    @Override
    public String ocr(PicForm form) {
        try {
            // ocr服务
            return HttpUtil.post(ocrUrl, JSON.toJSONString(form));
        } catch (Exception e) {
            throw new BizErrorException("-233","OCR识别服务调用失败");
        }
    }

    @Override
    public List<String> getPicUrl( PicForm form) {
        if (StrUtil.isNotBlank(form.getPic())) {
            LocalDate currentDate = LocalDate.now();
            // 将当前日期增加 10 年
            LocalDate futureDate = currentDate.plusYears(10);
            Date date = Date.from(futureDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
            return fileOssService.getUrlByUuidAndTime(StrUtil.split(form.getPic(),","), date, form.isPre());
        }

        return null;
    }

    private WeighDataDTO getWeighDataDTO(WeighDataDO record, Map<Long, String> deviceAttributionMap, Map<String, List<String>> prePicMap, Map<String, List<String>> downloadPicMap,String attributionCode,Map<String, List<String>> uuidMap) {
        String recordId = record.getRecordId();
        Long attributionId = record.getAttributionId();
        WeighDataDTO weighDataDTO = new WeighDataDTO();
        weighDataDTO.setId(record.getId());
        weighDataDTO.setGmtCreate(DateUtil.parseLocalDateTime(record.getGmtCreate()));
        weighDataDTO.setGmtModify(DateUtil.parseLocalDateTime(record.getGmtModify()));
        weighDataDTO.setCreateId(record.getCreateId());
        weighDataDTO.setModifyId(record.getModifyId());
        weighDataDTO.setRecordId(recordId);
        weighDataDTO.setUid(record.getUid());
        weighDataDTO.setAttributionId(attributionId);
        String code = attributionCodeShowUtil.choose(attributionId, deviceAttributionMap.get(attributionId), attributionCode);
        weighDataDTO.setAttributionCode(code);
        weighDataDTO.setDeviceSn(record.getDeviceSn());
        weighDataDTO.setTruckNo(record.getTruckNo());
        weighDataDTO.setLprTruckNo(record.getLprTruckNo());
        weighDataDTO.setMaterial(record.getMaterial());
        weighDataDTO.setWeight(record.getWeight());
        weighDataDTO.setUnit(record.getUnit());
        weighDataDTO.setType(record.getType());
        weighDataDTO.setRiskGrade(record.getRiskGrade());
        weighDataDTO.setWeighTime(DateUtil.parseLocalDateTime(record.getWeighTime()));
        weighDataDTO.setPic(prePicMap.get(recordId));
        weighDataDTO.setUrls(downloadPicMap.get(recordId));
        weighDataDTO.setUuids(uuidMap.get(recordId));
        return weighDataDTO;
    }

    private QueryWrapper<WeighDataDO> getWeighDataDOQueryWrapper(String uid, List<String> ids, LocalDateTime startTime, LocalDateTime endTime, String deviceSn, String attributionCode, List<String> truckNos) {
        QueryWrapper<WeighDataDO> wrapper = new QueryWrapper<>();
        // 租户
        wrapper.lambda().eq(WeighDataDO::getUid, uid);
        // 称重记录ID
        if (CollectionUtil.isNotEmpty(ids)) {
            wrapper.lambda().in(WeighDataDO::getRecordId, ids);
        } else {
            // 时间范围
            wrapper.lambda().between(WeighDataDO::getWeighTime, startTime, endTime);
        }
        // 设备机器码
        if (StringUtils.isNotBlank(deviceSn)) {
            wrapper.lambda().eq(WeighDataDO::getDeviceSn, deviceSn);
        }
        // 设备归属方code
        if (StringUtils.isNotBlank(attributionCode)) {
            Long deviceAttributionId = deviceAttributionExtMapper.getDeviceAttributionIdByCode(uid, attributionCode);
            wrapper.lambda().eq(WeighDataDO::getAttributionId, deviceAttributionId);
        }
        // 车牌号
        if (CollectionUtil.isNotEmpty(truckNos)) {
            wrapper.lambda().in(WeighDataDO::getTruckNo, truckNos);
        }
        wrapper.lambda().orderByDesc(WeighDataDO::getWeighTime);
        return wrapper;
    }

    private void validQueryParams(WeighDataQuery weighDataQuery) {
        if (ObjectUtil.isNull(weighDataQuery)) {
            throw new BizErrorException(BizExceptionMessageEnum.QUERY_PARAM_NULL_ERROR);
        }
        List<String> ids = weighDataQuery.getIds();
        LocalDateTime startTime = weighDataQuery.getStartTime();
        LocalDateTime endTime = weighDataQuery.getEndTime();
        List<String> truckNos = weighDataQuery.getTruckNos();
        if (CollectionUtil.isNotEmpty(ids)) {
            ids = ids.stream().distinct().collect(Collectors.toList());
            int size = ids.size();
            if (size > SdkQueryConstant.DATA_MAX_SIZE) {
                throw new BizErrorException(BizExceptionMessageEnum.BEYOND_MAX_SIZE_ERROR);
            }
            weighDataQuery.setIds(ids);
        } else if (ObjectUtil.isNull(startTime)) {
            throw new BizErrorException(BizExceptionMessageEnum.QUERY_TIME_ERROR);
        }
        if (ObjectUtil.isNull(endTime)) {
            weighDataQuery.setEndTime(LocalDateTime.now());
        }
        if (CollectionUtil.isNotEmpty(truckNos)) {
            truckNos = truckNos.stream().distinct().collect(Collectors.toList());
            int size = truckNos.size();
            if (size > SdkQueryConstant.TRUCK_NO_MAX_SIZE) {
                throw new BizErrorException(BizExceptionMessageEnum.TRUCK_NO_MAX_SIZE_ERROR);
            }
            weighDataQuery.setTruckNos(truckNos);
        }
    }

    @Override
    public boolean pushReceiptRecycleConfirm(Long id) {
        ReceiptRecycleDO one = receiptRecycleService.getById(id);
        if (ObjectUtil.isNotNull(one)) {
            // 数据确认
            receiptRecycleService.lambdaUpdate()
                    .eq(ReceiptRecycleDO::getId, one.getId())
                    .set(ReceiptRecycleDO::getPushStatus, PushStatusEnum.PUSH.getVal())
                    .update();
            // 日志
            pushLogService.createLog(one.getUid(), "单据回收数据-" + id, PushLogStatusEnum.THREE.value(), null);
            return true;
        }
        return false;
    }
}
