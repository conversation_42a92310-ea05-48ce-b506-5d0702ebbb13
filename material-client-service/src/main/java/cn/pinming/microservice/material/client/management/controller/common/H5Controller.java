package cn.pinming.microservice.material.client.management.controller.common;

import cn.pinming.microservice.material.client.management.common.form.DeliveryAddSelfCheckForm;
import cn.pinming.microservice.material.client.management.common.form.OCRDeliveryAddForm;
import cn.pinming.microservice.material.client.management.common.vo.SupplierConfigVO;
import cn.pinming.microservice.material.client.management.common.vo.h5.SimpleDeliveryVO;
import cn.pinming.microservice.material.client.management.common.vo.h5.SimplePurchaseVO;
import cn.pinming.microservice.material.client.management.infrastructure.response.SingleResponse;
import cn.pinming.microservice.material.client.management.service.biz.IDeliveryService;
import cn.pinming.microservice.material.client.management.service.biz.IPurchaseOrderService;
import cn.pinming.microservice.material.client.management.service.biz.ISelfQrcodeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(value = "h5", tags = {"h5"})
@RestController
@RequestMapping("/api/h5")
public class H5Controller {

    @Resource
    private ISelfQrcodeService selfQrcodeService;
    @Resource
    private IPurchaseOrderService purchaseOrderService;
    @Resource
    private IDeliveryService deliveryService;

    @ApiOperation(value = "扫码获取供应商列表", nickname = "qrcodeSupplierList", response = SupplierConfigVO.class)
    @GetMapping("/supplier/list")
    public SingleResponse<?> supplierList(@RequestParam String bizId) {
        List<SupplierConfigVO> list = selfQrcodeService.listSupplierConfig(bizId);
        return SingleResponse.of(list);
    }

    /**
     * 获取发货单列表
     *
     * @param supplierExtId 供应商id
     * @param attributionId 归属方id(来自二维码)
     */
    @ApiOperation(value = "获取发货单列表", nickname = "qrcodePurchaseList", response = SimplePurchaseVO.class)
    @GetMapping("/purchase/list")
    public SingleResponse<?> purchaseList(@RequestParam String supplierExtId, @RequestParam Long attributionId) {
        List<SimplePurchaseVO> list = purchaseOrderService.listPurchaseOrder(supplierExtId, attributionId);
        return SingleResponse.of(list);
    }

    @ApiOperation(value = "获取发货单明细", nickname = "qrcodePurchaseDetail", response = SimplePurchaseVO.class)
    @GetMapping("/purchase/detail")
    public SingleResponse<?> purchaseDetail(@RequestParam Long purchaseId, @RequestParam Long attributionId) {
        SimplePurchaseVO result = purchaseOrderService.getPurchaseOrder(purchaseId, attributionId);
        return SingleResponse.of(result);
    }

    @ApiOperation(value = "保存发货单(运单)", nickname = "h5DeliveryAdd")
    @PostMapping("/delivery/add")
    public SingleResponse<Long> deliveryAdd(@Validated @RequestBody DeliveryAddSelfCheckForm form) {
        Long deliveryId = deliveryService.h5Add(form);
        return SingleResponse.of(deliveryId);
    }

    @ApiOperation(value = "运单详情", nickname = "h5DeliveryDetail", response = SimpleDeliveryVO.class)
    @GetMapping("/delivery/{deliveryId}/detail")
    public SingleResponse<SimpleDeliveryVO> deliveryAdd(@PathVariable Long deliveryId) {
        SimpleDeliveryVO result = deliveryService.h5DetailByDeliveryId(deliveryId);
        return SingleResponse.of(result);
    }

    @ApiOperation(value = "保存OCR发货单(运单)", nickname = "h5OCRDeliveryAdd")
    @PostMapping("/ocr/delivery/add")
    public SingleResponse<String> ocrDeliveryAdd(@Validated @RequestBody OCRDeliveryAddForm form) {
        String deliveryNo = deliveryService.deliveryAdd(form.getAttributionId(), 2, null);
        return SingleResponse.of(deliveryNo);
    }

}
