package cn.pinming.microservice.material.client.management.common.enums;


import lombok.Getter;

@Getter
public enum RecycleStatusEnum {
    //回收状态(1-待回收、2-回收成功、3-回收失败)
    WAIT(1, "待回收"),
    SUCCESS(2, "回收成功"),
    FAIL(3, "回收失败"),
    INVALID(4, "已作废"),
    PROCESSING(5, "处理中"),
    ;

    private final Integer value;
    private final String desc;

    RecycleStatusEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

}
