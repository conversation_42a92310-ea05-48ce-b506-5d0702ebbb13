package cn.pinming.microservice.material.client.management.common.mapper;

import cn.pinming.microservice.material.client.management.common.model.MsgRobotConfigDO;
import cn.pinming.microservice.material.client.management.common.query.MsgRobotPageQuery;
import cn.pinming.microservice.material.client.management.common.vo.MsgRobotVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 消息机器人配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
public interface MsgRobotConfigMapper extends BaseMapper<MsgRobotConfigDO> {

    Page<MsgRobotVO> selectPageByQuery(@Param("query") MsgRobotPageQuery query);

    int selectCountByMsgRobotId(@Param("id") Long id);
}
