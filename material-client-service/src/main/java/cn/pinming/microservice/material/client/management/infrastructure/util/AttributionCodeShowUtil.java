package cn.pinming.microservice.material.client.management.infrastructure.util;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.microservice.material.client.management.common.BaseDO;
import cn.pinming.microservice.material.client.management.common.model.DeviceAttributionDO;
import cn.pinming.microservice.material.client.management.service.biz.DeviceAttributionService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class AttributionCodeShowUtil {
    @Resource
    private DeviceAttributionService deviceAttributionService;

    public String choose(Long attributionId, String dbCode, String queryCode) {
        if (StrUtil.isNotBlank(queryCode)) {
            return StrUtil.split(dbCode, ",").stream().filter(e -> e.equals(queryCode)).collect(Collectors.toList()).get(0);
        }


        DeviceAttributionDO one = deviceAttributionService.lambdaQuery()
                .select(DeviceAttributionDO::getPrimaryCode)
                .eq(BaseDO::getId, attributionId)
                .one();
        if (ObjUtil.isNotNull(one)) {
            return one.getPrimaryCode();
        }

        return dbCode;
    }
}
