package cn.pinming.microservice.material.client.management.common.vo.purchase;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class PurchaseVO {

    private Long id;

    /**
     * 业务系统订单id
     */
    private String orderExtId;

    /**
     * 采购类目
     */
    private String materialCategory;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 采购方
     */
    private String project;

    /**
     * 采购单状态 1 启用 2 弃用
     */
    private Integer status;

    /**
     * 订单同步时间
     */
    private LocalDateTime gmtModify;

    /**
     * 发货状态 1 待发货 2 发货中 3 发货完毕
     */
    private Integer deliveryStatus;

    /**
     * 计划使用部位
     */
    private String position;
}
