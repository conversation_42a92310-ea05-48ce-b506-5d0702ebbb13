package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 单据回收推送配置
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("d_receipt_push_config")
public class ReceiptPushConfigDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 用户id
     */
    private String uid;

    /**
     * 推送类型 1 sdk推送 2 一般推送
     */
    private Integer pushType;

    /**
     * 数据推送地址
     */
    private String pushUrl;

    /**
     * 数据推送开关 1 关闭 2 开启
     */
    private Integer pushStatus;

    /**
     * 排除的数据归属方id
     */
    private String excludedAttributionIds;

    /**
     * 请求地址
     */
    private String endpoint;

    /**
     * 请求Header
     */
    private String requestHeader;

    /**
     * 请求模式(none-无、signature-加签)
     */
    private String requestMode;

    /**
     * 应用key
     */
    private String appKey;

    /**
     * 应用密钥key
     */
    private String appSecretKey;

    /**
     * 签名模板
     */
    private String signatureTemplate;

    /**
     * 签名算法
     */
    private String signatureAlgorithm;

    /**
     * 返回类型(JSONObject、JSONArray、String)
     */
    private String responseType;

    /**
     * 返回标识(key=value)
     */
    private String responseFlag;


}
