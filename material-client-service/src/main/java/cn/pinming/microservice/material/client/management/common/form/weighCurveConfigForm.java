package cn.pinming.microservice.material.client.management.common.form;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class weighCurveConfigForm {
    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "归属方id")
    @NotNull(message = "归属方id不能为空")
    private Long attributionId;

    @ApiModelProperty(value = "设备sn")
    @NotBlank(message = "设备sn不能为空")
    private String deviceSn;

    @ApiModelProperty(value = "平台期时长 秒")
    private Integer platformDuration;

    @ApiModelProperty(value = "持续时长 秒")
    private Integer sustainDuration;

    @ApiModelProperty(value = "平台期个数")
    private Integer platformCount;

    @ApiModelProperty(value = "称重最大值 kg")
    private BigDecimal weight;

    @ApiModelProperty(value = "防控仪健康周期 秒")
    private Integer alarmTime;
}
