package cn.pinming.microservice.material.client.management.common.mapper.ext;

import cn.pinming.microservice.material.client.management.common.model.OcrModuleDO;
import cn.pinming.microservice.material.client.management.common.query.OCRModuleQuery;
import cn.pinming.microservice.material.client.management.common.vo.ReceiptModuleDetailConfigVO;
import cn.pinming.microservice.material.client.management.common.vo.ocr.OCRConvertVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OCRModuleExtMapper {
    List<OcrModuleDO> selectList(@Param("query") OCRModuleQuery query);

    List<OCRConvertVO> selectModuleList(@Param("uid") String uid, @Param("attributionId") Long attributionId, @Param("billMatchType") Byte billMatchType);

    List<ReceiptModuleDetailConfigVO> recycleModuleDetailShow(@Param("moduleId") Long moduleId, @Param("type") Byte type);

    int checkMainPoint(@Param("uid") String uid, @Param("id") Long id, @Param("mainPointKeyName") String mainPointKeyName);
}
