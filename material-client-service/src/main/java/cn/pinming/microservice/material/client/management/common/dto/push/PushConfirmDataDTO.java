package cn.pinming.microservice.material.client.management.common.dto.push;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class PushConfirmDataDTO {
    @ApiModelProperty(value = "车牌号")
    private String truckNo;

    @ApiModelProperty(value = "称重类型 1-收料过磅；2-发料过磅")
    private Integer weighingType;

    @ApiModelProperty(value = "签名人照片下载地址(请及时下载，会过期)")
    private String signerPhotoUrl;

    @ApiModelProperty(value = "签字照片下载地址(请及时下载，会过期)")
    private String signaturePicUrl;

    @ApiModelProperty(value = "送货单照片下载地址(请及时下载，会过期)")
    private List<String> waybillPhotoUrls;

    @ApiModelProperty(value = "换算结果")
    private PushConfirmConvertResultDTO convertResult;

    @ApiModelProperty(value = "称重数据")
    private PushConfirmWeightDTO weightResult;
}
