package cn.pinming.microservice.material.client.management.common.mapper;

import cn.pinming.microservice.material.client.management.common.model.AppInvocationDailyLogDO;
import cn.pinming.microservice.material.client.management.common.vo.AppInvocationVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 服务调用表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-07
 */
public interface AppInvocationDailyLogMapper extends BaseMapper<AppInvocationDailyLogDO> {

    List<AppInvocationVO> selectExtAppInvocation(@Param("uid") String uid, @Param("appId") Long appId);

    Long sumUsedApiTotal(@Param("uid") String uid, @Param("appIdList") List<Long> appIdList);
}
