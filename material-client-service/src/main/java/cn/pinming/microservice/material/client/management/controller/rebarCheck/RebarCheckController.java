package cn.pinming.microservice.material.client.management.controller.rebarCheck;

import cn.hutool.core.util.ObjectUtil;
import cn.pinming.microservice.material.client.management.common.enums.VerifyEnum;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizErrorException;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizExceptionMessageEnum;
import cn.pinming.microservice.material.client.management.infrastructure.response.SingleResponse;
import cn.pinming.microservice.material.client.management.common.BaseDO;
import cn.pinming.microservice.material.client.management.common.form.RebarCheckDetailForm;
import cn.pinming.microservice.material.client.management.common.form.RebarCheckForm;
import cn.pinming.microservice.material.client.management.common.model.CheckDO;
import cn.pinming.microservice.material.client.management.common.model.CheckDetailDO;
import cn.pinming.microservice.material.client.management.common.model.CheckInfoDO;
import cn.pinming.microservice.material.client.management.common.model.RebarMaterialDO;
import cn.pinming.microservice.material.client.management.common.query.CheckQuery;
import cn.pinming.microservice.material.client.management.common.vo.CheckDetailVO;
import cn.pinming.microservice.material.client.management.common.vo.CheckVO;
import cn.pinming.microservice.material.client.management.service.biz.CheckDetailService;
import cn.pinming.microservice.material.client.management.service.biz.CheckInfoService;
import cn.pinming.microservice.material.client.management.service.biz.CheckService;
import cn.pinming.microservice.material.client.management.service.biz.RebarMaterialService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(value = "钢筋小助手-controller", tags = {"rebarCheck"})
@RestController
@RequestMapping("/api/rebarCheck")
public class RebarCheckController {
    @Resource
    private CheckService checkService;
    @Resource
    private CheckInfoService checkInfoService;
    @Resource
    private CheckDetailService checkDetailService;
    @Resource
    private RebarMaterialService rebarMaterialService;

    @ApiOperation(value = "新增验收", responseReference = "SingleResponse«Long»", nickname = "rebarCheckAdd")
    @PostMapping("/add")
    public SingleResponse<Long> rebarCheckAdd(@RequestBody RebarCheckForm form) {
        Long id = checkService.rebarCheckAdd(form);
        return SingleResponse.of(id);
    }

    @ApiOperation(value = "校验复核-各规格自洽校验", responseReference = "SingleResponse«?»", nickname = "rebarCheckFirst")
    @GetMapping("/first/{id}")
    public SingleResponse<?> rebarCheckFirst(@PathVariable("id")Long id) {
        checkService.rebarCheckFirst(id);
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "校验复核-反向复核", responseReference = "SingleResponse«?»", nickname = "rebarCheckThree")
    @GetMapping("/second/{id}")
    public SingleResponse<?> rebarCheckSecond(@PathVariable("id")Long id) {
        checkService.rebarCheckSecond(id);
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "编辑反向复核总重使用(实点+实称)/总重确认(实称)", responseReference = "SingleResponse«?»", nickname = "reverseWeightTypeUpdate")
    @GetMapping("/reverseWeightType/{id}/{reverseWeightType}")
    @Transactional(rollbackFor = Exception.class)
    public SingleResponse<?> reverseWeightTypeUpdate(@PathVariable("id")Long id,@PathVariable("reverseWeightType")Integer reverseWeightType) {
        CheckDO one = checkService.lambdaQuery()
                .eq(BaseDO::getId, id)
                .eq(CheckDO::getIsVerify, VerifyEnum.YES.getValue())
                .one();
        if (ObjectUtil.isNotNull(one)) {
            throw new BizErrorException(BizExceptionMessageEnum.HAS_VERIFY);
        }
        checkInfoService.lambdaUpdate()
                .eq(CheckInfoDO::getCheckId,id)
                .set(CheckInfoDO::getReverseWeightType,reverseWeightType)
                .update();

        checkDetailService.lambdaUpdate()
                .eq(CheckDetailDO::getCheckId,id)
                .set(CheckDetailDO::getReverseTheoryAmount,null)
                .set(CheckDetailDO::getReverseTheoryWeight,null)
                .set(CheckDetailDO::getReverseTheoryWeightDif,null)
                .set(CheckDetailDO::getReverseTheoryAmountDif,null)
                .set(CheckDetailDO::getReverseTheoryWeightResult,null)
                .set(CheckDetailDO::getReverseTheoryAmountResult,null)
                .set(CheckDetailDO::getConfirmAmount,null)
                .set(CheckDetailDO::getConfirmWeight,null)
                .update();
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "点选确认", responseReference = "SingleResponse«?»", nickname = "chooseConfirm")
    @PostMapping("/chooseConfirm/{id}")
    public SingleResponse<?> chooseConfirm(@PathVariable("id")Long id,@RequestBody List<RebarCheckDetailForm> list) {
        checkService.chooseConfirm(id,list);
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "编辑车牌及影像资料", responseReference = "SingleResponse«?»", nickname = "truckOrPicUpdate")
    @PostMapping("/truckOrPicUpdate")
    public SingleResponse<?> truckOrPicUpdate(@RequestBody RebarCheckForm form) {
        checkService.truckOrPicUpdate(form);
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "修改运单数据", responseReference = "SingleResponse«?»", nickname = "complexUpdate")
    @PostMapping("/complexUpdate")
    public SingleResponse<?> complexUpdate(@RequestBody RebarCheckForm form) {
        checkService.complexUpdate(form);
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "列表", responseReference = "SingleResponse«IPage<CheckVO>»", nickname = "checkList")
    @PostMapping("/checkList")
    public SingleResponse<IPage<CheckVO>> checkList(@RequestBody CheckQuery query) {
        IPage<CheckVO> list = checkService.checkList(query);
        return SingleResponse.of(list);
    }

    @ApiOperation(value = "详情", responseReference = "SingleResponse«CheckDetailVO»", nickname = "checkDetail")
    @GetMapping("/checkDetail/{id}")
    public SingleResponse<CheckDetailVO> checkDetail(@PathVariable("id")Long id) {
        CheckDetailVO vo = checkService.checkDetail(id);
        return SingleResponse.of(vo);
    }

    @ApiOperation(value = "材料列表", responseReference = "SingleResponse«List<RebarMaterialDO>»", nickname = "rebarMaterialList")
    @GetMapping("/rebarMaterialList")
    public SingleResponse<List<RebarMaterialDO>> rebarMaterialList(RebarMaterialDO query) {
        List<RebarMaterialDO> list = rebarMaterialService.rebarMaterialList(query);
        return SingleResponse.of(list);
    }
}
