package cn.pinming.microservice.material.client.management.infrastructure.strategy;

import cn.hutool.core.collection.CollUtil;
import cn.pinming.microservice.material.client.management.common.dto.push.PushDataDTO;
import cn.pinming.microservice.material.client.management.common.dto.push.PushDataPicDTO;
import cn.pinming.microservice.material.client.management.common.dto.push.PushWeighDataDTO;
import cn.pinming.microservice.material.client.management.common.enums.SdkPushTypeEnum;
import cn.pinming.microservice.material.client.management.common.mapper.ext.WeighDataExtMapper;
import cn.pinming.microservice.material.client.management.common.model.WeighDataDO;
import cn.pinming.microservice.material.client.management.common.model.WeighDataPicDO;
import cn.pinming.microservice.material.client.management.common.vo.push.PushRouteConfigVO;
import cn.pinming.microservice.material.client.management.service.biz.WeighDataPicService;
import cn.pinming.microservice.material.client.management.service.biz.WeighDataService;
import cn.pinming.microservice.material.client.management.service.push.sdk.dto.SdkRespDTO;
import cn.pinming.zhjz.cxptz.microservice.filecenter.api.FileCenterService;
import cn.pinming.zhjz.cxptz.microservice.filecenter.api.model.DownloadUrlOptionsDto;
import cn.pinming.zhjz.cxptz.microservice.filecenter.api.model.FileIdentityDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component("DATA")
public class DataSdkPushStrategy extends AbstractSkdPushStrategy<PushDataDTO> {
    @Resource
    private WeighDataService weighDataService;
    @Resource
    private WeighDataExtMapper weighDataExtMapper;
    @Resource
    private WeighDataPicService weighDataPicService;
    @DubboReference
    private FileCenterService fileCenterService;

    @Override
    Long getRouteConfigId() {
        return SdkPushTypeEnum.DATA.getVal();
    }

    @Override
    void afterCompletion(SdkRespDTO result, List<PushDataDTO> pushedData) {
        if (CollUtil.isNotEmpty(pushedData) && CollUtil.isNotEmpty(result.getData())) {
            try {
                Set<String> pushedRecordIds = pushedData.stream()
                        .map(PushDataDTO::getRecordId)
                        .collect(Collectors.toSet());

                Set<String> confirmedRecordIds = result.getData().stream()
                        .map(Object::toString)
                        .collect(Collectors.toSet());

                Set<String> validRecordIds = pushedRecordIds.stream()
                        .filter(confirmedRecordIds::contains)
                        .collect(Collectors.toSet());

                if (CollUtil.isNotEmpty(validRecordIds)) {
                    log.info("[称重数据推送成功] 推送数量: {}, 确认数量: {}, 有效更新数量: {}, 有效RecordIDs: {}",
                            pushedRecordIds.size(), confirmedRecordIds.size(), validRecordIds.size(), validRecordIds);

                    weighDataService.lambdaUpdate()
                            .in(WeighDataDO::getRecordId, validRecordIds)
                            .set(WeighDataDO::getPushStatus, 3)
                            .update();
                } else {
                    log.warn("[称重数据推送异常] 推送的数据与确认的数据无交集, 推送RecordIDs: {}, 确认RecordIDs: {}", pushedRecordIds, confirmedRecordIds);
                }
            } catch (Exception e) {
                log.error("[称重数据推送异常] 处理返回数据失败", e);
            }
        }
    }

    @Override
    Map<String, List<PushDataDTO>> queryDataListMap(PushRouteConfigVO config, Map<Long, String> attributionCodeMap) {
        Map<String, List<PushDataDTO>> result = new HashMap<>();
        //查询归属方
        String uid = config.getUid();
        Set<Long> attributionCodeList = attributionCodeMap.keySet();

        // 查询原始称重记录
        List<PushWeighDataDTO> list = weighDataExtMapper.selectDataToPush(uid, attributionCodeList);
        if (CollUtil.isNotEmpty(list)) {
            Map<String, List<PushDataPicDTO>> weighDataIdPicMap = new HashMap<>();

            Map<Long, List<PushWeighDataDTO>> attributionMap = list.stream().collect(Collectors.groupingBy(PushWeighDataDTO::getAttributionId));
            List<String> recordIdList = list.stream().map(PushWeighDataDTO::getRecordId).collect(Collectors.toList());
            List<WeighDataPicDO> recordPicList = weighDataPicService.lambdaQuery()
                    .in(WeighDataPicDO::getRecordId, recordIdList)
                    .list();
            if (CollUtil.isNotEmpty(recordPicList)) {
                Map<String, String> recordPicUuidList = recordPicList.stream().collect(Collectors.toMap(WeighDataPicDO::getFileId, WeighDataPicDO::getFilePath));
                if (CollUtil.isNotEmpty(recordPicUuidList)) {
                    DownloadUrlOptionsDto options = new DownloadUrlOptionsDto();
                    options.setTime(Date.from(LocalDate.now().plusYears(10).atStartOfDay(ZoneId.systemDefault()).toInstant()));
                    List<FileIdentityDto> fileIdentities = recordPicUuidList.keySet().stream().map(e -> {
                        FileIdentityDto dto = new FileIdentityDto();
                        dto.setFileUuid(e);
                        return dto;
                    }).collect(Collectors.toList());
                    Map<FileIdentityDto, String> downloadDtoMap = fileCenterService.acquireFileDownloadUrls(fileIdentities, options);
                    if (CollUtil.isNotEmpty(downloadDtoMap)) {
                        Map<String, String> downloadMap = new HashMap<>();
                        downloadDtoMap.forEach((k, url) -> downloadMap.put(k.getFileUuid(), url));
                        Map<String, List<WeighDataPicDO>> collect = recordPicList.stream().collect(Collectors.groupingBy(WeighDataPicDO::getRecordId));
                        collect.forEach((recordId, v) -> {
                            List<PushDataPicDTO> picDTOList = new ArrayList<>();
                            if (CollUtil.isNotEmpty(v)) {
                                v.forEach(t->{
                                    String fileId = t.getFileId();
                                    if (downloadMap.containsKey(fileId)) {
                                        PushDataPicDTO picDTO = new PushDataPicDTO();
                                        picDTO.setPic(downloadMap.get(fileId));
                                        String[] split = t.getFilePath().split("/");
                                        picDTO.setCamNo(split[2]);
                                        picDTOList.add(picDTO);
                                    }
                                });
                            }
                            weighDataIdPicMap.put(recordId, picDTOList);
                        });
                    }
                }
            }

            attributionMap.forEach((id, v) -> {
                if (attributionCodeMap.containsKey(id)) {
                    List<PushDataDTO> collect = v.stream().map(e -> {
                        PushDataDTO dto = new PushDataDTO();
                        BeanUtils.copyProperties(e, dto);
                        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                        dto.setWeighTime(e.getWeighTime().format(dateTimeFormatter));
                        if (CollUtil.isNotEmpty(weighDataIdPicMap)) {
                            dto.setPicList(weighDataIdPicMap.get(dto.getRecordId()));
                        }
                        return dto;
                    }).collect(Collectors.toList());
                    result.put(attributionCodeMap.get(id), collect);
                }
            });
        }
        return result;
    }

//    public static void main(String[] args) {
//        String filePath = "./WeighPhotos/1#/20230706/200856.jpg";
//        String[] split = filePath.split("/");
//        String camNo = split[2];
//        System.out.println(camNo);
//
//
//
//    }
}
