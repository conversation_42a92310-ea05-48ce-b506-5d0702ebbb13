package cn.pinming.microservice.material.client.management.common.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
public class RebarMaterialCompareDTO {
    @ApiModelProperty(value = "材料id")
    private Long materialId;

    @ApiModelProperty(value = "单根长度(米)")
    private BigDecimal length;


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        RebarMaterialCompareDTO that = (RebarMaterialCompareDTO) o;
        return Objects.equals(materialId, that.materialId) && Objects.equals(length, that.length);
    }

    @Override
    public int hashCode() {
        return Objects.hash(materialId, length);
    }
}
