package cn.pinming.microservice.material.client.management.common.enums;


import cn.pinming.microservice.material.client.management.common.vo.KeyValueVO;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Getter
public enum ReceiptFailTypeEnum {

    DISABLED((byte) 1, "称重组装被阻断"),
    COUNT_ERROR((byte) 2, "称重记录数量错误"),
    UNRECOGNIZED_TICKET((byte) 3, "未识别到称重小票"),
    UNMATCHED_TEMPLATE((byte) 4, "未匹配到单据模板"),
    KEY_FIELD_ERROR((byte) 5, "单据关键字段值识别不全"),
    MATCH_MULTIPLE((byte) 6, "匹配到多个单据"),
    CONVERT_ERROR((byte) 7, "数值型字段转换失败"),
    ;

    private final Byte value;
    private final String desc;

    ReceiptFailTypeEnum(Byte value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static List<KeyValueVO> all() {
        return Arrays.stream(values()).map(e -> new KeyValueVO(e.getValue(), e.getDesc())).collect(Collectors.toList());
    }

    public static List<String> getDescList(String recycleFailTypes) {
        return Arrays.stream(recycleFailTypes.split(","))
                .map(type -> Arrays.stream(values()).filter(e -> e.getValue().equals(Byte.valueOf(type))).map(ReceiptFailTypeEnum::getDesc).findFirst().orElse(null))
                .filter(Objects::nonNull).collect(Collectors.toList());
    }

}
