package cn.pinming.microservice.material.client.management.common.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
public enum MsgRobotTypeEnum {

    M1(0, "作弊报警通知"),
    ;

    public static final Map<Integer, String> KEY_MAP = Arrays.stream(values()).collect(Collectors.toMap(MsgRobotTypeEnum::getVal, MsgRobotTypeEnum::getDesc));

    public static final Map<String, Integer> VAL_MAP = Arrays.stream(values()).collect(Collectors.toMap(MsgRobotTypeEnum::getDesc, MsgRobotTypeEnum::getVal));

    private final Integer val;

    private final String desc;

    MsgRobotTypeEnum(Integer val, String desc) {
        this.val = val;
        this.desc = desc;
    }
}
