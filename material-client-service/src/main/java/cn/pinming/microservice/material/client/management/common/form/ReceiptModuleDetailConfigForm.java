package cn.pinming.microservice.material.client.management.common.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class ReceiptModuleDetailConfigForm {
    @ApiModelProperty(value = "模版明细id")
    @NotNull(message = "模版明细id不能为空")
    private Long moduleDetailId;

    @ApiModelProperty(value = "数据组顺序")
    private Integer groupOrder;

    @ApiModelProperty(value = "键值顺序")
    private Integer keyOrder;

    @ApiModelProperty(value = "业务类型 1 单据匹配显示设置 2 单据回收必须键值")
    @NotNull(message = "业务类型不能为空")
    private Byte type;
}
