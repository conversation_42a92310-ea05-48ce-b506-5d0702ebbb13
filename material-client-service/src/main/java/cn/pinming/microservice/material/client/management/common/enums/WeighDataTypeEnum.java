package cn.pinming.microservice.material.client.management.common.enums;

public enum WeighDataTypeEnum {
    FIRST((byte) 1, "载车称重"),
    SECOND((byte) 2, "净货称重");

    private byte type;
    private String description;

    WeighDataTypeEnum(byte type, String description) {
        this.type = type;
        this.description = description;
    }

    public byte value() {
        return type;
    }

    public String description() {
        return description;
    }

    public static String desc(Byte value) {
        for (WeighDataTypeEnum statusEnum : WeighDataTypeEnum.values()) {
            if (statusEnum.type == value) {
                return statusEnum.description;
            }
        }
        return null;
    }
}
