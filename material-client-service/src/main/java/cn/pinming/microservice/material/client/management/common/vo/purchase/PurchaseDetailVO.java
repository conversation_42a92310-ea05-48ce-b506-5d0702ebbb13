package cn.pinming.microservice.material.client.management.common.vo.purchase;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2024/8/22 14:32
 */
@Data
public class PurchaseDetailVO {

    private Long id;

    @ApiModelProperty(value = "业务系统订单id")
    private String orderExtId;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "账户名称")
    private String accountName;

    @ApiModelProperty(value = "采购方")
    private String project;

    @ApiModelProperty(value = "外部系统供应商id")
    private String supplierExtId;

    @ApiModelProperty(value = "计划使用部位")
    private String position;

    @ApiModelProperty(value = "收货地址")
    private String address;

    @ApiModelProperty(value = "收货人姓名")
    private String receiver;

    @ApiModelProperty(value = "收货人电话")
    private String mobile;

    @ApiModelProperty(value = "要货日期")
    private LocalDate receiveDate;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "采购明细列表")
    private List<PurchaseItemVO> detailList;

}
