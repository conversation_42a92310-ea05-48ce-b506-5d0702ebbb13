package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.hutool.core.collection.CollUtil;
import cn.pinming.microservice.material.client.management.common.BaseDO;
import cn.pinming.microservice.material.client.management.common.form.ReceiptModuleDetailConfigForm;
import cn.pinming.microservice.material.client.management.common.mapper.ReceiptModuleDetailConfigMapper;
import cn.pinming.microservice.material.client.management.common.mapper.ext.OCRModuleExtMapper;
import cn.pinming.microservice.material.client.management.common.mapper.ext.ReceiptModuleDetailConfigExtMapper;
import cn.pinming.microservice.material.client.management.common.model.ReceiptModuleDetailConfigDO;
import cn.pinming.microservice.material.client.management.common.vo.ReceiptModuleDetailConfigVO;
import cn.pinming.microservice.material.client.management.service.biz.ReceiptModuleDetailConfigService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class ReceiptModuleDetailConfigServiceImpl extends ServiceImpl<ReceiptModuleDetailConfigMapper, ReceiptModuleDetailConfigDO> implements ReceiptModuleDetailConfigService {
    @Resource
    private OCRModuleExtMapper ocrModuleExtMapper;
    @Resource
    private ReceiptModuleDetailConfigExtMapper receiptModuleDetailConfigExtMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void ocrModuleRecycleSave(Long moduleId,Byte type,List<ReceiptModuleDetailConfigForm> list) {
        // 删除原数据
        this.lambdaUpdate()
                .eq(ReceiptModuleDetailConfigDO::getModuleId,moduleId)
                .eq(ReceiptModuleDetailConfigDO::getType,type)
                .set(BaseDO::getDeleted,1)
                .update();

        if (CollUtil.isNotEmpty(list)) {
            List<ReceiptModuleDetailConfigDO> collect = list.stream().map(e -> {
                ReceiptModuleDetailConfigDO dto = new ReceiptModuleDetailConfigDO();
                BeanUtils.copyProperties(e, dto);
                dto.setModuleId(moduleId);
                return dto;
            }).collect(Collectors.toList());

            // 保存新数据
            this.saveBatch(collect);
        }
    }

    @Override
    public List<ReceiptModuleDetailConfigVO> ocrModuleRecycleShow(Long moduleId, Byte type, Byte function) {
        List<ReceiptModuleDetailConfigVO> result = new ArrayList<>();

        if (function == null) {
            // s_ocr_module为主表
            result = ocrModuleExtMapper.recycleModuleDetailShow(moduleId,type);
        }else if (function == 1){
            // d_receipt_recycle_module_detail为主表:单据回收匹配结果
            result = receiptModuleDetailConfigExtMapper.recycleModuleDetailFunctionShow(moduleId,type);
        }

        return result;
    }
}
