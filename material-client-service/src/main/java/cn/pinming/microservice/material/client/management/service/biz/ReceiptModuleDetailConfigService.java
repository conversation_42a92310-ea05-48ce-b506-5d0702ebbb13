package cn.pinming.microservice.material.client.management.service.biz;

import cn.pinming.microservice.material.client.management.common.form.ReceiptModuleDetailConfigForm;
import cn.pinming.microservice.material.client.management.common.model.ReceiptModuleDetailConfigDO;
import cn.pinming.microservice.material.client.management.common.vo.ReceiptModuleDetailConfigVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ReceiptModuleDetailConfigService extends IService<ReceiptModuleDetailConfigDO> {
    void ocrModuleRecycleSave(Long moduleId,Byte type,List<ReceiptModuleDetailConfigForm> list);

    /**
     *
     * @param moduleId 模版id
     * @param type 业务类型 1 单据匹配显示设置 2 单据回收必须键值
     * @param function 功能类型(开发用) null s_ocr_module为主表; 1 d_receipt_recycle_module_detail为主表
     */
    List<ReceiptModuleDetailConfigVO> ocrModuleRecycleShow(Long moduleId, Byte type, Byte function);
}
