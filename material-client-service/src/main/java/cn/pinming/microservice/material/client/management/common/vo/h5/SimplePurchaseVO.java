package cn.pinming.microservice.material.client.management.common.vo.h5;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2024/9/5 14:40
 */
@Data
public class SimplePurchaseVO {

    @ApiModelProperty(value = "订单ID")
    private Long id;

    @ApiModelProperty(value = "客户名称")
    private String owner;

    @ApiModelProperty(value = "要货日期")
    private LocalDate receiveDate;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "明细列表")
    private List<SimplePurchaseDetailVO> list;

}
