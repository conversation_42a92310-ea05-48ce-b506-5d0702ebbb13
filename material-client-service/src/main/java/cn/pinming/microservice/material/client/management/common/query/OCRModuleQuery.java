package cn.pinming.microservice.material.client.management.common.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class OCRModuleQuery {
    @ApiModelProperty(value = "模版名称")
    private String name;

    @ApiModelProperty(value = "租户id")
    private String uid;

    @ApiModelProperty(value = "创建类型 1 自由创建 2 使用自建数据创建  3 使用第三方平台创建 4 自建数据结构 ")
    private List<Byte> type;

    @ApiModelProperty(value = "创建类型  5 第三方平台数据结构")
    private List<Byte> clientType;

    @ApiModelProperty(value = "是否启用 1 是 2 否")
    private Byte isEnable;

    @ApiModelProperty(value = "归属方id")
    private Long attributionId;

    @ApiModelProperty("单据匹配方式 :  0 - 区域坐标匹配 1 - 表格索引匹配")
    private Byte billMatchType;
}
