package cn.pinming.microservice.material.client.management.service.business;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import cn.pinming.microservice.material.client.management.common.dto.OCRConvertDTO;
import cn.pinming.microservice.material.client.management.common.dto.OCRHtmlDTO;
import cn.pinming.microservice.material.client.management.common.enums.DeveloperAppEnum;
import cn.pinming.microservice.material.client.management.common.enums.DeveloperTypeEnum;
import cn.pinming.microservice.material.client.management.common.form.MatchForm;
import cn.pinming.microservice.material.client.management.common.model.AttributionExtConfigDO;
import cn.pinming.microservice.material.client.management.common.model.DeveloperDO;
import cn.pinming.microservice.material.client.management.common.model.DeviceAttributionDO;
import cn.pinming.microservice.material.client.management.common.model.UserExtConfigDO;
import cn.pinming.microservice.material.client.management.common.vo.ocr.OCRTemplateVO;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizErrorException;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizExceptionMessageEnum;
import cn.pinming.microservice.material.client.management.infrastructure.util.PicUtil;
import cn.pinming.microservice.material.client.management.service.biz.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class OcrService {

    @Value("${imatchocr.url}")
    private String imatchocrUlr;
    @NacosValue("${material-ocr.file.upload}")
    private String materialOcrFileUpload;
    @NacosValue("${material-ocr.match}")
    private String materialOcrMatch;
    @Resource
    private UserConfigService userConfigService;
    @Resource
    private UserExtConfigService userExtConfigService;
    @Resource
    private AttributionExtConfigService attributionExtConfigService;
    @Resource
    private AppInvocationDailyLogService appInvocationDailyLogService;
    @Resource
    private DeveloperService developerService;
    @Resource
    private FileOssService fileOssService;
    @Resource
    private OCRModuleService ocrModuleService;
    @Resource
    private ReceiptModuleDetailConfigService receiptModuleDetailConfigService;



    /**
     * 图片ocr识别
     */
    public String ocrMatch(DeviceAttributionDO deviceAttributionDO, MatchForm form) {
        //检查ocr使用次数是否足够
        AttributionExtConfigDO attributionExtConfigDO = this.checkOcrService(deviceAttributionDO.getUid(), deviceAttributionDO.getId());

        form.setUid(deviceAttributionDO.getUid());
        form.setAttributionCode(deviceAttributionDO.getCode());
        try {
            // ocr服务
            String res = HttpUtil.post(imatchocrUlr, JSON.toJSONString(form));
            log.info("ocr匹配结果：{}", res);
            // 调用记录
            Long attributionId = ObjectUtil.isNotNull(attributionExtConfigDO) && attributionExtConfigDO.getDateExpire().isAfter(LocalDateTime.now()) ? attributionExtConfigDO.getAttributionId() : null;
            // 归属方无订阅日期 || 归属方订阅日期已到
            appInvocationDailyLogService.saveInvokeLog(DeveloperAppEnum.OCR.value(), deviceAttributionDO.getUid(), attributionId, 1);
            return res;
        } catch (Exception e) {
            log.error("ocr匹配异常", e);
            throw new BizErrorException(BizExceptionMessageEnum.OCR_MATCH_ERROR);
        }
    }

    public OCRHtmlDTO ocrHtmlMatch(DeviceAttributionDO deviceAttributionDO, MatchForm form) {
        //检查ocr使用次数是否足够
        AttributionExtConfigDO attributionExtConfigDO = this.checkOcrService(deviceAttributionDO.getUid(), deviceAttributionDO.getId());
        form.setUid(deviceAttributionDO.getUid());
        form.setAttributionCode(deviceAttributionDO.getCode());

        // 查询模板列表
        List<OCRTemplateVO> templateList = ocrModuleService.listTemplateDetail(deviceAttributionDO.getUid(), deviceAttributionDO.getCode());
        if (CollUtil.isEmpty(templateList)) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "未查询到模板列表");
        }

        try {
            OCRHtmlDTO htmlDTO = new OCRHtmlDTO();
            // 调用ocr服务
            String result = HttpRequest.post(materialOcrFileUpload)
                    .form("file", PicUtil.base64ToInputStreamResource(form.getBase64()))
                    .timeout(10000)
                    .execute()
                    .body();
            if (StrUtil.isNotBlank(result) && JSONUtil.isTypeJSON(result)) {
                result = JSONUtil.parseObj(result).getStr("data");
                htmlDTO = JSONUtil.toBean(result, OCRHtmlDTO.class);
            }

            List<String> ext = htmlDTO.getExt();
            String table = htmlDTO.getTable();
            Map<String, Object> map = new HashMap<>();
            map.put("ext", ext);
            map.put("table", table);
            map.put("template", templateList);
            result = HttpRequest.post(materialOcrMatch)
                    .body(JSON.toJSONString(map))
                    .timeout(10000)
                    .execute()
                    .body();
            if (StrUtil.isNotBlank(result) && JSONUtil.isTypeJSON(result)) {
                result = JSONUtil.parseObj(result).getStr("data");
                OCRHtmlDTO matchDTO = JSONUtil.toBean(result, OCRHtmlDTO.class);
                htmlDTO.setResult(matchDTO.getResult());
                htmlDTO.setId(matchDTO.getId());
                htmlDTO.setOtherId(matchDTO.getOtherId());
            }

            // 调用记录
            Long attributionId = ObjectUtil.isNotNull(attributionExtConfigDO) && attributionExtConfigDO.getDateExpire().isAfter(LocalDateTime.now()) ? attributionExtConfigDO.getAttributionId() : null;
            // 归属方无订阅日期 || 归属方订阅日期已到
            appInvocationDailyLogService.saveInvokeLog(DeveloperAppEnum.OCR.value(), deviceAttributionDO.getUid(), attributionId, 1);
            return htmlDTO;
        } catch (Exception e) {
            log.error("ocr匹配异常", e);
            throw new BizErrorException(BizExceptionMessageEnum.OCR_MATCH_ERROR);
        }
    }

    @Nullable
    public AttributionExtConfigDO checkOcrService(String uid, Long attributionId) {
        DeveloperDO developerDO = developerService.lambdaQuery().eq(DeveloperDO::getCreateId, uid)
                .eq(DeveloperDO::getAppId, DeveloperAppEnum.OCR.value()).eq(DeveloperDO::getType, DeveloperTypeEnum.START.value()).one();
        if (ObjectUtil.isNull(developerDO)) {
            throw new BizErrorException(BizExceptionMessageEnum.APP_NOT_CONFIG);
        }
        // 单据匹配归属方订阅
        AttributionExtConfigDO attributionExtConfigDO = attributionExtConfigService.lambdaQuery()
                .eq(AttributionExtConfigDO::getUid, uid)
                .eq(AttributionExtConfigDO::getAttributionId, attributionId)
                .eq(AttributionExtConfigDO::getAppId, DeveloperAppEnum.OCR.value())
                .one();

        if (ObjectUtil.isNotNull(attributionExtConfigDO)) {
            // 归属方订阅判断：归属方订阅到期 && 余额不足(包含无订阅配置 || 租户订阅次数不足)
            if (attributionExtConfigDO.getDateExpire().isBefore(LocalDateTime.now())) {
                if (userConfigService.getReceiptApiRemainingCount(uid) <= 0) {
                    throw new BizErrorException(BizExceptionMessageEnum.OCR_NOT_AVAILABLE);
                }
            }
        } else if (userConfigService.getReceiptApiRemainingCount(uid) <= 0) {
            // 租户订阅判断：租户订阅次数不足
            throw new BizErrorException(BizExceptionMessageEnum.OCR_NOT_AVAILABLE);
        }
        return attributionExtConfigDO;
    }


    public OCRConvertDTO ocrMatchByUuid(DeviceAttributionDO deviceAttributionDO, String uuid) {
        String downloadUrl = fileOssService.getUrlByUuid(uuid);
        if (StrUtil.isEmpty(downloadUrl)) {
            log.info("未找到下载地址deviceAttributionId:{}, uuid:{}", deviceAttributionDO.getId(), uuid);
            throw new BizErrorException(BizExceptionMessageEnum.OCR_MATCH_ERROR);
        }
        try {
//            byte[] bytes = HttpUtil.downloadBytes(downloadUrl);
//            if (bytes == null) {
//                throw new BizErrorException(BizExceptionMessageEnum.OCR_MATCH_ERROR);
//            }

            UserExtConfigDO userExtConfigDO = userExtConfigService.lambdaQuery().eq(UserExtConfigDO::getUid, deviceAttributionDO.getUid()).one();
            boolean baiduOcrEngineEnable = ObjectUtil.isNotNull(userExtConfigDO) && userExtConfigDO.getOcrEnable() == 1;

            MatchForm matchForm = new MatchForm();
//            matchForm.setBase64(Base64.getEncoder().encodeToString(bytes));
            matchForm.setPicUrl(downloadUrl);
            matchForm.setBaiduOcrEngineEnable(baiduOcrEngineEnable);
            String response = this.ocrMatch(deviceAttributionDO, matchForm);
            if (StrUtil.isEmpty(response)) {
                throw new BizErrorException(BizExceptionMessageEnum.OCR_MATCH_ERROR);
            }
            return JSONObject.parseObject(response, OCRConvertDTO.class);
        } catch (Exception e) {
            log.error("单据回收OCR识别异常deviceAttributionId:{}, downloadUrl:{}", deviceAttributionDO.getId(), downloadUrl, e);
            throw new BizErrorException(BizExceptionMessageEnum.OCR_MATCH_ERROR);
        }
    }

    public OCRHtmlDTO ocrHtmlMatchByUuid(DeviceAttributionDO deviceAttributionDO, String uuid) {
        String downloadUrl = fileOssService.getUrlByUuid(uuid);
        if (StrUtil.isEmpty(downloadUrl)) {
            log.info("未找到下载地址deviceAttributionId:{}, uuid:{}", deviceAttributionDO.getId(), uuid);
            throw new BizErrorException(BizExceptionMessageEnum.OCR_MATCH_ERROR);
        }
        try {
            byte[] bytes = HttpUtil.downloadBytes(downloadUrl);
            if (bytes == null) {
                throw new BizErrorException(BizExceptionMessageEnum.OCR_MATCH_ERROR);
            }
            MatchForm matchForm = new MatchForm();
            matchForm.setBase64(Base64.getEncoder().encodeToString(bytes));
            return this.ocrHtmlMatch(deviceAttributionDO, matchForm);
        } catch (Exception e) {
            log.error("单据回收OCR识别异常deviceAttributionId:{}, downloadUrl:{}", deviceAttributionDO.getId(), downloadUrl, e);
            throw new BizErrorException(BizExceptionMessageEnum.OCR_MATCH_ERROR);
        }
    }
}
