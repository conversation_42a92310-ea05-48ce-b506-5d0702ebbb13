package cn.pinming.microservice.material.client.management.common.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class CheckMaterialReverseVO {
    @ApiModelProperty(value = "材料名称")
    private String materialName;

    @ApiModelProperty(value = "规格型号")
    private String materialSpec;

    @ApiModelProperty(value = "送货单根数")
    private BigDecimal sendWeight;

    @ApiModelProperty(value = "送货单重量")
    private BigDecimal sendAmount;

    @ApiModelProperty(value = "理重复核根数")
    private BigDecimal theoryAmount;

    @ApiModelProperty(value = "理重复核重量")
    private BigDecimal theoryWeight;

    @ApiModelProperty(value = "复核根数")
    private BigDecimal reverseTheoryAmount;

    @ApiModelProperty(value = "复核重量")
    private BigDecimal reverseTheoryWeight;

    @ApiModelProperty(value = "重量-差异")
    private BigDecimal weightDif;

    @ApiModelProperty(value = "重量-偏差率")
    private BigDecimal weightRate;

    @ApiModelProperty(value = "根数-差异")
    private BigDecimal amountDif;

    @ApiModelProperty(value = "根数-偏差率")
    private BigDecimal amountRate;

    @ApiModelProperty(value = "根数-结果 1 合格, 2 不合格")
    private Integer amountResult;

    @ApiModelProperty(value = "重量-结果 1 合格, 2 不合格")
    private Integer weightResult;

    @ApiModelProperty(value = "混装实称重量")
    private BigDecimal actualWeight;

    @ApiModelProperty(value = " 1 实称总重, 2 面单总重")
    private Integer reverseWeightType;

    @ApiModelProperty(value = "验收类型 1 仅称重验收, 2 称重+实点根数验收")
    private Integer checkType;

    @ApiModelProperty(value = "混装实称重量-是否为手动输入 1 是 2 否")
    private Byte isInput;
}
