package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 磅房自助运单入口码管理设置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("s_self_qrcode")
public class SelfQrcodeDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 类型  1 运单  2 ocr
     */
    private Integer type;

    /**
     * 业务id
     */
    private String bizId;

    /**
     * 入口名称
     */
    private String name;

    /**
     * 归属方id
     */
    private Long attributionId;

    /**
     * 供应商id 逗号分割
     */
    private String supplierId;

    /**
     * 状态 0 启用 1 禁用
     */
    private Byte enabled;




}
