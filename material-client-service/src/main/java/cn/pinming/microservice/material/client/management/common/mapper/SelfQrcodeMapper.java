package cn.pinming.microservice.material.client.management.common.mapper;

import cn.pinming.microservice.material.client.management.common.model.SelfQrcodeDO;
import cn.pinming.microservice.material.client.management.common.vo.SelfQrcodeVO;
import cn.pinming.microservice.material.client.management.common.vo.SupplierConfigVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 磅房自助运单入口码管理设置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-20
 */
public interface SelfQrcodeMapper extends BaseMapper<SelfQrcodeDO> {

    List<SelfQrcodeVO> selectListConfig(String uid, Integer type);

    void updateEnableStatusById(Long id, String uid);

    List<SupplierConfigVO> selectSupplierList(@Param("supplierIdList") List<Long> supplierIdList, @Param("attributionId") Long attributionId);

}
