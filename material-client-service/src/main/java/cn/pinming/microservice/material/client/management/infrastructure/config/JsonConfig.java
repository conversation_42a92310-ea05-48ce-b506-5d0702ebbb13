package cn.pinming.microservice.material.client.management.infrastructure.config;

import com.fasterxml.jackson.core.StreamReadConstraints;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

@Slf4j
@Configuration
public class JsonConfig {

    @Resource
    ObjectMapper objectMapper;

    /**
     * 最大字符长度
     */
    private static final int MAX_STRING_LEN = 30_000_000;

    @PostConstruct
    public void objectMapper() {
        objectMapper.getFactory().setStreamReadConstraints(StreamReadConstraints.builder()
                .maxStringLength(MAX_STRING_LEN).build());
    }

}
