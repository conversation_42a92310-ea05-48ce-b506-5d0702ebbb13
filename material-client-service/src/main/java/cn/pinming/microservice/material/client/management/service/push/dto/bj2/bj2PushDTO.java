package cn.pinming.microservice.material.client.management.service.push.dto.bj2;

import cn.hutool.core.date.DateTime;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class bj2PushDTO implements Serializable {

    private static final long serialVersionUID = 7172261082489078573L;

    /**
     * 设备sn
     */
    private String projectSysNo;

    /**
     * 车牌号
     */
    private String plateNumber;

    /**
     * 车牌号置信率
     */
    private BigDecimal plateNumberConfidenceRatio;

    /**
     * 过磅时间
     */
    private DateTime time;

    /**
     * 重量
     */
    private BigDecimal weight;

    /**
     * 备注
     */
    private String remark;

    /**
     * 客户端称重记录ID
     */
    private String appWeighingId;

    /**
     * 称重类型
     * 1 有人值守称重
     * 2 无人值守称重
     */
    private Integer weighingType;

    /**
     *
     */
    private String registerWeighingId;

    /**
     * 图片列表
     */
    private List<bj2PushFileDTO> attachments;
}
