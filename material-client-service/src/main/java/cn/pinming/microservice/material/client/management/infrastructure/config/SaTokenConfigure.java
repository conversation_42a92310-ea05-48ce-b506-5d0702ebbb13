package cn.pinming.microservice.material.client.management.infrastructure.config;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.router.SaRouter;
import cn.pinming.microservice.material.client.management.infrastructure.interceptor.OpenapiInterceptor;
import cn.pinming.microservice.material.client.management.infrastructure.interceptor.PlatformInterceptor;
import cn.pinming.microservice.material.client.management.infrastructure.util.StpKit;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description
 */
@Configuration
public class SaTokenConfigure implements WebMvcConfigurer {

    @Resource
    private OpenapiInterceptor openapiInterceptor;
    @Resource
    private PlatformInterceptor platformInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new SaInterceptor(handle -> {
                    SaRouter.match("/**").check(r -> {
                        if (!StpKit.DEFAULT.isLogin() && !StpKit.MANAGER.isLogin() && !StpKit.CONSUMER.isLogin()) {
                            throw new NotLoginException(NotLoginException.TOKEN_TIMEOUT_MESSAGE, "0", NotLoginException.TOKEN_TIMEOUT);
                        }
                    });
                })).addPathPatterns("/**")
                .excludePathPatterns("/api/openapi/**")
                .excludePathPatterns("/api/user/graph/captcha")
                .excludePathPatterns("/api/user/email/captchaFree")
                .excludePathPatterns("/api/user/register")
                .excludePathPatterns("/api/user/salt")
                .excludePathPatterns("/api/user/password/login")
                .excludePathPatterns("/api/user/email/login")
                .excludePathPatterns("/api/oss/**")
                .excludePathPatterns("/api/platform/**")
                // swagger相关
                .excludePathPatterns("/swagger-ui.html")
                .excludePathPatterns("/doc.html")
                .excludePathPatterns("/webjars/**")
                .excludePathPatterns("/error")
                .excludePathPatterns("/favicon.ico")
                .excludePathPatterns("/swagger-resources/**")
                .excludePathPatterns("/null/swagger-resources/**")
                // 业务
                .excludePathPatterns("/api/common/**")
                // 基石司机自助称重确认客户端服务端接口
                .excludePathPatterns("/api/self-check/**")
                // 短链接
                .excludePathPatterns("/api/s/**")
                //h5
                .excludePathPatterns("/api/h5/**");
        // 注册自定义openapi拦截器，检验接口签名
        registry.addInterceptor(openapiInterceptor).addPathPatterns("/api/openapi/**").excludePathPatterns("/api/openapi/weigh/data/warning4")
                .excludePathPatterns("/api/openapi/weigh/confirm/query").excludePathPatterns("/api/openapi/weigh/data/query").excludePathPatterns("/api/openapi/confirmDetail");

        registry.addInterceptor(platformInterceptor).addPathPatterns("/api/platform/**").excludePathPatterns("/api/platform/token");

    }
//
//    @Bean
//    public MappingJackson2HttpMessageConverter mappingJackson2HttpMessageConverter() {
//        ObjectMapper objectMapper = Jackson2ObjectMapperBuilder.json().build();
//        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
//        objectMapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
//        SimpleModule simpleModule = new SimpleModule();
//        simpleModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer());
//        objectMapper.registerModules(new Jdk8Module(), new JavaTimeModule(), simpleModule);
//        return new MappingJackson2HttpMessageConverter(objectMapper);
//    }
}
