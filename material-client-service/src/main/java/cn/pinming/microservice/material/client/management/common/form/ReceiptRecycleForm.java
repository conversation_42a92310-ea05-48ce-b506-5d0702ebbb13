package cn.pinming.microservice.material.client.management.common.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ReceiptRecycleForm implements Serializable {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("归属方id")
    private Long attributionId;

    @ApiModelProperty("回收图片")
    private List<String> recyclePics;

    @ApiModelProperty("批次id")
    private Long batchId;
}
