package cn.pinming.microservice.material.client.management.controller.subscribe;

import cn.pinming.microservice.material.client.management.common.form.*;
import cn.pinming.microservice.material.client.management.common.vo.*;
import cn.pinming.microservice.material.client.management.infrastructure.response.SingleResponse;
import cn.pinming.microservice.material.client.management.infrastructure.util.UserIdUtil;
import cn.pinming.microservice.material.client.management.common.model.UserConfigDO;
import cn.pinming.microservice.material.client.management.common.query.UserExtConfigQuery;
import cn.pinming.microservice.material.client.management.common.query.UserQuery;
import cn.pinming.microservice.material.client.management.service.biz.AttributionExtConfigService;
import cn.pinming.microservice.material.client.management.service.biz.AuthorizationFileService;
import cn.pinming.microservice.material.client.management.service.biz.UserConfigService;
import cn.pinming.microservice.material.client.management.service.biz.UserExtConfigService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2023/8/7 16:55
 */
@Api(value = "租户订阅", tags = {"subscribe"})
@RestController
@RequestMapping("/api/subscribe")
public class UserSubscribeController {
    @Resource
    private UserConfigService userConfigService;
    @Resource
    private AuthorizationFileService authorizationFileService;
    @Resource
    private UserIdUtil userIdUtil;
    @Resource
    private UserExtConfigService userExtConfigService;
    @Resource
    private AttributionExtConfigService attributionExtConfigService;

    @ApiOperation(value = "列表", responseReference = "SingleResponse«IPage<UserSubscribeVO>»", nickname = "subscribeList")
    @PostMapping("/list")
    public SingleResponse<IPage<UserSubscribeVO>> page(@RequestBody UserQuery query) {
        IPage<UserSubscribeVO> page = userConfigService.subscribeList(query);
        return SingleResponse.of(page);
    }

    @ApiOperation(value = "存储详情", responseReference = "SingleResponse«UserSpaceVO»", nickname = "subscribeSpace")
    @GetMapping("/{id}/space")
    public SingleResponse<UserSpaceVO> space(@PathVariable Long id) {
        UserSpaceVO result = userConfigService.queryUserSpace(id);
        return SingleResponse.of(result);
    }

    @ApiOperation(value = "修改存储空间、日期", responseReference = "SingleResponse«Boolean»", nickname = "editStorage")
    @PostMapping("/storage")
    public SingleResponse<Boolean> storage(@RequestBody @Validated UserSpaceForm form) {
        userConfigService.editUserStorageConfig(form);
        return SingleResponse.of(Boolean.TRUE);
    }

    @ApiOperation(value = "修改api调用次数", responseReference = "SingleResponse«Boolean»", nickname = "editInvocation")
    @PostMapping("/invocation")
    public SingleResponse<Boolean> invocation(@RequestBody @Validated UserInvocationForm form) {
        userConfigService.editUserInvocationConfig(form);
        return SingleResponse.of(Boolean.TRUE);
    }

    @ApiOperation(value = "套餐余量", responseReference = "SingleResponse«UserSubscribeInfoVO»", nickname = "subscribeInfo")
    @GetMapping("/info")
    public SingleResponse<UserSubscribeInfoVO> info() {
        String uid = userIdUtil.getUId();
        UserSubscribeInfoVO result = userConfigService.queryUserSubscribeInfo(uid);
        return SingleResponse.of(result);
    }

    @ApiOperation(value = "单机版授权文件新增/删除", responseReference = "SingleResponse«Boolean»", nickname = "addOrDelAuthorizationFiles")
    @PostMapping("/authorization/file")
    public SingleResponse<Boolean> addOrDelAuthorizationFiles(@RequestBody AuthorizationFileForm authorizationFileForm) {
        authorizationFileService.addOrDelAuthorizationFiles(authorizationFileForm);
        return SingleResponse.of(true);
    }

    @ApiOperation(value = "单机版授权文件列表", responseReference = "SingleResponse«List<AuthorizationFileVO>»", nickname = "getAuthorizationFiles")
    @GetMapping("/authorization/file")
    public SingleResponse<List<AuthorizationFileVO>> getAuthorizationFiles(@RequestParam("uid") String uid) {
        List<AuthorizationFileVO> list = authorizationFileService.getAuthorizationFiles(uid);
        return SingleResponse.of(list);
    }

    @ApiOperation(value = "单机版授权文件绑定数据归属方", responseReference = "SingleResponse«Boolean»", nickname = "bindAuthorizationFile")
    @PutMapping("/authorization/file")
    public SingleResponse<Boolean> bindAuthorizationFile(@RequestBody AuthorizationFileBindForm authorizationFileBindForm) {
        authorizationFileService.bindAuthorizationFile(authorizationFileBindForm);
        return SingleResponse.of(true);
    }

    @ApiOperation(value = "单机版授权文件下载", responseReference = "SingleResponse«AuthorizationFileDownloadVO»", nickname = "downloadAuthorizationFile")
    @GetMapping("/authorization/file/download/{id}")
    public SingleResponse<AuthorizationFileDownloadVO> downloadAuthorizationFile(@PathVariable("id") Long id) {
        AuthorizationFileDownloadVO fileDownloadVO = authorizationFileService.downloadAuthorizationFile(id);
        return SingleResponse.of(fileDownloadVO);
    }

    @ApiOperation(value = "修改数据归属方上限", responseReference = "SingleResponse«Boolean»", nickname = "updateAttributionAmount")
    @PostMapping("/attribution/amount/update")
    public SingleResponse<Boolean> updateAttributionAmount(@RequestBody AttributionAmountForm form) {
        userConfigService.updateAttributionAmount(form);
        return SingleResponse.of(true);
    }

    @ApiOperation(value = "查看数据归属方上限", responseReference = "SingleResponse«UserConfigDO»", nickname = "showAttributionAmount")
    @GetMapping("/attribution/amount/show/{uid}")
    public SingleResponse<UserConfigDO> showAttributionAmount(@PathVariable("uid")String uid) {
        UserConfigDO one = userConfigService.lambdaQuery()
                .eq(UserConfigDO::getUid, uid)
                .one();
        return SingleResponse.of(one);
    }

    @ApiOperation(value = "额外服务-租户", responseReference = "SingleResponse«Boolean»", nickname = "updateUserExtConfig")
    @PostMapping ("/extUpdate/user")
    public SingleResponse<Boolean> updateUserExtConfig(@RequestBody UserExtConfigForm form) {
        userExtConfigService.updateUserExtConfig(form);
        return SingleResponse.of(true);
    }

    @ApiOperation(value = "额外服务显示-租户", responseReference = "SingleResponse«UserExtConfigVO»", nickname = "showUserExtConfig")
    @PostMapping ("/extShow")
    public SingleResponse<UserExtConfigVO> showUserExtConfig() {
        UserExtConfigVO vo = userExtConfigService.showUserExtConfig();
        return SingleResponse.of(vo);
    }

    @ApiOperation(value = "额外归属方显示-归属方", responseReference = "SingleResponse«List<AttributionExtConfigVO>»", nickname = "showAttributionExtConfig")
    @PostMapping ("/showAttributionExtConfig")
    public SingleResponse<List<AttributionExtConfigVO>> showAttributionExtConfig(@RequestBody UserExtConfigQuery query) {
        return SingleResponse.of(attributionExtConfigService.showAttributionExtConfig(query.getUid(),query.getAppId()));
    }

    @ApiOperation(value = "钢筋小助手-租户设置", responseReference = "SingleResponse«Boolean»", nickname = "updateAttributionExtConfig")
    @PostMapping("/extUpdate/attribution")
    public SingleResponse<Boolean> updateAttributionExtConfig(@RequestBody AttributionExtConfigForm form) {
        attributionExtConfigService.updateAttributionExtConfig(form);
        return SingleResponse.of(true);
    }
}
