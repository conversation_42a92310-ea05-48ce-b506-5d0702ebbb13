package cn.pinming.microservice.material.client.management.controller.device;


import cn.hutool.core.util.StrUtil;
import cn.pinming.microservice.material.client.management.infrastructure.response.SingleResponse;
import cn.pinming.microservice.material.client.management.infrastructure.util.UserIdUtil;
import cn.pinming.microservice.material.client.management.common.BaseDO;
import cn.pinming.microservice.material.client.management.common.form.DeviceAttributionForm;
import cn.pinming.microservice.material.client.management.common.model.DeviceAttributionDO;
import cn.pinming.microservice.material.client.management.common.vo.AttributionDeviceVO;
import cn.pinming.microservice.material.client.management.service.biz.DeviceAttributionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(value = "归属方管理", tags = {"deviceAttribution"})
@RestController
@RequestMapping("/api/deviceAttribution")
public class DeviceAttributionController {
    @Resource
    private DeviceAttributionService deviceAttributionService;
    @Resource
    private UserIdUtil userIdUtil;

    @ApiOperation(value = "注册、编辑归属方", responseReference = "SingleResponse«?»", nickname = "save")
    @PostMapping("/save")
    public SingleResponse<?> save(@RequestBody @Validated DeviceAttributionForm form) {
        deviceAttributionService.add(form);
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "删除归属方", responseReference = "SingleResponse«?»", nickname = "delete")
    @GetMapping("/delete/{id}")
    public SingleResponse<?> delete(@PathVariable("id") Long id) {
        deviceAttributionService.delete(id);
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "归属方列表", responseReference = "SingleResponse«List<DeviceAttributionDO>»", nickname = "list")
    @GetMapping("/list")
    public SingleResponse<List<DeviceAttributionDO>> list(@RequestParam(value = "uid", required = false) String uId) {
        if (StrUtil.isBlank(uId)) {
            uId = userIdUtil.getUId();
        }
        List<DeviceAttributionDO> list = deviceAttributionService.lambdaQuery()
                .eq(DeviceAttributionDO::getUid, uId)
                .orderByDesc(BaseDO::getGmtCreate)
                .list();
        return SingleResponse.of(list);
    }

    @ApiOperation(value = "归属方详情", responseReference = "SingleResponse«DeviceAttributionDO»", nickname = "attributionDetail")
    @GetMapping("/detail/{id}")
    public SingleResponse<DeviceAttributionDO> detail(@PathVariable("id") Long id) {
        DeviceAttributionDO result = deviceAttributionService.lambdaQuery()
                .eq(DeviceAttributionDO::getId, id)
                .one();
        return SingleResponse.of(result);
    }

    @ApiOperation(value = "获取设备归属方及设备列表配置", responseReference = "SingleResponse«List<AttributionDeviceVO>»", nickname = "attributionDeviceConfig")
    @GetMapping("/config/list")
    public SingleResponse<List<AttributionDeviceVO>> configList(@RequestParam String type, @RequestParam(required = false) Long attributionId) {
        List<AttributionDeviceVO> result = deviceAttributionService.configList(type, attributionId);
        return SingleResponse.of(result);
    }

}
