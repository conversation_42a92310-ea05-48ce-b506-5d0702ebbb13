package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 单据回收模板表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("d_receipt_recycle_module")
public class ReceiptRecycleModuleDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 用户id
     */
    private String uid;

    /**
     * 单据回收id
     */
    private Long receiptRecycleId;

    /**
     * 模版id
     */
    private Long moduleId;

    /**
     * 模板明细id
     */
    private Long ocrModuleDetailId;

    /**
     * 数据组名称
     */
    private String groupName;

    /**
     * 数据键名称
     */
    private String keyName;

    /**
     * 模板键值类型（1-字符串 2-数字 3-日期）
     */
    private Byte keyType;

    /**
     * 模板键值
     */
    private String keyValue;

    /**
     * 是否有效(0-否、1-是)
     */
    private Integer isEffective;


}
