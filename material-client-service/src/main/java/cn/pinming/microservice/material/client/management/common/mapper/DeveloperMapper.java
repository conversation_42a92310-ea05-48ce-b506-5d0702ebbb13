package cn.pinming.microservice.material.client.management.common.mapper;

import cn.pinming.microservice.material.client.management.common.dto.DeveloperDTO;
import cn.pinming.microservice.material.client.management.common.model.DeveloperDO;
import cn.pinming.microservice.material.client.management.common.model.ext.DeveloperExtDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 开发者表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-16
 */
public interface DeveloperMapper extends BaseMapper<DeveloperDO> {

    List<DeveloperExtDO> selectAppList(@Param("uId") String uId);

    DeveloperDTO selectOneByAppKeyService(@Param("appKey")String appKey);
}
