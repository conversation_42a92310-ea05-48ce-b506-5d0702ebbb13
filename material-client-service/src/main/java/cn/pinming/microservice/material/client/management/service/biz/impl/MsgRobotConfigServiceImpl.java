package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.hutool.core.util.StrUtil;
import cn.pinming.exception.BOException;
import cn.pinming.exception.BOExceptionEnum;
import cn.pinming.microservice.material.client.management.common.enums.MessageRobotTypeEnum;
import cn.pinming.microservice.material.client.management.common.form.MsgRobotForm;
import cn.pinming.microservice.material.client.management.common.mapper.MsgRobotConfigMapper;
import cn.pinming.microservice.material.client.management.common.model.MsgRobotConfigDO;
import cn.pinming.microservice.material.client.management.common.query.MsgRobotPageQuery;
import cn.pinming.microservice.material.client.management.common.vo.MsgRobotVO;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizErrorException;
import cn.pinming.microservice.material.client.management.infrastructure.util.MsgSendUtil;
import cn.pinming.microservice.material.client.management.infrastructure.util.UserIdUtil;
import cn.pinming.microservice.material.client.management.service.biz.IMsgRobotConfigService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <p>
 * 消息机器人配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
@Service
public class MsgRobotConfigServiceImpl extends ServiceImpl<MsgRobotConfigMapper, MsgRobotConfigDO> implements IMsgRobotConfigService {

    @Resource
    private UserIdUtil userIdUtil;

    @Override
    public Page<MsgRobotVO> selectPageByQuery(MsgRobotPageQuery query) {
        query.setUid(userIdUtil.getUId());
        return getBaseMapper().selectPageByQuery(query);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateConfig(MsgRobotForm form) {
        Long id = form.getId();
        String name = form.getName();
        Integer type = form.getType();
        String token = form.getToken();
        String secret = form.getSecret();
        if (Objects.equals(type, MessageRobotTypeEnum.DING_DING.getVal())) {
            // 校验token  sign
            if (StrUtil.isBlank(token)) {
                throw new BOException(BOExceptionEnum.SYSTEM_ERROR.errorCode(), "钉钉: access_token为空");
            }
            if (token.contains("https")) {
                throw new BOException(BOExceptionEnum.SYSTEM_ERROR.errorCode(), "钉钉: 请将access_token填写到文本框");
            }
            if (StrUtil.isBlank(secret)) {
                throw new BOException(BOExceptionEnum.SYSTEM_ERROR.errorCode(), "钉钉: 加签秘钥为空");
            }
        } else if (Objects.equals(type, MessageRobotTypeEnum.WECHAT.getVal())) {
            // 校验key
            if (StrUtil.isBlank(token)) {
                throw new BOException(BOExceptionEnum.SYSTEM_ERROR.errorCode(), "企业微信: key为空");
            }
            if (token.contains("https")) {
                throw new BOException(BOExceptionEnum.SYSTEM_ERROR.errorCode(), "企业微信: 请将key为空填写到文本框");
            }
        }

        MsgRobotConfigDO config = lambdaQuery().eq(MsgRobotConfigDO::getName, name)
                .ne(id != null, MsgRobotConfigDO::getId, id) //修改时排除自己
                .eq(MsgRobotConfigDO::getCreateId, userIdUtil.getUId())
                .one();
        if (config != null) {
            throw new BOException(BOExceptionEnum.SYSTEM_ERROR.errorCode(), "机器人名称已存在，请勿重复添加！");
        }
        MsgRobotConfigDO msgRobotConfig = new MsgRobotConfigDO();
        BeanUtils.copyProperties(form, msgRobotConfig);
        saveOrUpdate(msgRobotConfig);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(Long id) {
        // 判断是否被使用
        int count = getBaseMapper().selectCountByMsgRobotId(id);
        if (count > 0) {
            throw new BizErrorException(BOExceptionEnum.SYSTEM_ERROR.errorCode(), "该消息机器人已被使用，请解除后再尝试删除！");
        }
        removeById(id);
    }

    @Override
    public void sendMsg(MsgRobotForm form) {
        String content = "{\"msgtype\": \"text\",\"text\": {\"content\":\"来自基石平台的推送消息，接收成功！\"}}";
        Integer type = form.getType();
        try {
            if (Objects.equals(type, MessageRobotTypeEnum.DING_DING.getVal())) {
                MsgSendUtil.sendDingDingMsg(form.getToken(), form.getSecret(), content);
            } else if (Objects.equals(type, MessageRobotTypeEnum.WECHAT.getVal())) {
                MsgSendUtil.sendWechatMsg(form.getToken(), content);
            }
        }catch (Exception e){
            log.error("消息发送失败", e);
        }
    }



}
