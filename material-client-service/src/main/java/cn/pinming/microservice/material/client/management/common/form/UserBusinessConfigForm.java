package cn.pinming.microservice.material.client.management.common.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 推送订阅配置表单
 *
 * <AUTHOR>
 */
@Data
public class UserBusinessConfigForm {
    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "数据推送地址")
    private String weighDataPushUrl;

    @ApiModelProperty(value = "照片推送地址")
    private String weighPicPushUrl;

    @ApiModelProperty(value = "数据推送开关 1 关闭 2 开启")
    private Integer weighDataPushStatus;

    @ApiModelProperty(value = "照片推送开关 1 关闭 2 开启")
    private Integer weighPicPushStatus;

    @ApiModelProperty(value = "称重单位(1-默认不处理、2-吨、3-千克)")
    private Integer weighUnit;

    @ApiModelProperty(value = "排除的数据归属方")
    private String weighDataPushAttribution;

    @ApiModelProperty(value = "请求地址")
    private String endpoint;

    @ApiModelProperty(value = "应用key")
    private String appKey;

    @ApiModelProperty(value = "应用密钥key")
    private String appSecretKey;

    @ApiModelProperty(value = "推送类型 1 sdk推送 2 一般推送 3 确认单推送 4 订单推送")
    private Byte pushType;
}
