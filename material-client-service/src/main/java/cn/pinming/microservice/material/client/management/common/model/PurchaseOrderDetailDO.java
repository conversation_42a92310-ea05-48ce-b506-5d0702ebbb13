package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <p>
 * 采购单明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("d_purchase_order_detail")
public class PurchaseOrderDetailDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 采购单ID
     */
    private Long orderId;

    /**
     * 外部系统明细业务ID
     */
    private String extId;

    /**
     * 货物名称
     */
    private String name;

    /**
     * 规格型号
     */
    private String spec;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 采购数量
     */
    private BigDecimal amount;

    /**
     * 采购单位
     */
    private String unit;

    /**
     * 扣杂(含水率)
     */
    private BigDecimal deductRatio;

    /**
     * 扣重
     */
    private BigDecimal deductWeight;

    /**
     * 称重换算系数
     */
    private BigDecimal scaleFactor;

    /**
     * 参照的重量单位：0 = 千克；1 = 吨
     */
    private Integer unitType;

    /**
     * 其他参数
     */
    private String argument;

    /**
     * 计划使用部位
     */
    private String position;

    /**
     * wbsId
     */
    private String wbsId;

    /**
     * 备注
     */
    private String remark;


}
