package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.pinming.microservice.material.client.management.common.mapper.PlateIdentifyMapper;
import cn.pinming.microservice.material.client.management.common.model.PlateIdentifyDO;
import cn.pinming.microservice.material.client.management.service.biz.PlateIdentifyService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2023/10/27 10:14
 */
@Slf4j
@Service
public class PlateIdentifyServiceImpl extends ServiceImpl<PlateIdentifyMapper, PlateIdentifyDO> implements PlateIdentifyService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addPlateIdentifyTask(String recordId) {
        PlateIdentifyDO plateIdentifyDO = new PlateIdentifyDO();
        plateIdentifyDO.setRecordId(recordId);
        plateIdentifyDO.setGmtCreate(LocalDateTime.now().plusMinutes(5));
        this.save(plateIdentifyDO);
    }
}
