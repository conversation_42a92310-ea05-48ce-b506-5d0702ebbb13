package cn.pinming.microservice.material.client.management.common.mapper;

import cn.pinming.microservice.material.client.management.common.model.PrintTemplateConfigDO;
import cn.pinming.microservice.material.client.management.common.model.PrintTemplateDO;
import cn.pinming.microservice.material.client.management.common.vo.print.PrintTemplateVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * <p>
 * 打印模板设置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-20
 */
public interface PrintTemplateMapper extends BaseMapper<PrintTemplateDO> {

    void updateEnableStatusById(Long id, String uid);

    PrintTemplateDO selectByDeviceId(Long deviceId, String uid, Byte type);

    @Deprecated
    List<PrintTemplateDO> selectByDeviceIdAndType(Long deviceId, String uid, Byte type, Byte formType);

    List<PrintTemplateDO> selectByDeviceIdAndStyle(Long deviceId, String uid, Byte type, Byte style, Byte formType);

    List<PrintTemplateVO> selectTemplateConfigList(String uid, Byte type, Byte style, Byte formType);

    List<PrintTemplateConfigDO> selectAttributionList(Long id, List<Long> deviceIdList, String uid, Byte type, Byte style, Byte formType);
}
