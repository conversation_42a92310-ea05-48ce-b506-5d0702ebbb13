package cn.pinming.microservice.material.client.management.common.form;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class DeviceAttributionForm {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 归属方名称
     */
//    @NotBlank(message = "归属方名称不能为空")
    private String name;

    /**
     * 归属方code
     */
    @NotBlank(message = "归属方code不能为空")
    private String code;

    /**
     * 主code(用于推送展示)
     */
    private String primaryCode;

    /**
     * 照片base64
     */
    private String base64;

    /**
     * 所属终端名称是否使用本归属方名称 1 使用 2 不使用
     */
    private Byte isName;

    /**
     * 所属终端名称是否使用本归属方logo 1 使用 2 不使用
     */
    private Byte isLogo;
}
