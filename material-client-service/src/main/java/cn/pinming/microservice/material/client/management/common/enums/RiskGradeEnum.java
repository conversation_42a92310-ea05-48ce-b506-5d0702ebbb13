package cn.pinming.microservice.material.client.management.common.enums;


import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
public enum RiskGradeEnum {

    LOW("LOW", "低风险"),
    MIDDLE("MIDD<PERSON>", "中风险"),
    HIGH("HIGH", "高风险")
    ;

    private final String val;
    private final String desc;

    public static final Map<String, String> KEY_MAP = Arrays.stream(values()).collect(Collectors.toMap(RiskGradeEnum::getVal, RiskGradeEnum::getDesc));

    public static final Map<String, String> VAL_MAP = Arrays.stream(values()).collect(Collectors.toMap(RiskGradeEnum::getDesc, RiskGradeEnum::getVal));

    RiskGradeEnum(String val, String desc) {
        this.val = val;
        this.desc = desc;
    }

    public static boolean validate(String riskGrade) {
        for (RiskGradeEnum value : RiskGradeEnum.values()) {
            if (value.name().equals(riskGrade)) {
                return true;
            }
        }
        return false;
    }

}
