package cn.pinming.microservice.material.client.management.common.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class ReceiptRecycleExportForm  {

    @ApiModelProperty("单据模板集合")
    private Map<String, List<Long>> moduleList;

    @ApiModelProperty("归属方id列表")
    private List<Long> attributionIdList;

    @ApiModelProperty("模版id")
    private Long moduleId;

    @ApiModelProperty("uid")
    private String uid;

    @ApiModelProperty("导出字段")
    private List<String> fieldList;

    @ApiModelProperty("过磅类型(1-收料、2-发料)")
    private List<Integer> weighTypeList;

    @ApiModelProperty("毛重时间")
    private List<String> weightGrossTimeList;

    @ApiModelProperty("皮重时间")
    private List<String> weightTareTimeList;


}
