package cn.pinming.microservice.material.client.management.common.enums;

/**
 * <AUTHOR>
 */

public enum PushLogStatusEnum {
    ONE((byte) 1, "进入队列"),
    TWO((byte) 2, "已发送"),
    THREE((byte) 3, "已确认"),
    FOUR((byte) 4, "消费失败"),
    FIVE((byte) 5, "发送成功"),
    SIX((byte) 6, "发送失败");

    private final Byte type;
    private final String description;

    PushLogStatusEnum(Byte type, String description) {
        this.type = type;
        this.description = description;
    }

    public Byte value() {
        return type;
    }

    public String description() {
        return description;
    }
}
