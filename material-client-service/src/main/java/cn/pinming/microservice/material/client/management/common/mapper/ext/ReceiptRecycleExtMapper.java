package cn.pinming.microservice.material.client.management.common.mapper.ext;

import cn.pinming.microservice.material.client.management.common.PageHeader;
import cn.pinming.microservice.material.client.management.common.form.ReceiptRecycleExportForm;
import cn.pinming.microservice.material.client.management.common.model.ReceiptRecycleDO;
import cn.pinming.microservice.material.client.management.common.query.ReceiptRecycleQuery;
import cn.pinming.microservice.material.client.management.common.query.ReceiptRecycleResultQuery;
import cn.pinming.microservice.material.client.management.common.vo.ReceiptRecycleResultVO;
import cn.pinming.microservice.material.client.management.common.vo.ReceiptRecycleVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface ReceiptRecycleExtMapper {

    IPage<ReceiptRecycleVO> pageList(@Param("query") ReceiptRecycleQuery query);

    PageHeader<ReceiptRecycleResultVO> resultList(@Param("pageHeader") PageHeader pageHeader, @Param("query") ReceiptRecycleResultQuery query);

    List<Map<String, Object>> resultExport(@Param("query") ReceiptRecycleExportForm query);

    Long countByDeviceSn(@Param("deviceSn") String deviceSn, @Param("attributionId") Long attributionId);

    List<ReceiptRecycleDO> needPushDatas(@Param("uid") String uid, @Param("attributionList") List<Long> attributionList);

    List<cn.pinming.material.v2.model.vo.ReceiptRecycleVO> ocrRecycleList(@Param("query")cn.pinming.material.v2.model.query.ReceiptRecycleQuery query, @Param("uid") String uid);
}
