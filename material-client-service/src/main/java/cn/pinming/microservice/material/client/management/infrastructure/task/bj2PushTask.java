package cn.pinming.microservice.material.client.management.infrastructure.task;

import cn.pinming.microservice.material.client.management.service.push.service.impl.bj2PushServiceImpl;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
//@Component
@RestController
@RequestMapping("/api/common")
public class bj2PushTask {
    @Resource
    private bj2PushServiceImpl bj2PushService;

    //    @Scheduled(cron = "0/5 * * * ? ")
//    @Transactional(rollbackFor = Exception.class)
    @GetMapping("/push")
    public void run() {
        bj2PushService.pushData();
    }


}
