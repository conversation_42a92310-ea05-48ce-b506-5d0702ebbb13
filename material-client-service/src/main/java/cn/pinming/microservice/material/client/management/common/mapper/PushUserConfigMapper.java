package cn.pinming.microservice.material.client.management.common.mapper;

import cn.pinming.microservice.material.client.management.common.model.PushUserConfigDO;
import cn.pinming.microservice.material.client.management.common.vo.push.PushRouteConfigVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * <p>
 * 租户推送配置 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-11
 */
public interface PushUserConfigMapper extends BaseMapper<PushUserConfigDO> {

    List<PushRouteConfigVO> selectOpenedRouteConfigList(Long routeConfigId);
}
