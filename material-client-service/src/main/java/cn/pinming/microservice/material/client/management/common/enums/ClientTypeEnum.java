package cn.pinming.microservice.material.client.management.common.enums;

import cn.pinming.microservice.material.client.management.common.vo.EnumVO;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
public enum ClientTypeEnum {
    CLIENT((byte) 1, "一体机终端软件"),
    TOOL((byte) 2, "数据库"),
    DOC((byte) 3, "文档手册"),
    DRIVER((byte) 4, "配套驱动程序"),
    HELPER((byte) 5, "基石下载助手"),
    SELF_CHECK((byte) 6, "自助确认终端软件"),
    SCAN((byte) 7, "单据回收扫描仪程序"),
    HELPER_TOOL((byte) 8, "基石助手"),
    LOCAL_SERVER((byte) 9, "基石本地部署版软件"),
    ;

    public static final Map<Byte, String> KEY_MAP = Arrays.stream(values()).collect(Collectors.toMap(ClientTypeEnum::getVal, ClientTypeEnum::getDesc));

    public static final Map<String, Byte> VAL_MAP = Arrays.stream(values()).collect(Collectors.toMap(ClientTypeEnum::getDesc, ClientTypeEnum::getVal));

    public static final List<EnumVO> LIST = Arrays.stream(values()).map(e -> new EnumVO(e.getVal(), e.getDesc())).collect(Collectors.toList());

    private final byte val;

    private final String desc;

    ClientTypeEnum(byte val, String desc) {
        this.val = val;
        this.desc = desc;
    }

}
