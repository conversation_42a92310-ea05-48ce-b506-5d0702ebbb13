package cn.pinming.microservice.material.client.management.common.mapper;

import cn.pinming.microservice.material.client.management.common.model.ClientDO;
import cn.pinming.microservice.material.client.management.common.vo.ClientVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * <p>
 * 基石客户端维护表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-13
 */
public interface ClientMapper extends BaseMapper<ClientDO> {

    IPage<ClientVO> selectPageList(Page query);
}
