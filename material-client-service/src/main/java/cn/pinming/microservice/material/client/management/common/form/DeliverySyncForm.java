package cn.pinming.microservice.material.client.management.common.form;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class DeliverySyncForm {
    /**
     * 发货时间，格式按yyyy-MM-dd HH:mm:ss 不能为空
     */
    @NotNull(message = "发货时间不能为空")
    private String deliveryTime;

    /**
     * 司机电话，不能为空
     */
//    @NotBlank(message = "司机电话不能为空")
    private String driverPhoneNo;

    /**
     * 司机名称，不能为空
     */
//    @NotBlank(message = "司机名称不能为空")
    private String driverName;

    /**
     * 基石运单编号，可能为空
     */
    private String jsDeliveryNo;

    /**
     * 车牌号，不能为空
     */
//    @NotBlank(message = "车牌号不能为空")
    private String truckNo;

    /**
     * 运单物料明细，不能为空
     */
    @NotEmpty(message = "运单物料明细不能为空")
    private List<DeliverySyncMaterialForm> cargoList;

    /**
     * 基石身份信息
     */
    @NotNull(message = "基石身份信息不能为空")
    private DeliverySyncIdentityForm jsInfo;

    /**
     * 运单对应的订单信息
     */
    @NotNull(message = "运单对应的订单信息不能为空")
    private DeliverySyncPurchaseForm purchaseBaseInfo;
}
