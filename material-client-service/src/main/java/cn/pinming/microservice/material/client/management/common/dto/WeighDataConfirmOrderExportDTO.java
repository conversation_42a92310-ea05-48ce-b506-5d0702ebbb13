package cn.pinming.microservice.material.client.management.common.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 现场称重确认单导出DTO
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
public class WeighDataConfirmOrderExportDTO {

    @ExcelProperty("确认单单号")
    private String confirmNo;

    @ExcelProperty("所属归属方")
    private String attributionName;

    @ExcelProperty("车牌号")
    private String truckNo;

    @ExcelProperty("司机")
    private String driver;

    @ExcelProperty("车载货物")
    private String material;

    @ExcelProperty("发货单所属供应商")
    private String supplierName;

    @ExcelProperty("实重")
    private BigDecimal weightActual;

    @ExcelProperty("实际数量")
    private BigDecimal actualCount;

    @ExcelProperty("推送状态")
    private String pushStateName;

    @ExcelProperty("现场设备SN")
    private String deviceSn;

    @ExcelProperty("创建时间")
    private String createTime;

}
