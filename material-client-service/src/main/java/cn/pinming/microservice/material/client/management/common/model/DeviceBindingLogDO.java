package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 设备管理-设备数据绑定日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("s_device_binding_log")
public class DeviceBindingLogDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 设备数据绑定表主键ID
     */
    private Long deviceBindingId;

    /**
     * 0 绑定 1 解绑
     */
    private Integer type;


}
