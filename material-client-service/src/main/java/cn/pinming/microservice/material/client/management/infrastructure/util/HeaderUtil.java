package cn.pinming.microservice.material.client.management.infrastructure.util;

import javax.servlet.http.HttpServletRequest;

import static cn.pinming.microservice.material.client.management.infrastructure.interceptor.PlatformInterceptor.AUTHORIZATION_HEADER;

/**
 *
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2025/4/9 14:35
 */
public class HeaderUtil {

    public static String getHeaderAuthorization(HttpServletRequest request) {
        return request.getHeader(AUTHORIZATION_HEADER);
    }
}
