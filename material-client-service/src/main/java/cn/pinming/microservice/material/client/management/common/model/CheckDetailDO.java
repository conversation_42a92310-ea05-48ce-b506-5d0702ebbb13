package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <p>
 * 钢筋验收明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("d_check_detail")
public class CheckDetailDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 验收id
     */
    private Long checkId;

    /**
     * 材料id
     */
    private Long materialId;

    /**
     * 单根长度(米)
     */
    private BigDecimal length;

    /**
     * 送货单根数
     */
    private Long sendAmount;

    /**
     * 实点根数
     */
    private Long actualAmount;

    /**
     * 送货重量
     */
    private BigDecimal sendWeight;

    /**
     * 送货重量单位
     */
    private String sendWeightUnit;

    /**
     * 根数自洽校验-理重复核根数
     */
    private BigDecimal theoryAmount;

    /**
     * 根数自洽校验-差异
     */
    private BigDecimal theoryAmountDif;

    /**
     * 根数自洽校验-结果 1 合格, 2 不合格
     */
    private Integer theoryAmountResult;

    /**
     * 重量自洽校验-理重复核重量
     */
    private BigDecimal theoryWeight;

    /**
     * 重量自洽校验-差异
     */
    private BigDecimal theoryWeightDif;

    /**
     * 重量自洽校验-结果 1 合格, 2 不合格
     */
    private Integer theoryWeightResult;

    /**
     * 反向根数复核-理重复核根数
     */
    private BigDecimal reverseTheoryAmount;

    /**
     * 反向根数复核-差异
     */
    private BigDecimal reverseTheoryAmountDif;

    /**
     * 反向根数复核-结果 1 合格, 2 不合格
     */
    private Integer reverseTheoryAmountResult;

    /**
     * 反向重量复核-理重复核重量
     */
    private BigDecimal reverseTheoryWeight;

    /**
     * 反向重量复核-差异
     */
    private BigDecimal reverseTheoryWeightDif;

    /**
     * 反向重量复核-结果 1 合格, 2 不合格
     */
    private Integer reverseTheoryWeightResult;

    /**
     * 确认根数
     */
    private BigDecimal confirmAmount;

    /**
     * 确认重量
     */
    private BigDecimal confirmWeight;
}
