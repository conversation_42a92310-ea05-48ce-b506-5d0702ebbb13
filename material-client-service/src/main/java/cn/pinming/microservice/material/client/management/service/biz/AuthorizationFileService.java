package cn.pinming.microservice.material.client.management.service.biz;

import cn.pinming.microservice.material.client.management.common.form.AuthorizationFileBindForm;
import cn.pinming.microservice.material.client.management.common.form.AuthorizationFileForm;
import cn.pinming.microservice.material.client.management.common.vo.AuthorizationFileDownloadVO;
import cn.pinming.microservice.material.client.management.common.vo.AuthorizationFileVO;

import java.util.List;

public interface AuthorizationFileService {
    List<AuthorizationFileVO> getAuthorizationFiles(String uid);

    void addOrDelAuthorizationFiles(AuthorizationFileForm authorizationFileForm);

    void bindAuthorizationFile(AuthorizationFileBindForm authorizationFileBindForm);

    AuthorizationFileDownloadVO downloadAuthorizationFile(Long id);

    Boolean authorizationFileIsValid(Long id);
}
