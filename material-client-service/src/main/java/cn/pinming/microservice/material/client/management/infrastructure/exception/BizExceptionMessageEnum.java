package cn.pinming.microservice.material.client.management.infrastructure.exception;

/**
 * <AUTHOR>
 * @description
 */
public enum BizExceptionMessageEnum {

    //基础
    FORM_DATA_NOT_FOUND("400", "数据不存在"),
    LOGIC_ERROR("-20004", "逻辑错误"),

    // 平台
    PLATFORM_AUTH_ERROR("-10000", "平台认证失败"),
    PLATFORM_ACCESS_TOKEN_ERROR("-10001", "AccessToken未配置"),
    PLATFORM_ACCESS_TOKEN_EXPIRED("-10002", "AccessToken失效"),



    NOT_LOGIN_ERROR("-1", "登录失效，请重新登录"),
    SALT_ERROR("-2", "加密盐值错误"),
    GRAPH_CAPTCHA_INVALID_ERROR("-3", "图形验证码失效，请重新生成"),
    GRAPH_CAPTCHA_ERROR("-4", "图形验证码错误，请重新输入"),
    USER_NOT_FOUND_ERROR("-5", "用户名或密码错误"),
    EMAIL_REGISTERED_ERROR("-6", "该邮箱已注册"),
    PASSWORD_ERROR("-7", "密码不正确，请重新输入"),
    EMAIL_CAPTCHA_INVALID_ERROR("-8", "邮箱验证码失效，请重新发送"),
    EMAIL_CAPTCHA_ERROR("-9", "邮箱验证码错误，请重新输入"),
    CLIENT_UNREGISTERED("-23", "设备未注册"),
    SIGN_UP_APP("-10", "请先获取所需服务"),
    SERVICE_DISABLED("-21", "%s服务已停用"),
    APP_INVOKE_EMPTY("-11", "服务可调用次数余额为0，调用失败！"),
    APP_SPACE_EMPTY("-12", "订阅存储空间不足，数据上传失败！"),
    APP_SPACE_END("-12", "订阅存储空间已到期，数据上传失败！"),
    DEVICE_IS_EXIST("-1200", "该设备已准入"),
    DEVICE_IS_NOT_EXIST("-1201", "该设备尚未准入，详情请联系平台方。"),
    DEVICE_IS_AUDITING("-1202", "该设备审核中，详情请联系平台方。"),
    DEVICE_IS_LOCKED("-1203", "该设备非当前用户添加，无法移除此设备"),
    DEVICE_IS_USED("-1204", "该设备已禁用"),
    DECEIVE_IS_DELETED("-13", "该设备已被移除"),
    DEVICE_NOT_HAVE_ATTRIBUTION("-14", "该设备需绑定归属方才可发送数据"),
    DEVICE_IS_OWN("-15", "该设备已被您占有"),
    ATTRIBUTION_IS_EXIST("-16", "该归属方code已存在，请确认。"),
    ATTRIBUTION_HAS_DEVICE("-17", "该归属方已绑定设备，不可删除。"),
    ATTRIBUTION_HAS_DATA("-18", "该归属方已产生终端数据，不可删除。"),
    DEVICE_HAS_NO_ATTRIBUTION("-19", "该终端设备未绑定归属方，请确认。"),
    DEVICE_HAS_NO_OCCUPIED("-20", "您尚未获得此设备占用权，请先在设备列表中添加此设备。"),
    DEVICE_IS_NOT_RECEIVE("-22", "该设备处于'暂停接收'状态"),
    ATTRIBUTION_IS_EMPTY("-23", "您还未创建设备归属方"),
    ATTRIBUTION_IS_ERROR("-24", "您还未创建设备归属方"),
    WEIDATE_IS_NO_EXIST("-25", "请求称重数据并不存在"),
    WEIGHDATA_TYPE_SAME("-26", "重组数据只允许称重类型为'载车称重'"),
    RECORDID_REPEAT("-27", "请求称重数据id重复"),
    PASSWORD_IS_ERROR("-28", "密码需6位纯数字"),
    DEVICE_IS_OTHERS("-29", "该设备已被其他租户所占用。"),
    USER_SPACE_OVER("-30", "当前业务数据已占空间大于所选容量。"),
    USER_SPACE_EXPIRE("-31", "请选择将来的日期"),
    VERSION_EXIST("-33", "当前类型版本号已存在"),
    RISK_GRADE_EMPTY("-34", "风险等级不能为空"),
    RISK_GRADE_ILLEGALITY("-35", "非法的风险等级"),
    RISK_GRADE_ERROR("-36", "修改的风险等级和原风险等级一致"),
    TRUCK_NO_EMPTY("-37", "车牌号不能为空"),
    TRUCK_NO_ERROR("-38", "修改的车牌号和原车牌号一致"),
    NOT_ADMIN_ERROR("-39", "非管理员，无权限"),
    AUTHORIZATION_FILE_NOT_FOUND_ERROR("-40", "授权文件不存在"),
    AUTHORIZATION_FILE_BIND_NOT_FOUND_ERROR("-41", "授权文件绑定的数据归属方不存在"),
    AUTHORIZATION_FILE_AES_ENCRYPT_ERROR("-41", "授权文件内容加密异常"),
    AUTHORIZATION_FILE_BIND_EXIST_ERROR("-42", "数据归属方已绑定授权文件"),
    BEYOND_MAX_SIZE_ERROR("-43", "一次查询最大数据量不能超过1000"),
    QUERY_TIME_ERROR("-44", "查询开始时间不能为空"),
    TRUCK_NO_MAX_SIZE_ERROR("-45", "一次查询车牌号数量不能超过500"),
    QUERY_PARAM_NULL_ERROR("-46", "参数不能为空"),
    MODULE_DETAIL_CAN_NOT_REPEAT("-47", "同一数据组的数据键名称不能重复"),
    MODULE_IS_NOT_EXIST("-48", "该归属方下无模版"),
    MAIN_POINT_SIZE_ERROR("-49", "主锚只能定义一个"),
    MAIN_POINT_REPEAT("-50", "主锚租户内需唯一"),
    OCR_NOT_AVAILABLE("-51", "面单识别服务不可用!(余额不足或订阅到期)"),
    MODULE_TYPE_FIVE_ERROR("-52", "平台模版只能由平台管理员创建"),
    APP_NOT_CONFIG("-53", "该服务不可用!"),
    OCR_MATCH_EMPTY("-232", "单据识别模板数据为空"),
    OCR_MATCH_ERROR("-233", "单据识别服务调用失败"),
    OCR_MODULE_KEY_TYPE_MISMATCH("-231", "键值类型不匹配"),
    RECYCLE_WEIGH_DATA_ERROR("-234", "称重数据异常，不能回收"),
    RECYCLE_MODULE_DATA_ERROR("-235", "数据组键值异常，不能回收"),
    PUSH_ATTRIBUTION_ERROR("-236", "归属方不可选择多个推送服务"),

    RECYCLE_CHOOSE_MODULE_EXIST("-201", "选择模板已存在或数据不存在"),
    RECYCLE_STATUS_SUCCESS_ERROR("-236", "单据回收已成功，不能作废"),
    RECYCLE_INVALID_OPERATE_ERROR("-237", "单据回收作废操作失败"),
    RECYCLE_SUCCESS_OPERATE_ERROR("-237", "单据回收确认操作失败"),
    RECYCLE_UPLOAD_FILE_ERROR("-237", "单据回收上传图片失败"),
    HAS_VERIFY("-238", "该验收记录已归档"),
    REVERSEWEIGHTTYPE_IS_NULL("-239", "请先选择反向复核总重适用类型"),
    CONSUMER_HAS_PHONESN("-240", "该用户已添加该设备"),
    OTHER_CONSUMER_HAS_PHONESN("-241", "有其他用户已添加该设备"),
    CONSUMER_LOGIN_ERROR("-242", "请在基石平台添加该设备或为该设备绑定指定归属方"),
    CONSUMER_DELETED("-243", "该用户已被删除"),
    PHONESN_ERROR("-244", "请先把设备绑定该用户"),
    PHONGSN_UNABLE("-245", "该设备已被禁用"),
    DATE_ARRIVED("-246", "订阅日期已到"),
    MANAGE_MATERIAL("-247", "请先维护材料信息"),
    DEVICE_ATTRIBUTION_ERROR("-248", "请在基石平台为该设备绑定指定归属方"),

    ATTRIBUTION_CANNOT_REPEAT("-238", "归属方code账号内不可重复"),
    RECORD_HAS_USED("-239", "称重小票已被使用,不可重复使用!"),
    CONFIRM_HAS_NOT_UPLOAD("-240", "确认单不存在"),
    ORDER_IS_EMPTY("-241","同步运单所需订单为空"),
    ORDER_IS_NOT_EXITS("-242","id为:%s的采购单未找到对应的基石订单,uid为:%s,attributionId为:%s"),
    ORDER_IS_HAS_SYNC("-242","运单编号为:%s已在attributionId为:%s账号下同步过"),
    CURVE_WEIGH_POINT_ERROR("-243","称重数据点数量异常,请联系相关技术人员"),
    CURVE_CONFIG_EXIST("-244","该归属方已有称重曲线配置"),

    ;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String errorMessage;

    BizExceptionMessageEnum(String errorCode, String errorMessage) {
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }

    public String errorCode() {
        return errorCode;
    }

    public String errorMessage() {
        return errorMessage;
    }
}
