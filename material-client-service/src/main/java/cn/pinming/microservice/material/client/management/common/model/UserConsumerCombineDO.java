package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 租户用户绑定表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("s_user_consumer_combine")
public class UserConsumerCombineDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 用户id
     */
    private Long consumeId;

    /**
     * 设备绑定码
     */
    private String phoneSn;

    /**
     * 用户id
     */
    private String uid;

    /**
     * 是否启用 0 启用 1 禁用
     */
    private Integer isEnable;
}
