package cn.pinming.microservice.material.client.management.common.form;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class DeliveryAddForm {

    @ApiModelProperty("基石订单ID")
    @NotNull(message = "订单ID为空")
    private Long orderId;

    @ApiModelProperty("发货车牌")
    @NotBlank(message = "发货车牌为空")
    private String truckNo;

    @ApiModelProperty("发货司机")
    @NotBlank(message = "发货司机为空")
    private String driver;

    @ApiModelProperty("发货司机手机号")
    @NotBlank(message = "发货司机手机号为空")
    private String driverMobile;

    @ApiModelProperty("发货明细列表")
    @NotNull(message = "发货明细列表为空")
    @Size(min = 1, message = "发货明细列表为空")
    @Valid
    private List<DeliveryItemForm> list;

    @ApiModelProperty("归属方ID")
    private Long attributionId;

    @ApiModelProperty("打印模板ID")
    private Long printTemplateId;

}
