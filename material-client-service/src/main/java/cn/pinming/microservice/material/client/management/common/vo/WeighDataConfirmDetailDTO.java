package cn.pinming.microservice.material.client.management.common.vo;

import cn.pinming.microservice.material.client.management.common.vo.delivery.DeliveryDetailDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class WeighDataConfirmDetailDTO {
    @ApiModelProperty(value = "确认单本地id")
    private String localId;

    @ApiModelProperty(value = "车牌号码")
    private String truckNo;

    @ApiModelProperty(value = "称重类型 1 收料 2 发料")
    private Integer weighType;

    @ApiModelProperty(value = "毛重")
    private BigDecimal weightGross;

    @ApiModelProperty(value = "皮重")
    private BigDecimal weightTare;

    @ApiModelProperty(value = "扣重")
    private BigDecimal weightDeduct;

    @ApiModelProperty(value = "净重")
    private BigDecimal weightNet;

    @ApiModelProperty(value = "含水率")
    private BigDecimal moistureContent;

    @ApiModelProperty(value = "实重")
    private BigDecimal weightActual;

    @ApiModelProperty(value = "换算系数")
    private BigDecimal ratio;

    @ApiModelProperty(value = "实际数量：实重 / 换算系数")
    private BigDecimal actualCount;

    @ApiModelProperty(value = "结算单位")
    private String weightUnit;

    @ApiModelProperty(value = "毛重时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime grossTime;

    @ApiModelProperty(value = "皮重时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime tareTime;

    @ApiModelProperty(value = "毛重记录id")
    private String grossId;

    @ApiModelProperty(value = "皮重记录id")
    private String tareId;

    @ApiModelProperty(value = "毛重风险等级")
    private String grossRiskGrade;

    @ApiModelProperty(value = "毛重照片")
    private String grossPic;

    @ApiModelProperty(value = "皮重照片")
    private String tarePic;

    @ApiModelProperty(value = "皮重风险等级")
    private String tareRiskGrade;

    @ApiModelProperty(value = "单据照片")
    private String documentPic;

    @ApiModelProperty(value = "签名照片")
    private String signPic;

    @ApiModelProperty(value = "签名人照片")
    private String signerPic;

    @ApiModelProperty(value = "确认单打印次数")
    private Integer printCount;

    @ApiModelProperty(value = "发货单详情")
    private DeliveryDetailDTO deliveryDetailVO;

    @ApiModelProperty(value = "确认单号")
    private String confirmNo;

    @ApiModelProperty(value = "推送状态 1 未推送 2 已推送 3 推送失败")
    private Integer pushState;

    @ApiModelProperty(value = "确认单本地创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime localCreateTime;

    @ApiModelProperty(value = "设备sn")
    private String deviceSn;

    @ApiModelProperty(value = "数据归属方名称")
    private String attributionName;

    @ApiModelProperty(value = "数据归属方code")
    private String attributionCode;
}
