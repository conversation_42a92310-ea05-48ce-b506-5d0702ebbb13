package cn.pinming.microservice.material.client.management.common.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2023/8/8 13:35
 */
@Data
public class UserSubscribeInfoVO {

    /**
     * 购买空间， 单位GB
     */
    private BigDecimal spaceSize;

    /**
     * 使用空间， 单位GB
     */
    private BigDecimal spaceUseSize;

    /**
     * 购买空间到期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime spaceExpire;

    /**
     * 已购买服务调用总次数
     */
    private Long apiTotal;

    /**
     * 实际服务调用次数
     */
    private Long apiUseTotal;

    /**
     * 剩余称重数据服务调用api
     */
    private Long remainingApi;

    /**
     * 剩余单据服务调用api
     */
    private Long remainingReceiptApi;

    /**
     * 剩余天数
     */
    private Integer remainingDays;

    /**
     * 称重数据历史服务调用情况
     */
    private List<AppInvocationVO> list;

    /**
     * 单据匹配历史服务调用情况
     */
    private List<AppInvocationVO> receiptInvocationList;

}
