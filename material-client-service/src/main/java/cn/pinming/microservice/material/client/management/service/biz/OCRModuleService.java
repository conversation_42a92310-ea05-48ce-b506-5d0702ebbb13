package cn.pinming.microservice.material.client.management.service.biz;

import cn.pinming.microservice.material.client.management.common.form.OCRModuleForm;
import cn.pinming.microservice.material.client.management.common.model.OcrModuleDO;
import cn.pinming.microservice.material.client.management.common.query.OCRModuleQuery;
import cn.pinming.microservice.material.client.management.common.vo.OCRModuleVO;
import cn.pinming.microservice.material.client.management.common.vo.ocr.OCRTemplateVO;
import cn.pinming.microservice.material.client.management.common.vo.ocr.OCRVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface OCRModuleService extends IService<OcrModuleDO> {
    Long addOCRModule(OCRModuleForm form);

    void updateOCRModule(OCRModuleForm form);

    void refreshOCRModule(Long moduleId);

    List<OCRModuleVO> listOCRModule(OCRModuleQuery query);

    OCRModuleVO detailOCRModule(Long moduleId);

    List<OCRModuleVO> chooseOCRModule(OCRModuleQuery query);

    List<OCRModuleVO> allOCRModule(OCRModuleQuery query);

    List<OCRVO> listModuleDetail(String uid, String attributionCode);

    List<OCRTemplateVO> listTemplateDetail(String uid, String attributionCode);
}
