package cn.pinming.microservice.material.client.management.common.vo.selfcheck;


import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PurchaseBaseInfoVO {

    @ApiModelProperty("该发货单所属订单在基石平台的唯一编号，不能为空")
    @JsonProperty("jsBillOfLadingNumber")
    private Long id;

    @ApiModelProperty("业务系统中此订单的唯一ID，不能为空")
    private String purchaseId;

    @ApiModelProperty("业务系统中订单对应供应商的唯一ID，不能为空")
    private String supplierId;

    @ApiModelProperty("供应商名称，不能为空")
    private String supplierName;

    @ApiModelProperty("订单拥有者的名称，一般指项目名称，不能为空")
    private String orderName;

    @ApiModelProperty("订单拥有者的地址，一般指项目收货地址，可为空，但建议设置")
    private String orderAddress;

    @ApiModelProperty("收货人姓名，可以为空，但建议设置")
    private String cargoReceiver;

    @ApiModelProperty("收货人电话，可以为空，但建议设置")
    private String receiverTelNumber;

    @ApiModelProperty("要货日期，不能为空")
    private String requireDate;

    @ApiModelProperty("订单备注，可为空")
    private String remark;

    @ApiModelProperty("归属方")
    private String attributionName;
}
