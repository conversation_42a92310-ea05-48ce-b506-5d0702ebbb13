package cn.pinming.microservice.material.client.management.common.form.supplier;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class PrintTemplateForm {

    @ApiModelProperty(value = "模板ID")
    private Long id;

    @ApiModelProperty(value = "打印业务类型  0 司机确认单(单据回收) 1 发货单  2 司机确认单(ocr) ")
    @NotNull(message = "打印业务类型为空")
    private Byte type;

    @ApiModelProperty(value = "打印模板样式")
    @NotNull(message = "打印模板样式为空")
    private Byte style;

    @ApiModelProperty(value = "打印小票类型")
    @NotNull(message = "打印小票类型为空")
    private Byte formType;

    @ApiModelProperty(value = "名称")
    @NotBlank(message = "模板名称为空")
    private String name;

}
