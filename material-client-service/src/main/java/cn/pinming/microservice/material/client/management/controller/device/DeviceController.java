package cn.pinming.microservice.material.client.management.controller.device;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import cn.pinming.microservice.material.client.management.common.enums.DeviceTypeEnum;
import cn.pinming.microservice.material.client.management.common.mapper.ext.DeviceExtMapper;
import cn.pinming.microservice.material.client.management.common.model.DeviceBindingDO;
import cn.pinming.microservice.material.client.management.common.model.DeviceDO;
import cn.pinming.microservice.material.client.management.common.vo.DeviceVO;
import cn.pinming.microservice.material.client.management.infrastructure.constant.AuthorizationFileConstant;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizErrorException;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizExceptionMessageEnum;
import cn.pinming.microservice.material.client.management.infrastructure.response.SingleResponse;
import cn.pinming.microservice.material.client.management.infrastructure.util.AesEncryptUtil;
import cn.pinming.microservice.material.client.management.service.biz.DeviceBindingService;
import cn.pinming.microservice.material.client.management.service.biz.DeviceService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api(value = "设备准入-管理员", tags = {"device"})
@RestController
@RequestMapping("/api/device")
public class DeviceController {
    @Resource
    private DeviceService deviceService;
    @Resource
    private DeviceBindingService deviceBindingService;
    @Resource
    private DeviceExtMapper deviceExtMapper;

    @ApiOperation(value = "设备准入", responseReference = "SingleResponse«?»", nickname = "device")
    @PostMapping
    public SingleResponse<?> device(@RequestBody DeviceDO deviceDO) {
        DeviceDO one = deviceService.lambdaQuery()
                .eq(DeviceDO::getDeviceSn, deviceDO.getDeviceSn())
                .eq(DeviceDO::getDeviceType, deviceDO.getDeviceType())
                .one();
        if (ObjectUtil.isNotNull(one)) {
            throw new BizErrorException(BizExceptionMessageEnum.DEVICE_IS_EXIST);
        } else {
            if (deviceDO.getDeviceType().equals(DeviceTypeEnum.LOCAL.name())
                    && deviceDO.getExpireDate() == null) {
                throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "请设置授权截止日期");
            }
            deviceService.save(deviceDO);
        }
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "移除", responseReference = "SingleResponse«?»", nickname = "deviceRemove")
    @GetMapping("/remove/{deviceId}")
    public SingleResponse<?> remove(@PathVariable("deviceId") Long deviceId) {
        deviceService.removeById(deviceId);
        QueryWrapper<DeviceBindingDO> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(DeviceBindingDO::getDeviceId, deviceId);
        deviceBindingService.remove(wrapper);
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "启用禁用", responseReference = "SingleResponse«?»", nickname = "deviceUsed")
    @GetMapping("/used/{deviceId}/{isUsed}")
    public SingleResponse<?> used(@PathVariable("deviceId") Long deviceId, @PathVariable("isUsed") Byte isUsed) {
        deviceService.lambdaUpdate()
                .eq(DeviceDO::getId, deviceId)
                .set(DeviceDO::getIsUsed, isUsed)
                .update();
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "列表", responseReference = "SingleResponse«List<DeviceDO>»", nickname = "deviceList")
    @GetMapping("/list")
    public SingleResponse<?> list(@RequestParam(value = "deviceType", required = false) String deviceType) {
        List<DeviceVO> list = deviceExtMapper.selectListByDeviceType(deviceType);
        return SingleResponse.of(list);
    }

    @ApiOperation(value = "设备编辑", responseReference = "SingleResponse«?»", nickname = "deviceEdit")
    @PostMapping("/edit")
    @Transactional(rollbackFor = Exception.class)
    public SingleResponse<?> deviceEdit(@RequestBody DeviceDO deviceDO) {
        Long id = deviceDO.getId();
        if (id == null) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "设备ID为空");
        }

        Integer count = deviceService.lambdaQuery().ne(DeviceDO::getId, id)
                .eq(DeviceDO::getDeviceSn, deviceDO.getDeviceSn())
                .eq(DeviceDO::getDeviceType, deviceDO.getDeviceType())
                .count();
        if (count > 0) {
            throw new BizErrorException(BizExceptionMessageEnum.DEVICE_IS_EXIST);
        }

        if (deviceDO.getDeviceType().equals(DeviceTypeEnum.LOCAL.name())
                && deviceDO.getExpireDate() == null) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "请设置授权截止日期");
        }
        deviceService.updateById(deviceDO);
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "下载证书", responseReference = "SingleResponse«?»", nickname = "license")
    @GetMapping("/license/{deviceId}")
    @Transactional(rollbackFor = Exception.class)
    public ResponseEntity<?> license(@PathVariable Long deviceId) throws Exception {
        DeviceDO deviceDO = deviceService.getById(deviceId);
        if (deviceDO == null) {
            throw new BizErrorException(BizExceptionMessageEnum.CLIENT_UNREGISTERED);
        }
        Long id = deviceDO.getId();
        String deviceSn = deviceDO.getDeviceSn();
        LocalDate expireDate = deviceDO.getExpireDate();
        Map<String, String> map = new HashMap<>();
        map.put("id", String.valueOf(id));
        map.put("deviceSn", deviceSn);
        map.put("expireDate", expireDate.toString());
        String json = AesEncryptUtil.aesEncrypt(JSONUtil.toJsonStr(map), AuthorizationFileConstant.SECRET_KEY);

        String fileName = "key.lic";
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"))
                .contentLength(json.length())
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(json.getBytes());
    }

}
