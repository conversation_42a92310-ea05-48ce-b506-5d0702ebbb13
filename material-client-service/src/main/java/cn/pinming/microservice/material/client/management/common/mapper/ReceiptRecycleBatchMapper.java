package cn.pinming.microservice.material.client.management.common.mapper;

import cn.pinming.microservice.material.client.management.common.model.ReceiptRecycleBatchDO;
import cn.pinming.microservice.material.client.management.common.vo.ReceiptRecycleBatchVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 单据回收批次表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-11
 */
public interface ReceiptRecycleBatchMapper extends BaseMapper<ReceiptRecycleBatchDO> {

    List<ReceiptRecycleBatchVO> selectByQuery(@Param("uid") String uid, @Param("keyword") String keyword);

    List<ReceiptRecycleBatchDO> selectDeviceIdListByBatchId(@Param("id") Long id, @Param("deviceIdList") List<Long> deviceIdList, @Param("uid") String uId);

    ReceiptRecycleBatchDO selectBatchByDeviceId(@Param("deviceId") Long deviceId, @Param("uid") String uid);
}
