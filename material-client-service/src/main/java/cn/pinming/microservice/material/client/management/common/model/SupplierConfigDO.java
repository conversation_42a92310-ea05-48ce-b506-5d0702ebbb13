package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 订单收发货-供应商关联配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("s_supplier_config")
public class SupplierConfigDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 供应商名称
     */
    private String name;

    /**
     * 来自业务系统的供应商唯一ID
     */
    private String supplierExtId;

    /**
     * 供应商在基石平台的ID(UID)
     */
    private String supplierSysId;

    /**
     * 运单推送  0 关闭 1 开启
     */
    private Integer deliveryPush;

    /**
     * 确认单推送  0 关闭 1 开启
     */
    private Integer confirmPush;

    /**
     * 组装超时时间 单位 分
     */
    private Integer timeout;

    /**
     * 分组
     */
    @TableField(value = "`group`")
    private String group;

    /**
     * 一车多料  0 不允许 1 允许
     */
    private Integer multiCargo;

    @ApiModelProperty(value = "最短时长 单位 分")
    private Integer sameTruckMinDuration;

}
