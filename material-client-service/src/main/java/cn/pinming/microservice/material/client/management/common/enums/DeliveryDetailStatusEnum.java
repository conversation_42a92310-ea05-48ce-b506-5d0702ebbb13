package cn.pinming.microservice.material.client.management.common.enums;

import java.util.Objects;

public enum DeliveryDetailStatusEnum {
    ONE(1, "在途"),
    TWO(2, "待确认"),
    THREE(3, "自助确认中"),
    FOUR(4, "已确认"),
    FIVE(5, "已作废"),
    ;

    private final Integer type;
    private final String description;

    DeliveryDetailStatusEnum(Integer type, String description) {
        this.type = type;
        this.description = description;
    }

    public Integer value() {
        return type;
    }

    public String description() {
        return description;
    }

    public static String descByType(Integer type) {
        for (DeliveryDetailStatusEnum value : DeliveryDetailStatusEnum.values()) {
            if (Objects.equals(value.type, type)) {
                return value.description;
            }
        }
        return null;
    }
}
