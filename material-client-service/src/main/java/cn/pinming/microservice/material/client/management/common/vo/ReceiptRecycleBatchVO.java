package cn.pinming.microservice.material.client.management.common.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class ReceiptRecycleBatchVO {

    @ApiModelProperty("批次id")
    private Long id;

    @ApiModelProperty("批次名称")
    private String name;

    @ApiModelProperty("归属方名称")
    private String attributionName;

    @ApiModelProperty("状态 0 未归档 1 已归档")
    private Byte status;

    @ApiModelProperty("扩展编码")
    private String extCode;

    @ApiModelProperty("归属方id")
    private Long attributionId;

    @ApiModelProperty("设备id")
    @JsonIgnore
    private String deviceId;

    @ApiModelProperty("设备id列表")
    private List<Long> deviceIdList;

    @ApiModelProperty("创建时间")
    private LocalDateTime gmtCreate;

    @ApiModelProperty("回收数量")
    private Integer amount;

}
