package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.pinming.microservice.material.client.management.common.model.WeighCurveAlarmDO;
import cn.pinming.microservice.material.client.management.common.mapper.WeighCurveAlarmMapper;
import cn.pinming.microservice.material.client.management.service.biz.IWeighCurveAlarmService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 原始记录称重曲线报警日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
@Service
public class WeighCurveAlarmServiceImpl extends ServiceImpl<WeighCurveAlarmMapper, WeighCurveAlarmDO> implements IWeighCurveAlarmService {

}
