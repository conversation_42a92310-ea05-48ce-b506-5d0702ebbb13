package cn.pinming.microservice.material.client.management.common.form;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class DeliverySyncMaterialForm {
    /**
     * 货物品牌，可为空
     */
    private String cargoBrand;

    /**
     * 业务系统中的订单明细id，可为空，但不能与jsCargoId同时为空
     */
    private String cargoId;

    /**
     * 货物规格，不能为空
     */
    @NotBlank(message = "货物规格不能为空")
    private String cargoModel;

    /**
     * 货物名称，不能为空
     */
    @NotBlank(message = "货物名称不能为空")
    private String cargoName;

    /**
     * 货物其他参数，可为空
     */
    private String cargoParameter;

    /**
     * 货物使用部位名称，可为空，但强烈建议设置
     */
    private String cargoUsage;

    /**
     * 货物使用部位id，可为空，但强烈建议设置
     */
    private String cargoUsageId;

    /**
     * 基石平台上订单明细id，可为空
     */
    private String jsCargoId;

    /**
     * 货物备注，可为空
     */
    private String remark;

    /**
     * 运单数量，不能为空
     */
    @NotNull(message = "运单数量不能为空")
    private BigDecimal waybillCounts;

    /**
     * 运单数量单位
     */
    @NotBlank(message = "运单数量单位不能为空")
    private String waybillUnit;

    /**
     * 称重计算用到的参数
     */
    @NotNull(message = "称重计算用到的参数不能为空")
    private DeliverySyncMaterialCulForm calParameters;
}
