package cn.pinming.microservice.material.client.management.service.biz;

import cn.pinming.microservice.material.client.management.common.form.ReceiptRecycleBatchEditForm;
import cn.pinming.microservice.material.client.management.common.form.ReceiptRecycleBatchForm;
import cn.pinming.microservice.material.client.management.common.form.ReceiptRecycleDataBatchEditForm;
import cn.pinming.microservice.material.client.management.common.model.ReceiptRecycleBatchDO;
import cn.pinming.microservice.material.client.management.common.vo.ReceiptRecycleBatchVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 单据回收批次表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-11
 */
public interface IReceiptRecycleBatchService extends IService<ReceiptRecycleBatchDO> {

    List<ReceiptRecycleBatchVO> listByQuery(String keyword);

    void add(ReceiptRecycleBatchForm form);

    void deleteById(Long id);

    void archiveById(Long id);

    void extCodeEdit(ReceiptRecycleBatchEditForm form);

    void deviceBindEdit(ReceiptRecycleBatchEditForm form);

    void dataBatchEdit(ReceiptRecycleDataBatchEditForm form);

    ReceiptRecycleBatchDO checkDeviceId(Long deviceId, String uid);
}
