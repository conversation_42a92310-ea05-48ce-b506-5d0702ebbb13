package cn.pinming.microservice.material.client.management.controller.qrcode;


import cn.pinming.microservice.material.client.management.infrastructure.response.SingleResponse;
import cn.pinming.microservice.material.client.management.common.form.supplier.SelfQrcodeEditForm;
import cn.pinming.microservice.material.client.management.common.form.supplier.SelfQrcodeForm;
import cn.pinming.microservice.material.client.management.common.model.DeviceAttributionDO;
import cn.pinming.microservice.material.client.management.common.vo.SelfQrcodeVO;
import cn.pinming.microservice.material.client.management.service.biz.ISelfQrcodeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 磅房自助运单入口码管理设置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-20
 */
@Api(value = "磅房自助运单入口码管理", tags = {"self-qrcode"})
@RestController
@RequestMapping("/api/self/qrcode")
public class SelfQrcodeController {

    @Resource
    private ISelfQrcodeService selfQrcodeService;

    @ApiOperation(value = "二维码列表", responseReference = "SingleResponse«List<SelfQrcodeVO>»", nickname = "qrcodeList")
    @GetMapping("/list")
    public SingleResponse<?> list(@RequestParam Integer type) {
        List<SelfQrcodeVO> result = selfQrcodeService.listConfig(type);
        return SingleResponse.of(result);
    }

    @ApiOperation(value = "新增", responseReference = "SingleResponse«?»", nickname = "qrcodeAdd")
    @PostMapping("/add")
    public SingleResponse<?> add(@Validated @RequestBody SelfQrcodeForm form) {
        selfQrcodeService.add(form);
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "启用禁用", responseReference = "SingleResponse«?»", nickname = "qrcodeEnable")
    @GetMapping("/{id}/enable")
    public SingleResponse<?> enable(@PathVariable Long id) {
        selfQrcodeService.enableById(id);
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "设置", responseReference = "SingleResponse«?»", nickname = "qrcodeEdit")
    @PostMapping("/edit")
    public SingleResponse<?> edit(@Validated @RequestBody SelfQrcodeEditForm form) {
        selfQrcodeService.edit(form);
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "可选归属方列表(已创建入口码的数据归属方置灰不可选)", nickname = "attributeList")
    @GetMapping("/attribute/list")
    public SingleResponse<?> attributeList() {
        List<DeviceAttributionDO> list = selfQrcodeService.attributeList();
        return SingleResponse.of(list);
    }

}

