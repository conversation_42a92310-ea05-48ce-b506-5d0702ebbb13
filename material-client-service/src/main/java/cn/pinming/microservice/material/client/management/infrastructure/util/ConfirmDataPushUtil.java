package cn.pinming.microservice.material.client.management.infrastructure.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.microservice.material.client.management.common.dto.push.*;
import cn.pinming.microservice.material.client.management.common.mapper.DeliveryDetailMapper;
import cn.pinming.microservice.material.client.management.common.mapper.ext.WeighDataConfirmExtMapper;
import cn.pinming.microservice.material.client.management.common.model.WeighDataPicDO;
import cn.pinming.microservice.material.client.management.common.model.ext.WeighDataGrossExtDO;
import cn.pinming.microservice.material.client.management.common.model.ext.WeighDataTareExtDO;
import cn.pinming.microservice.material.client.management.service.biz.WeighDataPicService;
import cn.pinming.zhjz.cxptz.microservice.filecenter.api.FileCenterService;
import cn.pinming.zhjz.cxptz.microservice.filecenter.api.model.DownloadUrlOptionsDto;
import cn.pinming.zhjz.cxptz.microservice.filecenter.api.model.FileIdentityDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ConfirmDataPushUtil {
    @Resource
    private WeighDataConfirmExtMapper weighDataConfirmExtMapper;
    @Resource
    private DeliveryDetailMapper deliveryDetailMapper;
    @Resource
    private WeighDataPicService weighDataPicService;
    @DubboReference
    private FileCenterService fileCenterService;

    public List<PushConfirmDTO> getConfirmPushData(List<Long> ids) {
        List<PushConfirmDbDTO> list = weighDataConfirmExtMapper.getConfirmPushData(ids);
        if (CollUtil.isNotEmpty(list)) {
            Map<String, PushConfirmMaterialDTO> deliveryDetailDOMap = new HashMap<>();
            Map<String, String> weighDataIdPicMap = new HashMap<>();
            List<String> recordIdList = new ArrayList<>();
            List<String> recordPicUuidList = new ArrayList<>();
            List<WeighDataPicDO> recordPicList = new ArrayList<>();
            Map<String,String> downloadMap = new HashMap<>();

            // 签名、签名人、单据照片map
            List<String> picList = new ArrayList<>();
            List<String> signPic = list.stream().map(PushConfirmDbDTO::getSignPic).filter(StrUtil::isNotBlank).collect(Collectors.toList());
            List<String> signerPic = list.stream().map(PushConfirmDbDTO::getSignerPic).filter(StrUtil::isNotBlank).collect(Collectors.toList());
            List<String> documentPic = list.stream().filter(e -> StrUtil.isNotBlank(e.getDocumentPic())).flatMap(e -> StrUtil.split(e.getDocumentPic(), ",").stream()).collect(Collectors.toList());
            picList.addAll(signerPic);
            picList.addAll(signPic);
            picList.addAll(documentPic);
            // 过磅照片
            List<String> recordId1 = list.stream().map(PushConfirmDbDTO::getRecordId1).distinct().collect(Collectors.toList());
            List<String> recordId2 = list.stream().map(PushConfirmDbDTO::getRecordId2).distinct().collect(Collectors.toList());
            recordIdList.addAll(recordId1);
            recordIdList.addAll(recordId2);
            if (CollUtil.isNotEmpty(recordIdList)) {
                recordPicList = weighDataPicService.lambdaQuery()
                        .in(WeighDataPicDO::getRecordId, recordIdList)
                        .list();
                if (CollUtil.isNotEmpty(recordPicList)) {
                    recordPicUuidList = recordPicList.stream().map(WeighDataPicDO::getFileId).collect(Collectors.toList());
                    picList.addAll(recordPicUuidList);
                }
            }
            if (CollUtil.isNotEmpty(picList)) {
                DownloadUrlOptionsDto options = new DownloadUrlOptionsDto();
                options.setTime(Date.from(LocalDate.now().plusYears(10).atStartOfDay(ZoneId.systemDefault()).toInstant()));
                List<FileIdentityDto> fileIdentities = picList.stream().map(e -> {
                    FileIdentityDto dto = new FileIdentityDto();
                    dto.setFileUuid(e);
                    return dto;
                }).collect(Collectors.toList());
                Map<FileIdentityDto, String> downloadDtoMap = fileCenterService.acquireFileDownloadUrls(fileIdentities, options);
               if (CollUtil.isNotEmpty(downloadDtoMap)) {
                   downloadDtoMap.forEach((k, v) -> downloadMap.put(k.getFileUuid(), v));
               }
            }
            if (CollUtil.isNotEmpty(downloadMap) && CollUtil.isNotEmpty(recordPicList)) {
                recordPicList.forEach(data -> {
                    if (StrUtil.isNotBlank(downloadMap.get(data.getFileId()))) {
                        if (weighDataIdPicMap.containsKey(data.getRecordId())) {
                            String pic = weighDataIdPicMap.get(data.getRecordId());
                            weighDataIdPicMap.put(data.getRecordId(), pic + "," + downloadMap.get(data.getFileId()));
                        } else {
                            weighDataIdPicMap.put(data.getRecordId(), downloadMap.get(data.getFileId()));
                        }
                    }
                });
            }

            // 订单明细map
            List<String> deliveryDetailIdList = list.stream().filter(e -> StrUtil.isNotBlank(e.getDeliveryDetailIds())).flatMap(e -> StrUtil.split(e.getDeliveryDetailIds(), ",").stream()).distinct().collect(Collectors.toList());
            if (CollUtil.isNotEmpty(deliveryDetailIdList)) {
                List<PushConfirmMaterialDTO> detailDOList = deliveryDetailMapper.selectInfoForConfirmPush(deliveryDetailIdList);
                if (CollUtil.isNotEmpty(detailDOList)) {
                    deliveryDetailDOMap = detailDOList.stream().collect(Collectors.toMap(PushConfirmMaterialDTO::getId, e -> e));
                }
            }

            Map<String, PushConfirmMaterialDTO> finalDeliveryDetailDOMap = deliveryDetailDOMap;
            log.error("init list size: {}",list.size());
            List<PushConfirmDTO> result = list.stream().map(e -> {
                PushConfirmDTO pushConfirmDTO = new PushConfirmDTO();

                PushConfirmDeliveryDTO pushConfirmDeliveryDTO = new PushConfirmDeliveryDTO();
                BeanUtils.copyProperties(e, pushConfirmDeliveryDTO);
                pushConfirmDeliveryDTO.setTruckNo(e.getDriverTruckNo());
                pushConfirmDTO.setJsDeliveryInfo(pushConfirmDeliveryDTO);
                pushConfirmDTO.setConfirmId(e.getConfirmId());
                pushConfirmDTO.setProjectId(e.getPrimaryCode());
                pushConfirmDTO.setId(e.getId());

                PushConfirmDataDTO pushConfirmDataDTO = new PushConfirmDataDTO();
                BeanUtils.copyProperties(e, pushConfirmDataDTO);
                if (CollUtil.isNotEmpty(downloadMap)) {
                    if (StrUtil.isNotBlank(e.getSignerPic())) {
                        pushConfirmDataDTO.setSignerPhotoUrl(downloadMap.get(e.getSignerPic()));
                    }
                    if (StrUtil.isNotBlank(e.getSignPic())) {
                        pushConfirmDataDTO.setSignaturePicUrl(downloadMap.get(e.getSignPic()));
                    }
                    if (StrUtil.isNotBlank(e.getDocumentPic())) {
                        pushConfirmDataDTO.setWaybillPhotoUrls(StrUtil.split(e.getDocumentPic(), ",").stream().map(downloadMap::get).collect(Collectors.toList()));
                    }
                }
                // 换算结果
                PushConfirmConvertResultDTO pushConfirmConvertResultDTO = new PushConfirmConvertResultDTO();
                BeanUtils.copyProperties(e, pushConfirmConvertResultDTO);
                pushConfirmDataDTO.setConvertResult(pushConfirmConvertResultDTO);
                // 称重数据
                PushConfirmWeightDTO pushConfirmWeightDTO = new PushConfirmWeightDTO();
                BeanUtils.copyProperties(e, pushConfirmWeightDTO);
                WeighDataGrossExtDO weighDataGrossExtDO = new WeighDataGrossExtDO();
                weighDataGrossExtDO.setWeightValue(e.getWeightGross());
                WeighDataTareExtDO weighDataTareExtDO = new WeighDataTareExtDO();
                weighDataTareExtDO.setWeightValue(e.getWeightTare());
                if (e.getWeighingType() == 1) {
                    weighDataGrossExtDO.setSort(1);
                    weighDataGrossExtDO.setOriginalId(e.getRecordId1());
                    weighDataGrossExtDO.setWeighTime(e.getEnterTime());
                    weighDataTareExtDO.setSort(2);
                    weighDataTareExtDO.setOriginalId(e.getRecordId2());
                    weighDataTareExtDO.setWeighTime(e.getLeaveTime());
                } else if (e.getWeighingType() == 2) {
                    weighDataGrossExtDO.setSort(2);
                    weighDataGrossExtDO.setOriginalId(e.getRecordId2());
                    weighDataGrossExtDO.setWeighTime(e.getLeaveTime());
                    weighDataTareExtDO.setSort(1);
                    weighDataTareExtDO.setOriginalId(e.getRecordId1());
                    weighDataTareExtDO.setWeighTime(e.getEnterTime());
                }
                if (CollUtil.isNotEmpty(weighDataIdPicMap)) {
                    weighDataGrossExtDO.setPhotoUrls(StrUtil.split(weighDataIdPicMap.get(weighDataGrossExtDO.getOriginalId()), ","));
                    weighDataTareExtDO.setPhotoUrls(StrUtil.split(weighDataIdPicMap.get(weighDataTareExtDO.getOriginalId()), ","));
                }
                pushConfirmWeightDTO.setGrossWeight(weighDataGrossExtDO);
                pushConfirmWeightDTO.setTareWeight(weighDataTareExtDO);
                pushConfirmDataDTO.setWeightResult(pushConfirmWeightDTO);
                pushConfirmDTO.setConfirmData(pushConfirmDataDTO);

                // 确认物料明细
                if (StrUtil.isNotBlank(e.getDeliveryDetailIds())) {
                    List<PushConfirmMaterialDTO> collect = StrUtil.split(e.getDeliveryDetailIds(), ",").stream().map(finalDeliveryDetailDOMap::get).collect(Collectors.toList());
                    pushConfirmDTO.setCargoConfirmList(collect);
                }

                return pushConfirmDTO;
            }).collect(Collectors.toList());
            log.error("final result size: {}",result.size());
            return result;
        }

        return null;
    }
}
