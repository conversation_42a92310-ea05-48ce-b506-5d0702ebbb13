package cn.pinming.microservice.material.client.management.service.biz;

import cn.pinming.microservice.material.client.management.common.dto.AnprDTO;
import cn.pinming.microservice.material.client.management.common.dto.PlateIdentifyDTO;
import cn.pinming.microservice.material.client.management.common.vo.ocr.AnprRespVO;

import java.util.List;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2023/10/26 14:27
 */
public interface PlateService {
    List<String> identify(String recordId);

    List<AnprRespVO.AnprVO> identifyByFileIds(String fileIds);

    void updatePlateAndRiskGrade(PlateIdentifyDTO identify, List<AnprDTO> anprList);
}
