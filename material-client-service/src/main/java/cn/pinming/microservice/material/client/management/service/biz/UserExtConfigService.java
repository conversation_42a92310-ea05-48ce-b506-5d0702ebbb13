package cn.pinming.microservice.material.client.management.service.biz;

import cn.pinming.microservice.material.client.management.common.form.UserExtConfigForm;
import cn.pinming.microservice.material.client.management.common.model.UserExtConfigDO;
import cn.pinming.microservice.material.client.management.common.vo.UserExtConfigVO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <AUTHOR>
 */
public interface UserExtConfigService extends IService<UserExtConfigDO> {
    void init(String uid);

    void updateUserExtConfig(UserExtConfigForm form);

    UserExtConfigVO showUserExtConfig();

    Byte getBillMatchType();
}
