package cn.pinming.microservice.material.client.management.common.mapper;

import cn.pinming.microservice.material.client.management.common.model.PushRouteConfigDO;
import cn.pinming.microservice.material.client.management.common.vo.push.PushRouteConfigVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * <p>
 * 公共推送路由配置 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-11
 */
public interface PushRouteConfigMapper extends BaseMapper<PushRouteConfigDO> {

    List<PushRouteConfigVO> selectRouteList(String uid);
}
