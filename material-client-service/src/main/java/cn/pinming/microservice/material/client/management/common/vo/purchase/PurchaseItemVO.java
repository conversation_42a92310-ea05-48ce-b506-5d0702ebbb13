package cn.pinming.microservice.material.client.management.common.vo.purchase;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class PurchaseItemVO {

    @ApiModelProperty(value = "采购单ID")
    private Long orderId;

    @ApiModelProperty(value = "外部系统明细业务ID")
    private String extId;

    @ApiModelProperty(value = "货物名称")
    private String name;

    @ApiModelProperty(value = "规格型号")
    private String spec;

    @ApiModelProperty(value = "其他参数")
    private String argument;

    @ApiModelProperty(value = "品牌")
    private String brand;

    @ApiModelProperty(value = "采购数量")
    private BigDecimal amount;

    @ApiModelProperty(value = "采购单位")
    private String unit;

    @ApiModelProperty(value = "扣杂(含水率)")
    private BigDecimal deductRatio;

    @ApiModelProperty(value = "转换系数")
    private BigDecimal scaleFactor;

    @ApiModelProperty(value = "称重换算系数 参照的重量单位：0 = 千克；1 = 吨")
    private Integer unitType;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "过磅重量单位")
    private String weighUnit;

    @ApiModelProperty(value = "累计发货数量")
    private BigDecimal sendAmount;

    @ApiModelProperty(value = "累计换算数量")
    private BigDecimal actualAmount;

    @ApiModelProperty(value = "累计过磅重量")
    private BigDecimal weightAmount;

    @ApiModelProperty(value = "计划使用部位")
    private String position;
}
