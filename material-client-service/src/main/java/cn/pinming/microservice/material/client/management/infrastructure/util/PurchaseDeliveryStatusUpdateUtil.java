package cn.pinming.microservice.material.client.management.infrastructure.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.pinming.microservice.material.client.management.common.enums.DeliveryDetailStatusEnum;
import cn.pinming.microservice.material.client.management.common.enums.PurchaseDeliveryStatusEnum;
import cn.pinming.microservice.material.client.management.common.BaseDO;
import cn.pinming.microservice.material.client.management.common.model.DeliveryDO;
import cn.pinming.microservice.material.client.management.common.model.DeliveryDetailDO;
import cn.pinming.microservice.material.client.management.common.model.PurchaseOrderDO;
import cn.pinming.microservice.material.client.management.service.biz.IDeliveryDetailService;
import cn.pinming.microservice.material.client.management.service.biz.IDeliveryService;
import cn.pinming.microservice.material.client.management.service.biz.IPurchaseOrderService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class PurchaseDeliveryStatusUpdateUtil {
    @Resource
    private IDeliveryService deliveryService;
    @Resource
    private IDeliveryDetailService deliveryDetailService;
    @Resource
    private IPurchaseOrderService purchaseOrderService;

    public void judge(Long purchaseOrderId) {
        if (ObjectUtil.isNull(purchaseOrderId)) {
            return;
        }

        List<DeliveryDO> deliveryDOList = deliveryService.lambdaQuery()
                .eq(DeliveryDO::getPurchaseOrderId, purchaseOrderId)
                .list();
        if (CollUtil.isEmpty(deliveryDOList)) {
            return;
        }

        List<DeliveryDetailDO> detailDOList = deliveryDetailService.lambdaQuery()
                .in(DeliveryDetailDO::getDeliveryId, deliveryDOList.stream().map(BaseDO::getId).collect(Collectors.toList()))
                .list();
        long finishCount = detailDOList.stream().filter(e -> e.getStatus().equals(DeliveryDetailStatusEnum.FOUR.value())).count();
        long cancelCount = detailDOList.stream().filter(e -> e.getStatus().equals(DeliveryDetailStatusEnum.FIVE.value())).count();
        if (finishCount + cancelCount == detailDOList.size()) {
            purchaseOrderService.lambdaUpdate()
                    .eq(BaseDO::getId, purchaseOrderId)
                    .set(PurchaseOrderDO::getDeliveryStatus, PurchaseDeliveryStatusEnum.THREE.value())
                    .update();
        }
    }
}
