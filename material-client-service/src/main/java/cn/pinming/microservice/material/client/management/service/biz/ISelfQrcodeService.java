package cn.pinming.microservice.material.client.management.service.biz;

import cn.pinming.microservice.material.client.management.common.form.supplier.SelfQrcodeEditForm;
import cn.pinming.microservice.material.client.management.common.form.supplier.SelfQrcodeForm;
import cn.pinming.microservice.material.client.management.common.model.DeviceAttributionDO;
import cn.pinming.microservice.material.client.management.common.model.SelfQrcodeDO;
import cn.pinming.microservice.material.client.management.common.vo.SelfQrcodeVO;
import cn.pinming.microservice.material.client.management.common.vo.SupplierConfigVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.TreeMap;

/**
 * <p>
 * 磅房自助运单入口码管理设置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-20
 */
public interface ISelfQrcodeService extends IService<SelfQrcodeDO> {

    List<SelfQrcodeVO> listConfig(Integer type);

    void add(SelfQrcodeForm form);

    void enableById(Long id);

    void edit(SelfQrcodeEditForm form);

    List<SupplierConfigVO> listSupplierConfig(String bizId);

    List<SupplierConfigVO> listSupplierConfigByDeviceSn(String deviceSn, String deviceType);

    List<DeviceAttributionDO> attributeList();

    TreeMap<String, List<SupplierConfigVO>> supplierConfigMapByDeviceSnAndType(String deviceSn, String deviceType);
}
