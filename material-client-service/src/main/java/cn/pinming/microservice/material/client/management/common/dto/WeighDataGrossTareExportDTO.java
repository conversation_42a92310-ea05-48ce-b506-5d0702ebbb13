package cn.pinming.microservice.material.client.management.common.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 仅毛皮重称重记录导出DTO
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
public class WeighDataGrossTareExportDTO {

    @ExcelProperty("确认单单号")
    private String confirmNo;

    @ExcelProperty("所属归属方")
    private String attributionName;

    @ExcelProperty("车牌号")
    private String truckNo;

    @ExcelProperty("毛重")
    private BigDecimal weightGross;

    @ExcelProperty("皮重")
    private BigDecimal weightTare;

    @ExcelProperty("净重")
    private BigDecimal weightNet;

    @ExcelProperty("单位")
    private String weightUnit;

    @ExcelProperty("过磅类型")
    private String weighTypeName;

    @ExcelProperty("推送状态")
    private String pushStateName;

    @ExcelProperty("现场设备SN")
    private String deviceSn;

    @ExcelProperty("创建时间")
    private String createTime;

}
