package cn.pinming.microservice.material.client.management.common.form;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 终端称重数据标准格式
 */
@Data
public class WeighDataStandardForm {
    /**
     * 终端记录id
     */
    @NotBlank(message = "终端记录id不能为空")
    private String recordId;

    /**
     * 设备机器码
     */
    @NotBlank(message = "设备机器码不能为空")
    private String deviceSn;

    /**
     * 车牌号
     */
    private String truckNo;

    /**
     * 材料名称
     */
    private String material;

    /**
     * 重量
     */
    @NotNull(message = "终端重量不能为空")
    private BigDecimal weight;

    /**
     * 称重单位
     */
    @NotBlank(message = "终端记录id不能为空")
    private String unit;

    /**
     * 1 载车称重 2 净货称重
     */
    @NotNull(message = "终端类型不能为空")
    private Byte type;

    /**
     * 称重时间
     */
    @NotNull(message = "称重时间不能为空")
    private LocalDateTime weighTime;

    /**
     * 使用车牌识别事后checkout的车牌号
     */
    private String lprTruckNo;
}
