package cn.pinming.microservice.material.client.management.common.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class WeighPushDataDTO {
    /**
     * 终端记录id
     */
    private String recordId;

    /**
     * 用户id
     */
    private String uid;

    /**
     * 归属方名称
     */
    private String name;

    /**
     * 归属方code
     */
    private String code;

    /**
     * 设备机器码
     */
    private String deviceSn;

    /**
     * 车牌号
     */
    private String truckNo;

    /**
     * 材料名称
     */
    private String material;

    /**
     * 重量
     */
    private BigDecimal weight;

    /**
     * 称重单位
     */
    private String unit;

    /**
     * 1 载车称重 2 净货称重
     */
    private Byte type;

    /**
     * 称重时间
     */
    private LocalDateTime weighTime;
}
