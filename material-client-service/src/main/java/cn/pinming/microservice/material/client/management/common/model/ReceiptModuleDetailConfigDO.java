package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 单据回收模版明细业务表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("d_receipt_module_detail_config")
public class ReceiptModuleDetailConfigDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 模版id
     */
    private Long moduleId;

    /**
     * 模版明细id
     */
    private Long moduleDetailId;

    /**
     * 数据组顺序
     */
    private Integer groupOrder;

    /**
     * 数据键顺序
     */
    private Integer keyOrder;

    /**
     * 业务类型 1 单据匹配显示设置 2 单据回收必须键值
     */
    private Byte type;
}
