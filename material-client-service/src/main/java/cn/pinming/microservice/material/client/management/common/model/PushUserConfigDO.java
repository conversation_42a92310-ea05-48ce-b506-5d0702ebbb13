package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 租户推送配置
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("d_push_user_config")
public class PushUserConfigDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 应用id
     */
    private Long routeConfigId;

    /**
     * 回调网关
     */
    private String gateway;

    /**
     * 数据推送开关 1 关闭 2 开启
     */
    private Integer isOpen;

    /**
     * 排除的数据归属方id
     */
    private String excludeId;


}
