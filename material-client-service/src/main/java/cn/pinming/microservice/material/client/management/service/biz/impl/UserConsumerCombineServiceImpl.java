package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizErrorException;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizExceptionMessageEnum;
import cn.pinming.microservice.material.client.management.infrastructure.util.StpKit;
import cn.pinming.microservice.material.client.management.common.BaseDO;
import cn.pinming.microservice.material.client.management.common.mapper.UserConsumerCombineMapper;
import cn.pinming.microservice.material.client.management.common.mapper.ext.UserConsumerCombineExtMapper;
import cn.pinming.microservice.material.client.management.common.model.DeviceAttributionDO;
import cn.pinming.microservice.material.client.management.common.model.UserConsumerCombineDO;
import cn.pinming.microservice.material.client.management.common.vo.AttributionExtConfigVO;
import cn.pinming.microservice.material.client.management.common.vo.ConsumerVO;
import cn.pinming.microservice.material.client.management.service.biz.DeviceAttributionService;
import cn.pinming.microservice.material.client.management.service.biz.UserConsumerCombineService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class UserConsumerCombineServiceImpl extends ServiceImpl<UserConsumerCombineMapper, UserConsumerCombineDO> implements UserConsumerCombineService {
    @Resource
    private UserConsumerCombineExtMapper userConsumerCombineExtMapper;
    @Resource
    private DeviceAttributionService deviceAttributionService;

    @Override
    public List<ConsumerVO> getInfoByPhoneSn(String phoneSn) {
        List<ConsumerVO> list = userConsumerCombineExtMapper.getInfoByPhoneSn(phoneSn);

        if (CollUtil.isNotEmpty(list)) {
            List<ConsumerVO> listWithAttribution = list.stream().filter(e -> StrUtil.isNotBlank(e.getAttributions())).collect(Collectors.toList());

            if (CollUtil.isNotEmpty(listWithAttribution)) {
                Set<String> collect = listWithAttribution.stream().flatMap(e -> Arrays.stream(e.getAttributions().split(","))).collect(Collectors.toSet());
                List<DeviceAttributionDO> attributionDOList = deviceAttributionService.lambdaQuery()
                        .in(BaseDO::getId, collect)
                        .list();

                if (CollUtil.isNotEmpty(attributionDOList)) {
                    Map<Long, DeviceAttributionDO> deviceAttributionDOMap = attributionDOList.stream().collect(Collectors.toMap(BaseDO::getId, e -> e));
                    listWithAttribution.forEach(e -> {
                        List<AttributionExtConfigVO> configVOS = new ArrayList<>();
                        List<String> split = StrUtil.split(e.getAttributions(), ",");
                        split.forEach(item -> {
                            DeviceAttributionDO deviceAttributionDO = deviceAttributionDOMap.get(Long.valueOf(item));
                            if (ObjectUtil.isNotNull(deviceAttributionDO)) {
                                AttributionExtConfigVO attributionExtConfigVO = new AttributionExtConfigVO();
                                attributionExtConfigVO.setAttributionId(Long.valueOf(item));
                                attributionExtConfigVO.setName(deviceAttributionDO.getName());
                                attributionExtConfigVO.setCode(deviceAttributionDO.getCode());
                                configVOS.add(attributionExtConfigVO);
                            }
                        });
                        e.setList(configVOS);
                    });
                }
            }
        }

        return list;
    }

    @Override
    public String loginByPhoneSn(String phoneSn, String uId, Long attributionId) {
        UserConsumerCombineDO userConsumerCombineDO = userConsumerCombineExtMapper.loginByPhoneSn(phoneSn,uId,attributionId);
        if (ObjectUtil.isNotNull(userConsumerCombineDO)) {
            StpKit.CONSUMER.login(userConsumerCombineDO.getConsumeId());
            return StpKit.CONSUMER.getTokenValue();
        }else {
            throw new BizErrorException(BizExceptionMessageEnum.CONSUMER_LOGIN_ERROR);
        }
    }
}
