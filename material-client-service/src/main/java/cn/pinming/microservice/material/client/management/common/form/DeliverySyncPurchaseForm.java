package cn.pinming.microservice.material.client.management.common.form;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class DeliverySyncPurchaseForm {
    /**
     * 收货人姓名，可以为空，但建议设置
     */
    private String cargoReceiver;

    /**
     * 该发货单所属订单在基石平台的唯一编号，可能为空
     */
    private String jsBillOfParcelsNumber;

    /**
     * 订单拥有者的地址，一般指项目收货地址，可为空，但建议设置
     */
    private String orderAddress;

    /**
     * 订单拥有者的名称，一般指项目名称，不能为空
     */
    @NotBlank(message = "收货项目名称不能为空")
    private String orderName;

    /**
     * 业务系统中此订单的唯一ID，可能为空，但不能与js_bill_of_parcels_number同时为空
     */
    private String purchaseId;

    /**
     * 收货人电话，可以为空，但建议设置
     */
    private String receiverTelNumber;

    /**
     * 订单备注，可为空
     */
    private String remark;

    /**
     * 要货日期，不能为空
     */
    @NotBlank(message = "要货日期不能为空")
    private String requireDate;

    /**
     * 业务系统中订单对应供应商的唯一ID，不能为空
     */
    @NotBlank(message = "供应商ID不能为空")
    private String supplierId;

    /**
     * 供应商名称，不能为空
     */
    @NotBlank(message = "供应商名称不能为空")
    private String supplierName;
}
