package cn.pinming.microservice.material.client.management.controller.consumer;

import cn.hutool.core.util.ObjectUtil;
import cn.pinming.microservice.material.client.management.infrastructure.response.SingleResponse;
import cn.pinming.microservice.material.client.management.infrastructure.util.UserIdUtil;
import cn.pinming.microservice.material.client.management.common.BaseDO;
import cn.pinming.microservice.material.client.management.common.mapper.ext.UserConsumerCombineExtMapper;
import cn.pinming.microservice.material.client.management.common.model.UserConsumerCombineDO;
import cn.pinming.microservice.material.client.management.common.model.UserConsumerDO;
import cn.pinming.microservice.material.client.management.common.vo.CheckBindVO;
import cn.pinming.microservice.material.client.management.common.vo.UserConsumerVO;
import cn.pinming.microservice.material.client.management.service.biz.UserConsumerCombineService;
import cn.pinming.microservice.material.client.management.service.biz.UserConsumerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Api(value = "用户管理", tags = {"consumer"})
@RestController
@RequestMapping("/api/consumer")
public class UserConsumerController {
    @Resource
    private UserConsumerService userConsumerService;
    @Resource
    private UserIdUtil userIdUtil;
    @Resource
    private UserConsumerCombineService userConsumerCombineService;
    @Resource
    private UserConsumerCombineExtMapper userConsumerCombineExtMapper;


    @ApiOperation(value = "新增编辑", responseReference = "SingleResponse«?»", nickname = "consumerSaveOrUpdate")
    @PostMapping("/consumerSaveOrUpdate")
    public SingleResponse<?> consumerSaveOrUpdate(@RequestBody UserConsumerDO userConsumerDO) {
        if (ObjectUtil.isNull(userConsumerDO.getId())) {
            String uId = userIdUtil.getUId();
            userConsumerDO.setUid(uId);
        }
        userConsumerService.saveOrUpdate(userConsumerDO);
        return SingleResponse.of(SingleResponse.buildSuccess());
    }

    @ApiOperation(value = "启用禁用", responseReference = "SingleResponse«?»", nickname = "consumerIsEnable")
    @GetMapping("/consumerIsEnable/{id}/{isEnable}")
    public SingleResponse<?> consumerIsEnable(@PathVariable("id") Long id, @PathVariable("isEnable") Integer isEnable) {
        userConsumerCombineService.lambdaUpdate()
                .eq(UserConsumerCombineDO::getConsumeId, id)
                .set(UserConsumerCombineDO::getIsEnable, isEnable)
                .update();
        return SingleResponse.of(SingleResponse.buildSuccess());
    }

    @ApiOperation(value = "删除", responseReference = "SingleResponse«?»", nickname = "consumerRemove")
    @GetMapping("/consumerRemove/{id}")
    public SingleResponse<?> consumerRemove(@PathVariable("id") Long id) {
        userConsumerService.removeById(id);
        userConsumerCombineService.lambdaUpdate()
                .eq(UserConsumerCombineDO::getConsumeId, id)
                .set(BaseDO::getDeleted, 1)
                .update();
        return SingleResponse.of(SingleResponse.buildSuccess());
    }

    @ApiOperation(value = "是否需跳出提示", responseReference = "SingleResponse«CheckBindVO»", nickname = "checkBind")
    @GetMapping("/checkBind/{phoneSn}/{consumeId}")
    public CheckBindVO checkBind(@PathVariable("phoneSn") String phoneSn, @PathVariable("consumeId") Long consumeId) {
        CheckBindVO vo = new CheckBindVO();
        vo.setFlag(false);
        String uId = userIdUtil.getUId();
        UserConsumerCombineDO one = userConsumerCombineService.lambdaQuery()
                .eq(UserConsumerCombineDO::getUid, uId)
                .eq(UserConsumerCombineDO::getPhoneSn, phoneSn)
                .one();
        if (ObjectUtil.isNotNull(one)) {
            vo.setFlag(true);
        }
        UserConsumerCombineDO consumerCombineDO = userConsumerCombineService.lambdaQuery()
                .eq(UserConsumerCombineDO::getConsumeId, consumeId)
                .one();
        if (ObjectUtil.isNotNull(consumerCombineDO)) {
            vo.setFlag(true);
            vo.setPhoneSn(consumerCombineDO.getPhoneSn());
        }
        return vo;
    }


    @ApiOperation(value = "绑定设备码", responseReference = "SingleResponse«?»", nickname = "bindPhoneSn")
    @GetMapping("/bindPhoneSn/{phoneSn}/{consumeId}")
    public SingleResponse<?> bindPhoneSn(@PathVariable("phoneSn") String phoneSn, @PathVariable("consumeId") Long consumeId) {
        String uId = userIdUtil.getUId();

        // phoneSn被他人占用/自身占用
        userConsumerCombineService.lambdaUpdate()
                .eq(UserConsumerCombineDO::getUid, uId)
                .eq(UserConsumerCombineDO::getPhoneSn, phoneSn)
                .set(BaseDO::getDeleted, 1)
                .update();
        // 自身已有其他设备
        userConsumerCombineService.lambdaUpdate()
                .eq(UserConsumerCombineDO::getConsumeId, consumeId)
                .set(BaseDO::getDeleted, 1)
                .update();

        UserConsumerCombineDO userConsumerCombineDO = new UserConsumerCombineDO();
        userConsumerCombineDO.setConsumeId(consumeId);
        userConsumerCombineDO.setUid(uId);
        userConsumerCombineDO.setPhoneSn(phoneSn);
        userConsumerCombineService.save(userConsumerCombineDO);
        return SingleResponse.of(SingleResponse.buildSuccess());
    }

    @ApiOperation(value = "列表", responseReference = "SingleResponse«List<UserConsumerVO>»", nickname = "consumerList")
    @GetMapping("/consumerList")
    public SingleResponse<List<UserConsumerVO>> consumerList() {
        String uId = userIdUtil.getUId();
        List<UserConsumerVO> list = userConsumerService.consumerList(uId);
        return SingleResponse.of(list);
    }

    @ApiOperation(value = "用户绑定记录", responseReference = "SingleResponse«List<UserConsumerCombineDO>»", nickname = "bindHistoryList")
    @GetMapping("/bindHistoryList/{id}")
    public SingleResponse<List<UserConsumerCombineDO>> bindHistoryList(@PathVariable("id") Long id) {
        List<UserConsumerCombineDO> list = userConsumerCombineExtMapper.bindHistoryList(id);
        return SingleResponse.of(list);
    }

}
