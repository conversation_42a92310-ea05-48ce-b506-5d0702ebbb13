package cn.pinming.microservice.material.client.management.common.enums;

import lombok.Getter;

import java.util.Arrays;

@Getter
public enum VerifyEnum {
    NO(1, "否"),
    YES(2, "是"),
    ;

    private final Integer value;
    private final String desc;

    VerifyEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static VerifyEnum getByValue(Integer value) {
        return Arrays.stream(values()).filter(e -> e.getValue().equals(value)).findFirst().orElse(null);
    }

}
