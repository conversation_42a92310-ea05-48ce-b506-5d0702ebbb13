package cn.pinming.microservice.material.client.management.common.query;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/6/14
 */
@Data
public class UserQuery extends Page {
    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "uid")
    private String uid;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "租户名称")
    private String userName;

}
