package cn.pinming.microservice.material.client.management.service.biz;

import cn.pinming.microservice.material.client.management.common.form.client.ClientForm;
import cn.pinming.microservice.material.client.management.common.model.ClientDO;
import cn.pinming.microservice.material.client.management.common.vo.ClientVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2023/9/13 10:47
 */
public interface ClientService extends IService<ClientDO> {

    void upload(ClientForm form);

    String download(String fileId);

    IPage<ClientVO> clientPageList(Page query);

    ClientVO clientLatest(Byte type);
}
