package cn.pinming.microservice.material.client.management.common.form;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class DeliverySyncMaterialCulForm {
    /**
     * 扣水扣杂比例，取值范围为0-1，不能为空
     */
    @NotNull(message = "扣水扣杂比例不能为空")
    private BigDecimal deductRatio;

    /**
     * 转换系数，如2.334意思为1立方米 = 2.334吨，不能为空
     */
    @NotNull(message = "转换系数不能为空")
    private BigDecimal scaleFactor;

    /**
     * 用于设置该货物称重换算系数时，参照的重量单位：0 = 千克；1 = 吨，不能为空
     */
    @NotNull(message = "重量单位不能为空")
    private BigDecimal weightUnitForReference;
}
