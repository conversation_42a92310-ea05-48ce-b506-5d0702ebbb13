package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 称重数据修改日志
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("s_weigh_data_update_log")
public class WeighDataUpdateLogDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 称重数据ID
     */
    private Long weighDataId;

    /**
     * 修改项(记录车牌号：TRUCK_NO，系统识别车牌号：LPR_TRUCK_NO，风险等级：RISK_GRADE)
     */
    private String updateType;

    /**
     * 修改前的值
     */
    private String oldValue;

    /**
     * 修改后的值
     */
    private String newValue;

    /**
     * 操作账号
     */
    private String email;


}
