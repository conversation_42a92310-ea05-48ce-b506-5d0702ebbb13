package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <p>
 * 钢筋验收信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("d_check_info")
public class CheckInfoDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 验收id
     */
    private Long checkId;

    /**
     * 验收类型 1 仅称重验收, 2 称重+实点根数验收
     */
    private Integer checkType;

    /**
     * 混装实称重量
     */
    private BigDecimal actualWeight;

    /**
     * 称重单位
     */
    private String weightUnit;

    /**
     * 面单总重量
     */
    private BigDecimal sendWeight;

    /**
     * 偏差重量
     */
    private BigDecimal weightDif;

    /**
     * 偏差率
     */
    private BigDecimal weightRate;

    /**
     * 总重偏差校验-结果 1 合格, 2 不合格
     */
    private Integer weightCheckResult;

    /**
     * 反向复核总重使用类型(实点+称重)/数量确认使用类型(仅称重) 1 实称总重, 2 面单总重
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer reverseWeightType;

    /**
     * 混装实称重量-是否为手动输入 1 是 2 否
     */
    private Byte isInput;
}
