package cn.pinming.microservice.material.client.management.common.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class DeviceUserVO {
    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "设备id")
    private Long deviceId;

    @ApiModelProperty(value = "设备机器码")
    private String deviceSn;

    @ApiModelProperty(value = "设备类型(称重一体机-WEIGH、单据回收终端机-RECEIPT_RECYCLE)")
    private String deviceType;

    @ApiModelProperty(value = "归属方名称")
    private String name;

    @ApiModelProperty(value = "注册时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "是否接收 0 开始接收 1 暂停接收")
    private Byte receive;
}
