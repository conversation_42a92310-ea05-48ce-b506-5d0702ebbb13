package cn.pinming.microservice.material.client.management.common.vo.ocr;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2024/9/30 10:23
 */
@Data
public class ItemAreaVO {

    @ApiModelProperty(value = "类型")
    private Byte type;

    @ApiModelProperty(value = "名称")
    private String key;

    @ApiModelProperty(value = "值")
    private String value;

    @ApiModelProperty(value = "索引")
    private String idx;

    @ApiModelProperty(value = "组名")
    private String groupName;

}
