package cn.pinming.microservice.material.client.management.infrastructure.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import cn.pinming.microservice.material.client.management.common.BaseDO;
import cn.pinming.microservice.material.client.management.common.dto.push.*;
import cn.pinming.microservice.material.client.management.common.enums.DeveloperAppEnum;
import cn.pinming.microservice.material.client.management.common.enums.DeveloperTypeEnum;
import cn.pinming.microservice.material.client.management.common.model.*;
import cn.pinming.microservice.material.client.management.common.mapper.PurchaseOrderDetailMapper;
import cn.pinming.microservice.material.client.management.common.mapper.PurchaseOrderMapper;
import cn.pinming.microservice.material.client.management.service.biz.DeveloperService;
import cn.pinming.microservice.material.client.management.service.biz.IPurchaseOrderService;
import cn.pinming.microservice.material.client.management.service.biz.UserBusinessConfigService;
import cn.pinming.microservice.material.client.management.service.biz.UserService;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import cn.pinming.microservice.material.client.management.common.dto.push.PurchaseSyncDTO;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 订单转发定时任务
 */
@Component
@Slf4j
public class PurchaseSyncTask {
    @NacosValue(value = "${purchase.sync.enable:false}", autoRefreshed = true)
    private boolean isEnable;
    @Resource
    private UserBusinessConfigService userBusinessConfigService;
    @Resource
    private IPurchaseOrderService purchaseOrderService;
    @Resource
    private PurchaseOrderMapper purchaseOrderMapper;
    @Resource
    private PurchaseOrderDetailMapper purchaseOrderDetailMapper;
    @Resource
    private UserService userService;
    @Resource
    private DeveloperService developerService;

    @Scheduled(cron = "0 0/5 * * * ?")
    public void purchaseSyncTask() {
        if (!isEnable) {
            return;
        }
        log.info("订单转发开始...");

        // 查询配置
        List<UserBusinessConfigDO> configDOList = userBusinessConfigService.lambdaQuery()
                .eq(UserBusinessConfigDO::getPushType, 4)
                .eq(UserBusinessConfigDO::getWeighDataPushStatus, 2)
                .isNotNull(UserBusinessConfigDO::getWeighDataPushUrl)
                .list();
        if (CollUtil.isEmpty(configDOList)) {
            log.info("无订单转发配置...");
            return;
        }

        List<String> includeUidList = configDOList.stream().map(UserBusinessConfigDO::getUid).distinct().collect(Collectors.toList());
        List<DeveloperDO> developerDOList = developerService.lambdaQuery()
                .in(BaseDO::getCreateId, includeUidList)
                .eq(DeveloperDO::getAppId, DeveloperAppEnum.PURCHASE_SYNC.value())
                .eq(DeveloperDO::getType, DeveloperTypeEnum.START.value())
                .list();
        if (CollUtil.isEmpty(developerDOList)) {
            log.info("无租户开启订单接收服务");
            return;
        }
        List<String> collect = developerDOList.stream().map(BaseDO::getCreateId).distinct().collect(Collectors.toList());
        configDOList = configDOList.stream().filter(e -> collect.contains(e.getUid())).collect(Collectors.toList());

        // 查询数据
        List<PurchaseSyncDTO> cup = new ArrayList<>();
        for (int i = 0; i < configDOList.size(); i++) {
            UserBusinessConfigDO configDO = configDOList.get(i);
            List<PurchaseSyncDTO> purchaseList = purchaseOrderMapper.selectPurchaseSync(configDO.getUid(), StrUtil.split(configDO.getWeighDataPushAttribution(), ","));
            if (CollUtil.isEmpty(purchaseList)) {
                continue;
            }

            List<String> uidIdList = purchaseList.stream().map(PurchaseOrderDO::getSupplierId).distinct().collect(Collectors.toList());
            List<Long> purchaseIdList = purchaseList.stream().map(PurchaseSyncDTO::getId).collect(Collectors.toList());
            List<PurchaseDetailSyncDTO> purchaseDetailSyncDTOList = purchaseOrderDetailMapper.selectPurchaseDetailSync(purchaseIdList);
            Map<Long, List<PurchaseDetailSyncDTO>> purchaseDetailMap = purchaseDetailSyncDTOList.stream().collect(Collectors.groupingBy(PurchaseOrderDetailDO::getOrderId));
            List<UserDO> uidList = userService.lambdaQuery()
                    .in(UserDO::getUid, uidIdList)
                    .list();
            Map<String, String> uidMap = uidList.stream().collect(Collectors.toMap(UserDO::getUid, UserDO::getUserName));
            purchaseList.forEach(e -> {
                e.setList(purchaseDetailMap.get(e.getId()));
                e.setUserName(uidMap.get(e.getCreateId()));
            });

            cup.addAll(purchaseList);
        }
        if (CollUtil.isEmpty(cup)) {
            log.info("无运单可推送...");
            return;
        }

        // 组装数据
        List<PushPurchaseDTO> result = new ArrayList<>();
        cup.forEach(e -> {
            PushPurchaseDTO pushPurchaseDTO = new PushPurchaseDTO();
            PushPurchaseJSInfoDTO pushPurchaseJSInfoDTO = new PushPurchaseJSInfoDTO();
            PushPurchaseDataDTO pushPurchaseDataDTO = new PushPurchaseDataDTO();

            // 订单源头基石账号信息
            pushPurchaseJSInfoDTO.setJsPurchaseId(String.valueOf(e.getId()));
            pushPurchaseJSInfoDTO.setJsTenantId(e.getCreateId());
            pushPurchaseJSInfoDTO.setJsAttributionCode(e.getPrimaryCode());
            // 订单信息
            pushPurchaseDataDTO.setCargoReceiver(e.getReceiver());
            pushPurchaseDataDTO.setOrderAddress(e.getAddress());
            pushPurchaseDataDTO.setOrderName(e.getProject());
            pushPurchaseDataDTO.setPurchaseId(e.getOrderExtId());
            pushPurchaseDataDTO.setReceiverTelNumber(e.getMobile());
            pushPurchaseDataDTO.setRemark(e.getRemark());
            pushPurchaseDataDTO.setRequireDate(String.valueOf(e.getReceiveDate()));
            pushPurchaseDataDTO.setSupplierId(e.getSupplierId());
            pushPurchaseDataDTO.setSupplierName(e.getUserName());
            List<PurchaseDetailSyncDTO> detailList = e.getList();
            // 订单明细信息
            List<PushPurchaseMaterialDTO> cargoList = detailList.stream().map(detail -> {
                PushPurchaseMaterialDTO materialDTO = new PushPurchaseMaterialDTO();
                PushPurchaseCalDTO calDTO = new PushPurchaseCalDTO();
                PushPurchaseMaterialJSDTO jsDTO = new PushPurchaseMaterialJSDTO();

                materialDTO.setCargoBrand(detail.getBrand());
                materialDTO.setCargoId(detail.getExtId());
                materialDTO.setCargoModel(detail.getSpec());
                materialDTO.setCargoName(detail.getName());
                materialDTO.setCargoParameter(detail.getArgument());
                materialDTO.setCargoUsage(detail.getPosition());
                materialDTO.setCargoUsageId(detail.getWbsId());
                materialDTO.setRemark(detail.getRemark());
                materialDTO.setWaybillCounts(detail.getAmount());
                materialDTO.setWaybillUnit(detail.getUnit());
                calDTO.setDeductRatio(detail.getDeductRatio());
                calDTO.setScaleFactor(detail.getScaleFactor());
                calDTO.setWeightUnitForReference(detail.getUnitType());
                jsDTO.setJsCargoId(String.valueOf(detail.getId()));
                materialDTO.setCalParameters(calDTO);
                materialDTO.setJsInfo(jsDTO);

                return materialDTO;
            }).collect(Collectors.toList());
            pushPurchaseDataDTO.setCargoList(cargoList);

            pushPurchaseDTO.setJsInfo(pushPurchaseJSInfoDTO);
            pushPurchaseDTO.setPurchaseData(pushPurchaseDataDTO);

            result.add(pushPurchaseDTO);
        });

        // 推送数据
        Map<String, List<PushPurchaseDTO>> resultMap = result.stream().collect(Collectors.groupingBy(e -> e.getJsInfo().getJsTenantId()));
        configDOList.forEach(e -> {
            List<PushPurchaseDTO> pushList = resultMap.get(e.getUid());
            if (CollUtil.isNotEmpty(pushList)) {
                try {
                    HttpUtil.post(StrUtil.format("{}{}", e.getEndpoint(), e.getWeighDataPushUrl()), JSONUtil.toJsonStr(pushList));
                } catch (Exception ex) {
                    log.info("uid:{}基石账户下的订单转发失败",e.getUid());
                }
            }
        });

        log.info("订单转发结束...");
    }
}
