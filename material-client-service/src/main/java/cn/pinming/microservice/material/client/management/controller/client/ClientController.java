package cn.pinming.microservice.material.client.management.controller.client;

import cn.pinming.microservice.material.client.management.common.enums.ClientTypeEnum;
import cn.pinming.microservice.material.client.management.infrastructure.response.SingleResponse;
import cn.pinming.microservice.material.client.management.common.form.client.ClientForm;
import cn.pinming.microservice.material.client.management.common.vo.ClientVO;
import cn.pinming.microservice.material.client.management.service.biz.ClientService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Api(value = "client", tags = {"client"})
@RestController
@RequestMapping("/api/client")
public class ClientController {

    @Resource
    private ClientService clientService;

    @ApiOperation(value = "客户端文件类型", responseReference = "SingleResponse«?»", nickname = "clientFileType")
    @GetMapping("/type")
    public SingleResponse<?> type() {
        return SingleResponse.of(ClientTypeEnum.KEY_MAP);
    }

    @ApiOperation(value = "客户端上传", responseReference = "SingleResponse«?»", nickname = "clientUpload")
    @PostMapping("/upload")
    public SingleResponse<?> upload(@Validated @RequestBody ClientForm form) {
        clientService.upload(form);
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "客户端下载", responseReference = "SingleResponse«?»", nickname = "clientDownload")
    @GetMapping("/download")
    public SingleResponse<?> download(@RequestParam String fileId) {
        String url = clientService.download(fileId);
        return SingleResponse.of(url);
    }

    @ApiOperation(value = "客户端列表", responseReference = "SingleResponse«IPage<ClientVO>»", nickname = "clientPage")
    @PostMapping("/page")
    public SingleResponse<?> page(@RequestBody Page query) {
        IPage<ClientVO> page = clientService.clientPageList(query);
        return SingleResponse.of(page);
    }

    @ApiOperation(value = "客户端删除", responseReference = "SingleResponse«?»", nickname = "clientDel")
    @DeleteMapping("/{id}")
    @Transactional(rollbackFor = Exception.class)
    public SingleResponse<?> delete(@PathVariable Long id) {
        clientService.removeById(id);
        return SingleResponse.buildSuccess();
    }


}