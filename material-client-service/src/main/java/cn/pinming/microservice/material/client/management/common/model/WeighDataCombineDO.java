package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 称重数据使用表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("d_weigh_data_combine")
public class WeighDataCombineDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 数据id
     */
    private String weighDataId;

    /**
     * 归属方id
     */
    private Long attributionId;

    /**
     * appKey
     */
    private String appKey;


}
