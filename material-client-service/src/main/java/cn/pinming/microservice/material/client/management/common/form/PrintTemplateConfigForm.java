package cn.pinming.microservice.material.client.management.common.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class PrintTemplateConfigForm {

    @ApiModelProperty(value = "模板ID")
    @NotNull(message = "模板ID为空")
    private Long id;

    @ApiModelProperty(value = "打印次数限制")
    @NotNull(message = "打印次数限制为空")
    @Max(value = 6, message = "打印次数限制不能超过6")
    @Min(value = 0, message = "打印次数限制不能小于0")
    private Integer printLimit;

    @ApiModelProperty(value = "设备Id列表")
    @NotNull(message = "设备Id列表为空")
//    @Size(min = 1, message = "设备Id列表不能为空")
    private List<Long> deviceIdList;

}
