package cn.pinming.microservice.material.client.management.common.mapper;

import cn.pinming.microservice.material.client.management.common.model.OcrDictDO;
import cn.pinming.microservice.material.client.management.common.query.OCRDictQuery;
import cn.pinming.microservice.material.client.management.common.vo.OCRDictVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * <p>
 * OCR模版字典表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-03
 */
public interface OcrDictMapper extends BaseMapper<OcrDictDO> {

    Page<OCRDictVO> pageByQuery(OCRDictQuery query);
}
