package cn.pinming.microservice.material.client.management.common.form;

import lombok.Data;


@Data
public class AssembleForm {

    private String uid;

    /**
     * 第一条称重数据称重id
     */
    private String first;
    /**
     * 第二条称重数据称重id
     */
    private String second;
    /**
     * 归属方code
     */
    private String attributionCode;

    /**
     * 车牌号
     */
    private String truckNo;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

}
