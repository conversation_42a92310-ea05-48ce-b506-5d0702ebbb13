package cn.pinming.microservice.material.client.management.common.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Getter
public enum PushStatusEnum {
    NONPUSH((byte) 1, "未推送"),
    WAIT((byte) 2, "队列中"),
    PUSH((byte) 3, "已推送"),
    UNCONFIRMED((byte) 4, "待确认"), //已推送待确认接收
    ;

    private final Byte val;

    private final String desc;

    public static final Map<Byte, String> KEY_MAP = Arrays.stream(values()).collect(Collectors.toMap(PushStatusEnum::getVal, PushStatusEnum::getDesc));

    public static final Map<String, Byte> VAL_MAP = Arrays.stream(values()).collect(Collectors.toMap(PushStatusEnum::getDesc, PushStatusEnum::getVal));

    PushStatusEnum(Byte val,String desc) {
        this.val = val;
        this.desc = desc;
    }


}
