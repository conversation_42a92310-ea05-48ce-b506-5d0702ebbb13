package cn.pinming.microservice.material.client.management.common.query;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class ReceiptRecycleResultQuery extends Page {

    @ApiModelProperty("模版id")
    private Long moduleId;

    @ApiModelProperty("uid")
    private String uid;

    @ApiModelProperty("车牌号")
    private String truckNo;

    @ApiModelProperty("归属方名称模糊查询")
    private String attributionName;

    @ApiModelProperty("归属方code")
    private String attributionCode;

    @ApiModelProperty("过磅类型(1-收料、2-发料)")
    private List<Integer> weighType;

    @ApiModelProperty("推送状态 1 未推送 2 队列中 3 已推送")
    private List<Byte> pushStatus;

}
