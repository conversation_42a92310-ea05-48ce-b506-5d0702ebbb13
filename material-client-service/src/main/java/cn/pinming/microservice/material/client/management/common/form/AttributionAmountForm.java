package cn.pinming.microservice.material.client.management.common.form;

import cn.pinming.microservice.material.client.management.common.model.DeviceAttributionDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class AttributionAmountForm {
    @ApiModelProperty(value = "用户id")
    private String uid;

    @ApiModelProperty(value = "数据归属方数量 ")
    private Integer attributionAmount;

    @ApiModelProperty(value = "数据归属方列表")
    private List<DeviceAttributionDO> list;

    @ApiModelProperty(value = "是否无上限 1 否 2 是")
    private Byte isLimit;
}
