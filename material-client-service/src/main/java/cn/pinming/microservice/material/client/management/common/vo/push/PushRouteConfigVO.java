package cn.pinming.microservice.material.client.management.common.vo.push;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2024/9/12 14:58
 */
@Data
public class PushRouteConfigVO {

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "请求方式")
    private String method;

    @ApiModelProperty(value = "网关地址")
    private String gateway;

    @ApiModelProperty(value = "接口地址")
    private String endPoint;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "数据推送开关 1 关闭 2 开启")
    private Integer isOpen;

    @ApiModelProperty(value = "排除的数据归属方id")
    private String excludeId;

    @ApiModelProperty(value = "uid")
    private String uid;

    @ApiModelProperty(value = "排除的数据归属方id列表")
    private List<Long> attributionIdList;

}
