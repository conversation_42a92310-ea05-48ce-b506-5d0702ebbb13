package cn.pinming.microservice.material.client.management.common.form;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class RebarCheckForm {
    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "归属方id")
    @NotNull(message = "请选择归属方")
    private Long attributionId;

    @ApiModelProperty(value = "设备sn")
    @NotBlank(message = "请选择设备sn")
    private String phoneSn;

    @ApiModelProperty(value = "车牌号码")
    private String truckNo;

    @ApiModelProperty(value = "车辆到场时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime truckTime;

    @ApiModelProperty(value = "车辆称重图片")
    private String truckPic;

    @ApiModelProperty(value = "货/铭牌图片")
    private String goodsPic;

    @ApiModelProperty(value = "送货单图片")
    private String sendPic;

    @ApiModelProperty(value = "外部代码")
    private String extNo;

    @ApiModelProperty(value = "收料地址详情")
    private String location;

    @ApiModelProperty(value = "是否归档 1 否, 2 是")
    private Integer isVerify;

    @ApiModelProperty(value = "验收结果 1 合格进场, 2 不合格进场")
    private Integer checkResult;

    @ApiModelProperty(value = "验收类型 1 仅称重验收, 2 称重+实点根数验收")
    private Integer checkType;

    @ApiModelProperty(value = "混装实称重量")
    private BigDecimal actualWeight;

    @ApiModelProperty(value = "称重单位")
    private String weightUnit;

    @ApiModelProperty(value = "验收意见")
    private String checkRemark;

    @ApiModelProperty(value = "混装实称重量-是否为手动输入 1 是 2 否")
    private Byte isInput;

    @ApiModelProperty(value = "收料明细")
    private List<RebarCheckDetailForm> list;
}
