package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.microservice.material.client.management.common.BaseDO;
import cn.pinming.microservice.material.client.management.common.mapper.PushConfigurationMapper;
import cn.pinming.microservice.material.client.management.common.model.DeviceAttributionDO;
import cn.pinming.microservice.material.client.management.common.model.PushConfigurationDO;
import cn.pinming.microservice.material.client.management.common.model.UserDO;
import cn.pinming.microservice.material.client.management.common.vo.PushConfigurationVO;
import cn.pinming.microservice.material.client.management.service.biz.DeviceAttributionService;
import cn.pinming.microservice.material.client.management.service.biz.PushConfigurationService;
import cn.pinming.microservice.material.client.management.service.biz.UserService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class PushConfigurationServiceImpl extends ServiceImpl<PushConfigurationMapper, PushConfigurationDO> implements PushConfigurationService {
    @Resource
    private DeviceAttributionService deviceAttributionService;
    @Resource
    private UserService userService;

    @Override
    public void config(Long id,String users) {
        PushConfigurationDO one = this.lambdaQuery()
                .eq(BaseDO::getId, id)
                .one();
        one.setUserIds(users);

        if (StrUtil.isNotBlank(users)) {
            List<String> uidList = StrUtil.split(users, ",").stream().distinct().collect(Collectors.toList());
            List<DeviceAttributionDO> attributionDOList = deviceAttributionService.lambdaQuery()
                    .in(DeviceAttributionDO::getUid, uidList)
                    .list();
            List<UserDO> userList = userService.lambdaQuery()
                    .in(UserDO::getUid, uidList)
                    .list();
            List<String> userNames = userList.stream().map(UserDO::getUserName).collect(Collectors.toList());
            one.setClientName(String.join(",", userNames));
            List<String> attributionIdList = attributionDOList.stream().map(e -> String.valueOf(e.getId())).collect(Collectors.toList());
            if (StrUtil.isNotBlank(one.getAttributionIds())) {
                // 去除被删除租户下的归属方
                String newAttributionIdList = StrUtil.split(one.getAttributionIds(), ",").stream().filter(attributionIdList::contains).collect(Collectors.joining(","));
                one.setAttributionIds(newAttributionIdList);
            }
        }else {
            one.setAttributionIds(null);
            one.setClientName(null);
        }

        this.updateById(one);
    }

    @Override
    public List<PushConfigurationVO> pushConfigurationShow() {
        List<PushConfigurationDO> list = this.lambdaQuery()
                .eq(BaseDO::getDeleted, 0)
                .list();
        List<PushConfigurationVO> result = new ArrayList<>();

        if (CollUtil.isNotEmpty(list)) {
            result = list.stream().map(e -> {
                PushConfigurationVO vo = new PushConfigurationVO();
                BeanUtils.copyProperties(e, vo);
                if (StrUtil.isNotBlank(e.getAttributionIds())) {
                    vo.setAttributionAmount(StrUtil.split(e.getAttributionIds(),",").size());
                }
                if (StrUtil.isNotBlank(e.getUserIds())) {
                    vo.setUserAmount(StrUtil.split(e.getUserIds(),",").size());
                }
                return vo;
            }).collect(Collectors.toList());
        }

        return result;
    }
}
