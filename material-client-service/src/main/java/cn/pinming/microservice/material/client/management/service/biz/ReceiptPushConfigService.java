package cn.pinming.microservice.material.client.management.service.biz;

import cn.pinming.microservice.material.client.management.common.form.ReceiptPushConfigForm;
import cn.pinming.microservice.material.client.management.common.model.ReceiptPushConfigDO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 单据回收推送配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-27
 */
public interface ReceiptPushConfigService extends IService<ReceiptPushConfigDO> {

    void saveReceiptPushConfig(ReceiptPushConfigForm form);

    ReceiptPushConfigDO getByUid(String uid);
}
