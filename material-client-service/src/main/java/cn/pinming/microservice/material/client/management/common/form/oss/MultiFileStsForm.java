package cn.pinming.microservice.material.client.management.common.form.oss;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2023/6/16 14:50
 */
@Data
public class MultiFileStsForm {

    @NotBlank(message = "客户端sn为空")
    private String sn;

    @Valid
    @Size(min = 1, message = "请添加需要上传的文件")
    List<FileStsForm> stslist;

}
