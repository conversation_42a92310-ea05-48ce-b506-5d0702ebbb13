package cn.pinming.microservice.material.client.management.controller.plate;

import cn.pinming.microservice.material.client.management.infrastructure.response.SingleResponse;
import cn.pinming.microservice.material.client.management.service.biz.PlateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 车牌.
 *
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2023/10/26 14:16
 */
@Api(value = "车牌服务",tags = {"plateService"})
@RestController
@RequestMapping("/api/plate")
@Slf4j
public class PlateController {

    @Resource
    private PlateService plateService;

    @ApiOperation(value = "车牌识别列表", responseReference = "SingleResponse«List<String>»", nickname = "plateList")
    @PostMapping("/identify/{recordId}")
    public SingleResponse<List<String>> identify(@PathVariable String recordId) {
        List<String> result = plateService.identify(recordId);
        return SingleResponse.of(result);
    }

}
