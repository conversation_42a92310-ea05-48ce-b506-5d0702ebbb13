package cn.pinming.microservice.material.client.management.service.biz;

import cn.pinming.microservice.material.client.management.common.form.DeviceAttributionForm;
import cn.pinming.microservice.material.client.management.common.model.DeviceAttributionDO;
import cn.pinming.microservice.material.client.management.common.vo.AttributionDeviceVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface DeviceAttributionService extends IService<DeviceAttributionDO> {
    void add(DeviceAttributionForm form);

    void delete(Long id);

    List<AttributionDeviceVO> configList(String type, Long attributionId);
}
