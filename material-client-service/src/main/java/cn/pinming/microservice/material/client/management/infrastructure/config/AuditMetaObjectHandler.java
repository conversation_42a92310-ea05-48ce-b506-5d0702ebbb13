package cn.pinming.microservice.material.client.management.infrastructure.config;

import cn.hutool.core.util.ObjectUtil;
import cn.pinming.microservice.material.client.management.infrastructure.util.StpKit;
import cn.pinming.microservice.material.client.management.infrastructure.util.UserIdUtil;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;

@Slf4j
@Component
public class AuditMetaObjectHandler implements MetaObjectHandler {

    @Resource
    private UserIdUtil userIdUtil;

    private static final String CREATE_TIME = "gmtCreate";
    private static final String UPDATE_TIME = "gmtModify";
    private static final String CREATE_ID = "createId";
    private static final String UPDATE_ID = "modifyId";
    private static final String SYSTEM_USER = "system";

    @Override
    public void insertFill(MetaObject metaObject) {
        LocalDateTime localDateNow = LocalDateTime.now();
        Object createTime = this.getFieldValByName(CREATE_TIME, metaObject);
        if (metaObject.hasGetter(CREATE_TIME) && ObjectUtil.isNull(createTime)) {
            this.setFieldValByName(CREATE_TIME, localDateNow, metaObject);
        }
        Object updateTime = this.getFieldValByName(UPDATE_TIME, metaObject);
        if (metaObject.hasGetter(UPDATE_TIME) && ObjectUtil.isNull(updateTime)) {
            this.setFieldValByName(UPDATE_TIME, localDateNow, metaObject);
        }

        Object createId = this.getFieldValByName(CREATE_ID, metaObject);
        if (metaObject.hasGetter(CREATE_ID) && ObjectUtil.isNull(createId)) {
            String loginId;
            try {
                loginId = StpKit.DEFAULT.isLogin() || StpKit.MANAGER.isLogin() ? userIdUtil.getUId() : SYSTEM_USER;
            } catch (Exception e) {
                loginId = SYSTEM_USER;
            }
            this.setFieldValByName(CREATE_ID, loginId, metaObject);
        }
        Object updateId = this.getFieldValByName(UPDATE_ID, metaObject);
        if (metaObject.hasGetter(UPDATE_ID) && ObjectUtil.isNull(updateId)) {
            String loginId;
            try {
                loginId = StpKit.DEFAULT.isLogin() || StpKit.MANAGER.isLogin() ? userIdUtil.getUId() : SYSTEM_USER;
            } catch (Exception e) {
                loginId = SYSTEM_USER;
            }
            this.setFieldValByName(UPDATE_ID, loginId, metaObject);
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        LocalDateTime localDateNow = LocalDateTime.now();
        Object updateTime = this.getFieldValByName(UPDATE_TIME, metaObject);
        if (metaObject.hasGetter(UPDATE_TIME) && ObjectUtil.isNull(updateTime)) {
            this.setFieldValByName(UPDATE_TIME, localDateNow, metaObject);
        }

        Object updateId = this.getFieldValByName(UPDATE_ID, metaObject);
        if (metaObject.hasGetter(UPDATE_ID) && ObjectUtil.isNull(updateId)) {
            String loginId;
            try {
                loginId = StpKit.DEFAULT.isLogin() || StpKit.MANAGER.isLogin() ? userIdUtil.getUId() : SYSTEM_USER;
            } catch (Exception e) {
                loginId = SYSTEM_USER;
            }
            this.setFieldValByName(UPDATE_ID, loginId, metaObject);
        }
    }

}

