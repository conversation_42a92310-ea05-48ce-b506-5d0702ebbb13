package cn.pinming.microservice.material.client.management.common.mapper.ext;

import cn.pinming.microservice.material.client.management.common.dto.WeighPushPicDTO;
import cn.pinming.microservice.material.client.management.common.dto.WeighSimpleDTO;
import cn.pinming.microservice.material.client.management.common.dto.push.PushPicDTO;
import cn.pinming.microservice.material.client.management.common.model.WeighDataPicDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

public interface WeighDataPicExtMapper {


    long selectTodayUsedSpace(String uid);

    WeighPushPicDTO needPushWeighPic(@Param("id") Long id);

    List<WeighSimpleDTO> needPushWeighPicS(@Param("uid") String uid, @Param("attributionIdList") List<Long> attributionIdList);

    List<PushPicDTO> getWeighPics(@Param("recordIds") List<String> recordIds);

    /**
     * 查询需要推送的称重照片数据
     *
     * @param uid               用户ID
     * @param attributionIdList 归属方ID列表
     * @return 待推送的称重照片列表
     */
    List<WeighDataPicDO> selectPicToPush(@Param("uid") String uid, @Param("attributionIdList") Set<Long> attributionIdList);

    /**
     * 根据recordId列表查询所有相关图片（不限制推送状态）
     *
     * @param recordIds recordId列表
     * @return 图片列表
     */
    List<WeighDataPicDO> selectPicsByRecordIds(@Param("recordIds") Set<String> recordIds);
}
