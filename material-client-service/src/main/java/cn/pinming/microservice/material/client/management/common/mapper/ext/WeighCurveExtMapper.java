package cn.pinming.microservice.material.client.management.common.mapper.ext;

import cn.pinming.microservice.material.client.management.common.dto.push.PushCurveDataDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 称重曲线扩展Mapper
 *
 * <AUTHOR>
 */
public interface WeighCurveExtMapper {

    /**
     * 查询需要推送的称重曲线数据
     *
     * @param uid 用户ID
     * @param attributionList 归属方ID列表
     * @return 需要推送的称重曲线数据列表
     */
    List<PushCurveDataDTO> selectCurveToPush(@Param("uid") String uid, @Param("attributionList") Set<Long> attributionList);
}
