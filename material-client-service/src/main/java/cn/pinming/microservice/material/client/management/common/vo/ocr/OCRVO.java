package cn.pinming.microservice.material.client.management.common.vo.ocr;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class OCRVO implements Serializable {
    @ApiModelProperty(value = "id")
    private String id;
    @ApiModelProperty(value = "第三方id")
    private String id_other;
    @ApiModelProperty(value = "主锚")
    private AreaVO anchor_area;
    @ApiModelProperty(value = "副锚")
    private AreaVO base_area;
    @ApiModelProperty(value = "业务结构")
    private Map<String, Map<String,TextVO>> targets;
}
