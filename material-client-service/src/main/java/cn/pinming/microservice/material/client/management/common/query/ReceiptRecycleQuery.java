package cn.pinming.microservice.material.client.management.common.query;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class ReceiptRecycleQuery extends Page {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("uid")
    private String uid;

    @ApiModelProperty("回收来源(1-单据回收终端、2-高拍仪、3-外部推送)")
    private List<Integer> recycleSource;

    @ApiModelProperty("状态(1-待回收、2-回收成功、3-回收失败)")
    private List<Integer> recycleStatus;

    @ApiModelProperty("回收时间start")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate recycleTimeStart;

    @ApiModelProperty("回收时间end")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate recycleTimeEnd;

    @ApiModelProperty("设备机器码")
    private String deviceSn;

    @ApiModelProperty("归属方名称模糊查询")
    private String attributionName;

    @ApiModelProperty("归属方code")
    private String attributionCode;

    @ApiModelProperty("创建时间start")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreateStart;

    @ApiModelProperty("创建时间end")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreateEnd;

    @ApiModelProperty("回收失败类型数组")
    private List<Integer> receiptFailTypes;

    @ApiModelProperty("批次id")
    private Long batchId;
}
