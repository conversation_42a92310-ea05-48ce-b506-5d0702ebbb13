package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 短链接服务表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("s_short_link")
public class ShortLinkDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 短链接
     */
    private String shortLink;

    /**
     * 长链接
     */
    private String longLink;

    /**
     * 过期时间
     */
    private LocalDateTime expireTime;


}
