package cn.pinming.microservice.material.client.management.controller.weighDataConfirm;


import cn.pinming.microservice.material.client.management.infrastructure.response.SingleResponse;
import cn.pinming.microservice.material.client.management.infrastructure.util.UserIdUtil;
import cn.pinming.microservice.material.client.management.common.query.WeighDataPageQuery;
import cn.pinming.microservice.material.client.management.common.vo.WeighDataConfirmDetailDTO;
import cn.pinming.microservice.material.client.management.common.vo.WeighDataConfirmVO;
import cn.pinming.microservice.material.client.management.service.biz.IWeighDataConfirmService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 现场称重确认单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-22
 */
@RestController
@RequestMapping("/api/weighData/confirm")
public class WeighDataConfirmController {
    @Resource
    private IWeighDataConfirmService weighDataConfirmService;
    @Resource
    private UserIdUtil userIdUtil;

    @ApiOperation(value = "确认单列表")
    @PostMapping("/list")
    public SingleResponse<IPage<WeighDataConfirmVO>> list(@RequestBody WeighDataPageQuery query) {
        String uId = userIdUtil.getUId();
        query.setUid(uId);
        IPage<WeighDataConfirmVO> page = weighDataConfirmService.col(query);
        return SingleResponse.of(page);
    }

    @ApiOperation(value = "确认单详情")
    @GetMapping("/detail/{id}")
    public SingleResponse<WeighDataConfirmDetailDTO> detail(@PathVariable("id")Long id) {
        WeighDataConfirmDetailDTO vo = weighDataConfirmService.detail(null,id);
        return SingleResponse.of(vo);
    }
}

