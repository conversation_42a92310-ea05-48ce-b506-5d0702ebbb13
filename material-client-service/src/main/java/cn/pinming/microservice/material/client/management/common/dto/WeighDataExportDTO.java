package cn.pinming.microservice.material.client.management.common.dto;

import cn.pinming.microservice.material.client.management.infrastructure.util.excel.PushStatusConverter;
import cn.pinming.microservice.material.client.management.infrastructure.util.excel.RiskGradeConverter;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2024/11/15 10:00
 */
@Data
public class WeighDataExportDTO {

    @ExcelProperty("终端记录ID")
    private String recordId;

    @ExcelProperty("车牌号")
    private String truckNo;

    @ExcelProperty("系统识别车牌号")
    private String lprTruckNo;

    @ExcelProperty("重量")
    private BigDecimal weight;

    @ExcelProperty("单位")
    private String unit;

    @ExcelProperty("称重日期")
    private String weighDate;

    @ExcelProperty("称重时间")
    private String weighTime;

    @ExcelProperty("称重类型")
    private String typeStr;

    @ExcelProperty(value = "风险等级" , converter = RiskGradeConverter.class)
    private String riskGrade;

    @ExcelProperty("设备机器码")
    private String deviceSn;

    @ExcelProperty("上传时间")
    private String gmtCreate;

    @ExcelProperty("数据所属方")
    private String name;

    @ExcelProperty("数据所属方code")
    private String code;

    @ExcelProperty(value = "推送状态", converter = PushStatusConverter.class)
    private Byte pushStatus;

    @ExcelProperty("是否被组装")
    private String isUsed;
}
