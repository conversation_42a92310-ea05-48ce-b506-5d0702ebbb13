package cn.pinming.microservice.material.client.management.infrastructure.strategy;

import cn.hutool.core.collection.CollUtil;
import cn.pinming.microservice.material.client.management.common.dto.push.PushCurveDTO;
import cn.pinming.microservice.material.client.management.common.dto.push.PushCurveDataDTO;
import cn.pinming.microservice.material.client.management.common.enums.SdkPushTypeEnum;
import cn.pinming.microservice.material.client.management.common.mapper.ext.WeighCurveExtMapper;
import cn.pinming.microservice.material.client.management.common.model.WeighCurveDO;
import cn.pinming.microservice.material.client.management.common.vo.WeighCurveVO;
import cn.pinming.microservice.material.client.management.common.vo.push.PushRouteConfigVO;
import cn.pinming.microservice.material.client.management.service.biz.IWeighCurveService;
import cn.pinming.microservice.material.client.management.service.push.sdk.dto.SdkRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 称重曲线推送策略
 *
 * <AUTHOR>
 */
@Slf4j
@Component("CURVE")
public class CurveSdkPushStrategy extends AbstractSkdPushStrategy<PushCurveDTO> {

    private static final DateTimeFormatter ISO_DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSSSSSSS");

    @Resource
    private IWeighCurveService  weighCurveService;
    @Resource
    private WeighCurveExtMapper weighCurveExtMapper;

    @Override
    Long getRouteConfigId() {
        return SdkPushTypeEnum.CURVE.getVal();
    }

    @Override
    Map<String, List<PushCurveDTO>> queryDataListMap(PushRouteConfigVO config, Map<Long, String> attributionMap) {
        Map<String, List<PushCurveDTO>> result = new HashMap<>();

        if (CollUtil.isNotEmpty(attributionMap)) {
            String uid = config.getUid();
            Set<Long> attributionIdList = attributionMap.keySet();

            // 查询需要推送的称重曲线数据
            List<PushCurveDataDTO> curveList = weighCurveExtMapper.selectCurveToPush(uid, attributionIdList);

            if (CollUtil.isNotEmpty(curveList)) {
                // 按归属方分组
                Map<Long, List<PushCurveDataDTO>> attributionCurveMap = curveList.stream()
                        .collect(Collectors.groupingBy(PushCurveDataDTO::getAttributionId));

                attributionCurveMap.forEach((attributionId, curves) -> {
                    String attributionCode = attributionMap.get(attributionId);
                    if (attributionCode != null) {
                        // 获取该归属方下的所有唯一recordId
                        Set<String> recordIdSet = curves.stream()
                                .map(PushCurveDataDTO::getRecordId)
                                .collect(Collectors.toSet());

                        List<PushCurveDTO> pushDataList = new ArrayList<>();
                        recordIdSet.forEach(recordId -> {
                            try {
                                WeighCurveVO detail = weighCurveService.detail(recordId);
                                if (detail != null) {
                                    PushCurveDTO pushCurveDTO = new PushCurveDTO();

                                    // 设置记录ID
                                    pushCurveDTO.setRecordId(recordId);

                                    // curveDetail 方法的逻辑设置数据
                                    // 将 LocalDateTime 列表转换为格式化字符串列表
                                    if (CollUtil.isNotEmpty(detail.getTimes())) {
                                        List<String> formattedTimes = detail.getTimes().stream()
                                                .map(time -> time.format(ISO_DATE_TIME_FORMATTER))
                                                .collect(Collectors.toList());
                                        pushCurveDTO.setTimes(formattedTimes);
                                    }
                                    pushCurveDTO.setWeights(detail.getWeights());
                                    if (CollUtil.isNotEmpty(detail.getWeighPoints())) {
                                        List<PushCurveDTO.WeighDataDTO> collect = detail.getWeighPoints().stream().map(e -> {
                                            PushCurveDTO.WeighDataDTO weighDataDTO = new PushCurveDTO.WeighDataDTO();
                                            BeanUtils.copyProperties(e, weighDataDTO);
                                            weighDataDTO.setTime(ISO_DATE_TIME_FORMATTER.format(e.getTime()));
                                            return weighDataDTO;
                                        }).collect(Collectors.toList());
                                        pushCurveDTO.setWeighPoints(collect);
                                    }

                                    pushDataList.add(pushCurveDTO);
                                }
                            } catch (Exception e) {
                                log.error("获取称重曲线详情失败，recordId: {}", recordId, e);
                            }
                        });

                        if (CollUtil.isNotEmpty(pushDataList)) {
                            result.put(attributionCode, pushDataList);
                        }
                    }
                });
            }
        }

        return result;
    }

    @Override
    void afterCompletion(SdkRespDTO result, List<PushCurveDTO> pushedData) {
        if (CollUtil.isNotEmpty(pushedData) && CollUtil.isNotEmpty(result.getData())) {
            try {
                Set<String> pushedRecordIds = pushedData.stream()
                        .map(PushCurveDTO::getRecordId)
                        .collect(Collectors.toSet());

                Set<String> confirmedRecordIds = result.getData().stream()
                        .map(Object::toString)
                        .collect(Collectors.toSet());

                Set<String> validRecordIds = pushedRecordIds.stream()
                        .filter(confirmedRecordIds::contains)
                        .collect(Collectors.toSet());

                if (CollUtil.isNotEmpty(validRecordIds)) {
                    log.info("[称重曲线推送成功] 推送数量: {}, 确认数量: {}, 有效更新数量: {}, 有效RecordIDs: {}",
                            pushedRecordIds.size(), confirmedRecordIds.size(), validRecordIds.size(), validRecordIds);

                    weighCurveService.lambdaUpdate()
                            .in(WeighCurveDO::getRecordId, validRecordIds)
                            .set(WeighCurveDO::getPushState, 2)
                            .update();
                } else {
                    log.warn("[称重曲线推送异常] 推送的数据与确认的数据无交集, 推送RecordIDs: {}, 确认RecordIDs: {}", pushedRecordIds, confirmedRecordIds);
                }
            } catch (Exception e) {
                log.error("[称重曲线推送异常] 处理返回数据失败", e);
            }
        }
    }

}
