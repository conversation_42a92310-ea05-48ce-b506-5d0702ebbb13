package cn.pinming.microservice.material.client.management.common.dto.push;

import cn.pinming.microservice.material.client.management.common.model.ext.WeighDataGrossExtDO;
import cn.pinming.microservice.material.client.management.common.model.ext.WeighDataTareExtDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class PushConfirmWeightDTO {
    @ApiModelProperty(value = "扣杂")
    private BigDecimal deductRatio;

    @ApiModelProperty(value = "扣重")
    private BigDecimal deductWeight;

    @ApiModelProperty(value = "净重")
    private BigDecimal netWeight;

    @ApiModelProperty(value = "实重")
    private BigDecimal actualWeight;

    @ApiModelProperty(value = "称重单位")
    private String unitInuse;

    @ApiModelProperty(value = "毛重")
    private WeighDataGrossExtDO grossWeight;

    @ApiModelProperty(value = "皮重")
    private WeighDataTareExtDO tareWeight;
}
