package cn.pinming.microservice.material.client.management.common.dto.push;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PushDeliveryPurchaseDTO {
    @ApiModelProperty(value = "收货人姓名")
    private String cargoReceiver;

    @ApiModelProperty(value = "基石平台订单编号")
    private String jsBillOfParcelsNumber;

    @ApiModelProperty(value = "项目收货地址")
    private String orderAddress;

    @ApiModelProperty(value = "项目名称")
    private String orderName;

    @ApiModelProperty(value = "业务系统订单id")
    private String purchaseId;

    @ApiModelProperty(value = "收货人电话")
    private String receiverTelNumber;

    @ApiModelProperty(value = "订单备注")
    private String remark;

    @ApiModelProperty(value = "要货日期")
    private String requireDate;

    @ApiModelProperty(value = "业务系统订单供应商id")
    private String supplierId;

    @ApiModelProperty(value = "业务系统订单供应商名称")
    private String supplierName;
}
