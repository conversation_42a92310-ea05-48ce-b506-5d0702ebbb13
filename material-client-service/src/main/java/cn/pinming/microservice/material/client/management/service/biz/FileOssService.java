package cn.pinming.microservice.material.client.management.service.biz;

import cn.pinming.material.v2.model.dto.PicUrlDTO;
import cn.pinming.microservice.material.client.management.common.form.oss.FileConfirmForm;
import cn.pinming.microservice.material.client.management.common.form.oss.FileStsForm;
import cn.pinming.microservice.material.client.management.common.form.oss.MultiFileStsForm;
import cn.pinming.microservice.material.client.management.common.vo.oss.FileOssConfigVO;
import cn.pinming.microservice.material.client.management.common.vo.oss.StsResponseVO;
import cn.pinming.zhjz.cxptz.microservice.filecenter.api.model.StorageProviderConfigDto;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface FileOssService {


    FileOssConfigVO getConfig(MultiFileStsForm form);

    StorageProviderConfigDto getConfig(Byte subSystem);

    void confirm(FileConfirmForm form);

    void confirmList(List<String> fileUuids);

    String getUrlByUuid(String uuid);

    List<String> getUrlByUuids(List<String> uuids);

    Map<String, String> getUrlMapByUuids(List<String> uuids);

    String getPicUrlByUuid(String uuid, Byte picType);

    long spaceStatistics(String uid);

    void deleteFile(String fileId);

    StsResponseVO getFileSts(FileStsForm form);

    List<String> getUrlByUuidAndTime(List<String> uuids, Date date,Boolean isPre);

    Map<String, String>  getUrlByUuidAndTimeToMap(List<String> uuids, Date date, Boolean isPre);

    /**
     * 批量获取下载地址和预览地址
     * @param uuids
     * @return
     */
    List<PicUrlDTO> getPicUrlDTOs(List<String> uuids);
}
