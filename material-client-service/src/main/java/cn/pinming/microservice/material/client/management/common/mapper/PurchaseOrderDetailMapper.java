package cn.pinming.microservice.material.client.management.common.mapper;

import cn.pinming.microservice.material.client.management.common.dto.push.PurchaseDetailSyncDTO;
import cn.pinming.microservice.material.client.management.common.model.PurchaseOrderDetailDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 采购单明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-21
 */
public interface PurchaseOrderDetailMapper extends BaseMapper<PurchaseOrderDetailDO> {

    List<PurchaseDetailSyncDTO> selectPurchaseDetailSync(@Param("list") List<Long> purchaseIdList);
}
