package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 用户额外服务配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("s_user_ext_config")
public class UserExtConfigDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 用户id
     */
    private String uid;

    /**
     * 是否启用一二磅时间间隔（0-启用、1-禁用）
     */
    private Byte isTimeInterval;

    /**
     * 单据回收-间隔时间 开始
     */
    private Integer recycleStart;

    /**
     * 单据回收-间隔时间 结束
     */
    private Integer recycleEnd;

    /**
     * 是否启用忽略一二磅之间是否存在同车牌过磅记录（0-启用、1-禁用）
     */
    private Byte isIdenticalLicensePlate;

    /**
     * 是否启用忽略记录车牌号与系统识别车牌号是否不一致（0-启用、1-禁用）
     */
    private Byte isLicensePlateInconsistent;

    /**
     * 是否启用不检查待组装记录时间段内同车牌是否存在过磅记录（0-启用、1-禁用）
     */
    private Byte isCheckDataCombine;

    /**
     * 单据匹配方式 :  0 - 区域坐标匹配（旧） 1 - 索引匹配（新）
     */
    private Byte billMatchType;

    /**
     * 是否启用OCR识别（0-关闭、1-开启）
     */
    private Integer ocrEnable;

    /**
     * 百度AK
     */
    private String clientId;

    /**
     * 百度SK
     */
    private String clientSecret;
}
