package cn.pinming.microservice.material.client.management.common.form;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2024/8/21 17:05
 */
@Data
public class PurchaseForm {

    /**
     * 业务系统订单id
     */
    @NotBlank(message = "业务系统订单id为空")
    private String orderExtId;

    /**
     * 归属方code
     */
    @NotBlank(message = "归属方code为空")
    private String attributionCode;

    /**
     * 外部系统供应商id
     */
    @NotBlank(message = "外部系统供应商id为空")
    private String supplierExtId;

    /**
     * 计划使用部位
     */
//    @NotBlank(message = "计划使用部位为空")
    private String position;

    /**
     * wbsId
     */
    private String wbsId;

    /**
     * 收货项目
     */
    @NotBlank(message = "收货项目为空")
    private String project;

    /**
     * 收货地址
     */
    @NotBlank(message = "收货地址为空")
    private String address;

    /**
     * 收货人姓名
     */
//    @NotBlank(message = "收货人姓名为空")
    private String receiver;

    /**
     * 收货人电话
     */
//    @NotBlank(message = "收货人电话为空")
    private String mobile;

    /**
     * 要货日期
     */
    @NotNull(message = "要货日期为空")
    private LocalDate receiveDate;

    @NotNull(message = "订单来源为空")
    private String source;

    /**
     * 备注
     */
    @Length(max = 200, message = "备注字数超过200")
    private String remark;

    @NotNull(message = "订单清单为空")
    @Size(min = 1, message = "订单清单不能为空")
    @Valid
    private List<PurchaseItemForm> list;

}
