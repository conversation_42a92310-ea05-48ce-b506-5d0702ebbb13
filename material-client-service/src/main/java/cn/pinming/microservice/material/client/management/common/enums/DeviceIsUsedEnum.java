package cn.pinming.microservice.material.client.management.common.enums;

public enum DeviceIsUsedEnum {
    START((byte) 1, "启用"),
    STOP((byte) 2, "禁用");

    private byte type;
    private String description;

    DeviceIsUsedEnum(byte type, String description) {
        this.type = type;
        this.description = description;
    }

    public byte value() {
        return type;
    }

    public String description() {
        return description;
    }
}
