package cn.pinming.microservice.material.client.management.common.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 推送订阅配置表单
 *
 * <AUTHOR>
 */
@Data
public class ReceiptPushConfigForm {
    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "数据推送地址")
    private String pushUrl;

    @ApiModelProperty(value = "数据推送开关 1 关闭 2 开启")
    private Integer pushStatus;

    @ApiModelProperty(value = "排除的数据归属方")
    private String excludedAttributionIds;

    @ApiModelProperty(value = "请求地址")
    private String endpoint;

    @ApiModelProperty(value = "应用key")
    private String appKey;

    @ApiModelProperty(value = "应用密钥key")
    private String appSecretKey;

    @ApiModelProperty(value = "推送类型 1 sdk推送 2 一般推送")
    private Byte pushType;
}
