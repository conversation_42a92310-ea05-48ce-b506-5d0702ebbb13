package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.microservice.material.client.management.common.form.supplier.SupplierConfigForm;
import cn.pinming.microservice.material.client.management.common.form.supplier.SupplierConfigPushForm;
import cn.pinming.microservice.material.client.management.common.mapper.SupplierConfigMapper;
import cn.pinming.microservice.material.client.management.common.model.SelfQrcodeDO;
import cn.pinming.microservice.material.client.management.common.model.SupplierConfigDO;
import cn.pinming.microservice.material.client.management.common.model.UserDO;
import cn.pinming.microservice.material.client.management.common.vo.SupplierConfigVO;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizErrorException;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizExceptionMessageEnum;
import cn.pinming.microservice.material.client.management.infrastructure.util.UserIdUtil;
import cn.pinming.microservice.material.client.management.service.biz.ISelfQrcodeService;
import cn.pinming.microservice.material.client.management.service.biz.SupplierConfigService;
import cn.pinming.microservice.material.client.management.service.biz.UserService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SupplierConfigServiceImpl extends ServiceImpl<SupplierConfigMapper, SupplierConfigDO> implements SupplierConfigService {

    @Resource
    private UserIdUtil userIdUtil;
    @Resource
    private UserService userService;
    @Resource
    private ISelfQrcodeService selfQrcodeService;

    @Override
    public List<SupplierConfigVO> listByUID() {
        List<SupplierConfigDO> list = lambdaQuery().eq(SupplierConfigDO::getCreateId, userIdUtil.getUId()).list();
        if (CollUtil.isNotEmpty(list)) {
            return BeanUtil.copyToList(list, SupplierConfigVO.class);
        }
        return Collections.emptyList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveConfig(SupplierConfigForm form) {
        String uid = userIdUtil.getUId();
        Long id = form.getId();
        String supplierExtId = form.getSupplierExtId().trim();
        String supplierSysId = form.getSupplierSysId().trim();
        List<UserDO> userList = userService.lambdaQuery().select(UserDO::getUid).list();
        userList.stream().filter(user -> user.getUid().equals(supplierSysId)).findAny()
                .orElseThrow(() -> new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "供应商在基石平台的唯一ID不存在,请联系管理员"));
        // 供应商基石租户id对外部系统id 关系变更为可一对多 外部系统id在租户内唯一
        // 租户内唯一
        List<SupplierConfigDO> list = lambdaQuery().eq(SupplierConfigDO::getCreateId, uid).ne(id != null, SupplierConfigDO::getId, id)
                .select(SupplierConfigDO::getSupplierExtId, SupplierConfigDO::getSupplierSysId).list();
        if (CollUtil.isNotEmpty(list)) {
            for (SupplierConfigDO config : list) {
                if (supplierExtId.equals(config.getSupplierExtId())) {
                    throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "来自业务系统的供应商唯一ID已存在");
                }
//                if (supplierSysId.equals(config.getSupplierSysId())) {
//                    throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "供应商在基石平台的ID已存在");
//                }
            }
        }
        SupplierConfigDO supplierConfigDO = new SupplierConfigDO();
        BeanUtils.copyProperties(form, supplierConfigDO);
        saveOrUpdate(supplierConfigDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(Long id) {
        String uid = userIdUtil.getUId();
        SupplierConfigDO supplierConfig = getById(id);
        if (supplierConfig == null) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "数据不存在");
        }
        if (!supplierConfig.getCreateId().equals(uid)) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "非租户数据，无法删除");
        }
        //  判断供应商是否被使用 - 磅房自助码管理
        List<SelfQrcodeDO> list = selfQrcodeService.lambdaQuery().isNotNull(SelfQrcodeDO::getSupplierId).eq(SelfQrcodeDO::getCreateId, uid).list();
        if (CollUtil.isNotEmpty(list)) {
            long count = list.stream().flatMap(obj -> StrUtil.split(obj.getSupplierId(), StrUtil.COMMA).stream().map(Long::parseLong))
                    .filter(supplierId -> supplierId.equals(id)).count();
            if (count > 0) {
                throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "该供应商已被使用，无法删除");
            }
        }
        removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savePushConfig(SupplierConfigPushForm form) {
        String uid = userIdUtil.getUId();
        SupplierConfigDO supplierConfig = getById(form.getId());
        if (supplierConfig == null) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "数据不存在");
        }
        if (!supplierConfig.getCreateId().equals(uid)) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "非租户数据，无法编辑");
        }
        supplierConfig.setDeliveryPush(form.getDeliveryPush());
        supplierConfig.setConfirmPush(form.getConfirmPush());
        supplierConfig.setTimeout(form.getTimeout());
        supplierConfig.setMultiCargo(form.getMultiCargo());
        supplierConfig.setSameTruckMinDuration(form.getSameTruckMinDuration());
        if (StrUtil.isNotBlank(form.getGroup())) {
            Set<String> groupSet = StrUtil.split(form.getGroup(), StrUtil.COMMA).stream()
                    .filter(StrUtil::isNotBlank).collect(Collectors.toSet());
            supplierConfig.setGroup(String.join(StrUtil.COMMA, groupSet));
        }else {
            supplierConfig.setGroup(null);
        }
        updateById(supplierConfig);
    }
}
