package cn.pinming.microservice.material.client.management.common.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class RebarCheckDetailForm {
    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "材料id")
    private Long materialId;

    @ApiModelProperty(value = "单根长度(米)")
    private BigDecimal length;

    @ApiModelProperty(value = "送货单根数")
    private Long sendAmount;

    @ApiModelProperty(value = "实点根数")
    private Long actualAmount;

    @ApiModelProperty(value = "送货重量")
    private BigDecimal sendWeight;

    @ApiModelProperty(value = "送货重量单位")
    private String sendWeightUnit;

    @ApiModelProperty(value = "确认根数")
    private BigDecimal confirmAmount;

    @ApiModelProperty(value = "确认重量")
    private BigDecimal confirmWeight;
}
