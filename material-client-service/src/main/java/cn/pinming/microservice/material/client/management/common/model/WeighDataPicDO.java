package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 称重记录照片表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("d_weigh_data_pic")
public class WeighDataPicDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 称重记录id
     */
    private String recordId;

    /**
     * 本地文件路径
     */
    private String filePath;

    /**
     * oss 文件id
     */
    private String fileId;

    /**
     * 文件大小
     */
    private BigDecimal size;

    /**
     * 1 过磅照片 2 磅房、操作棚照片 3 操作人照片
     */
    private Integer type;

    /**
     * 照片本地创建时间
     */
    private LocalDateTime localCTime;

    /**
     * 照片本地id
     */
    private String localId;

    /**
     * 用户id
     */
    private String uid;

    /**
     * 归属方id
     */
    private Long attributionId;

    /**
     * 推送状态 1 未推送 2 队列中 3 已推送
     */
    private Byte pushStatus;

    /**
     * 首次队列开始时间 为什么明确为"首次"，之后还可能会有多次失败，导致数据一直处于"队列中"状态，推不推就根据有没有回调确认
     */
    private LocalDateTime waitTime;
}
