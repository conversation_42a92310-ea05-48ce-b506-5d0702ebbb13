package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 服务调用表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("s_app_invocation_daily_log")
public class AppInvocationDailyLogDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 服务id
     */
    private Long appId;

    /**
     * 统计日期 YYYY-mm-dd
     */
    private String date;

    /**
     * 已调用次数
     */
    private Long usedApiTotal;

    /**
     * 归属方id
     */
    private Long attributionId;
}
