package cn.pinming.microservice.material.client.management.common.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class CheckVO implements Serializable {
    private static final long serialVersionUID = 7506230916534641422L;

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "验收编号")
    private String no;

    @ApiModelProperty(value = "推送状态 1 未推, 2 已推")
    private Integer pushStatus;

    @ApiModelProperty(value = "是否归档 1 否, 2 是")
    private Integer isVerify;

    @ApiModelProperty(value = "数据归属方名称")
    private String attributionName;

    @ApiModelProperty(value = "用户名称")
    private String consumeName;

    @ApiModelProperty(value = "收货时间")
    private LocalDateTime truckTime;

    @ApiModelProperty(value = "钢筋类型 1 直螺纹,2 盘螺")
    private Integer type;

    @ApiModelProperty(value = "收料总数量")
    private BigDecimal receiveAmount;

    @ApiModelProperty(value = "数据归属方code")
    private String attributionCode;

    @ApiModelProperty(value = "设备sn")
    private String phoneSn;

    @ApiModelProperty(value = "验收材料列表")
    private List<CheckMaterialVO> list;
}
