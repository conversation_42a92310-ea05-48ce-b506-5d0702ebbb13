package cn.pinming.microservice.material.client.management.common.form;

import cn.pinming.microservice.material.client.management.common.form.oss.OCRModuleDetailForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class OCRModuleForm {
    @ApiModelProperty(value = "模版id")
    private Long id;

    @ApiModelProperty(value = "模版父id")
    private Long pid;

    @ApiModelProperty(value = "业务数据模板名称")
    private String name;

    @ApiModelProperty(value = "业务数据模板使用第三方")
    private String client;

    @ApiModelProperty(value = "模版底图")
    private String base64;

    @ApiModelProperty(value = "数据组")
    @NotEmpty(message = "数据组不能为空")
    private List<OCRModuleDetailForm> list;

    @ApiModelProperty(value = "模版类型  1 自由创建 2 使用自建数据创建 3 使用第三方平台数据创建 4 自建数据创建 5 平台数据创建")
    private Byte type;

    @ApiModelProperty(value = "外部系统id")
    private String extId;

    @ApiModelProperty("单据匹配方式 :  0 - 区域坐标匹配（旧） 1 - 索引匹配（新）")
    @NotNull(message = "单据匹配方式为空")
    private Byte billMatchType;

    @ApiModelProperty("html")
    private String html;
}
