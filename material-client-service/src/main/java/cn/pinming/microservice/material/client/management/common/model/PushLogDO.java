package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 推送日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("d_push_log")
public class PushLogDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 用户id
     */
    private String uid;

    /**
     * 推送内容
     */
    private String body;

    /**
     * 推送状态 1 进入队列 2 已发送 3 已确认
     */
    private Byte status;

    /**
     * 消息主键ID
     */
    private String messageId;

    /**
     * 原因
     */
    private String reason;

    /**
     * 创建人ID
     */
    private String createId;

    /**
     * 修改人ID
     */
    private String modifyId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtModify;
}
