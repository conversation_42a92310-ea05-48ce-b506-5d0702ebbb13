package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <p>
 * 钢筋材料表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("s_rebar_material")
public class RebarMaterialDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 物料规格
     */
    private String materialSpec;

    /**
     * 外部系统代码
     */
    private String extCode;

    /**
     * 理论重量值
     */
    private BigDecimal weighValue;

    /**
     * 钢筋类型 1 直螺纹,2 盘螺
     */
    private Integer type;


}
