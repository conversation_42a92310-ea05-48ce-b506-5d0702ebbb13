package cn.pinming.microservice.material.client.management.common.model.ext;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class WeighDataConfirmExtDO {
    @ApiModelProperty(value = "发货单编号")
    private String jsDeliveryNo;

    @ApiModelProperty(value = "确认数据的本地创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime terminalDataCreateTime;

    @ApiModelProperty(value = "确认数据id")
    private String terminalDataId;

    @ApiModelProperty(value = "确认数据的本地修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime terminalDataModifyTime;

    @ApiModelProperty(value = "确认数据")
    private WeighDataConfirmDetailExtDO confirmData;

    @ApiModelProperty(value = "确认物料明细")
    private List<WeighDataDeliveryExtDO> list;

    @ApiModelProperty(value = "一二磅称重数据 数量为1为一磅，数量为2为完整过磅")
    private List<WeighDataExtDO> weighDataList;
}
