package cn.pinming.microservice.material.client.management.infrastructure.util;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.util.Base64;

public class AesEncryptUtil {

    public static String aesEncrypt(String s, String encryptKey) throws Exception {
        byte[] keyBytes = encryptKey.getBytes();
        SecretKey secretKey = new SecretKeySpec(keyBytes, "AES");
        Cipher cipher = Cipher.getInstance("AES");
        cipher.init(Cipher.ENCRYPT_MODE, secretKey);
        byte[] encryptedBytes = cipher.doFinal(s.getBytes());
        return Base64.getEncoder().encodeToString(encryptedBytes);
    }

}
