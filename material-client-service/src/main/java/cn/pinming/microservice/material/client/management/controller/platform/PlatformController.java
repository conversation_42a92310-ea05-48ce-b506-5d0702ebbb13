package cn.pinming.microservice.material.client.management.controller.platform;

import cn.hutool.core.util.ObjectUtil;
import cn.pinming.microservice.material.client.management.common.model.UserDO;
import cn.pinming.microservice.material.client.management.common.query.ConfirmPullQuery;
import cn.pinming.microservice.material.client.management.common.query.WeighPullQuery;
import cn.pinming.microservice.material.client.management.common.vo.WeighConfirmPullVO;
import cn.pinming.microservice.material.client.management.common.vo.WeighDataPullVO;
import cn.pinming.microservice.material.client.management.infrastructure.constant.SdkQueryConstant;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizErrorException;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizExceptionMessageEnum;
import cn.pinming.microservice.material.client.management.infrastructure.response.SingleResponse;
import cn.pinming.microservice.material.client.management.infrastructure.util.HeaderUtil;
import cn.pinming.microservice.material.client.management.infrastructure.util.StpKit;
import cn.pinming.microservice.material.client.management.service.biz.IWeighDataConfirmService;
import cn.pinming.microservice.material.client.management.service.biz.UserService;
import cn.pinming.microservice.material.client.management.service.biz.WeighDataService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2025/4/9 10:13
 */
@Api(value = "platform", tags = {"platform"})
@RestController
@RequestMapping("/api/platform")
public class PlatformController {
    @Resource
    private UserService userService;
    @Resource
    private WeighDataService weighDataService;
    @Resource
    private IWeighDataConfirmService weighDataConfirmService;

    @GetMapping("/token")
    public SingleResponse<?> getToken(@RequestParam String appKey, @RequestParam String appSecret) {
        UserDO user = userService.getUserByAppKey(appKey);
        if (user == null || !user.getAppSecretKey().equals(appSecret)) {
            throw new BizErrorException(BizExceptionMessageEnum.PLATFORM_AUTH_ERROR);
        }
        // 清除上一次的token
        StpKit.PLATFORM.logout(appKey);
        // 生成新的token
        StpKit.PLATFORM.login(appKey, 24 * 60 * 60);
        Map<String, Object> result = new HashMap<>();
        result.put("accessToken", StpKit.PLATFORM.getTokenValue());
        result.put("expiresIn", StpKit.PLATFORM.getTokenTimeout());
        return SingleResponse.of(result);
    }

    @GetMapping("/user")
    public SingleResponse<?> getUser(HttpServletRequest request) {
        String token = HeaderUtil.getHeaderAuthorization(request);
        String appKey = (String) StpKit.PLATFORM.getLoginIdByToken(token);
        UserDO user = userService.getUserByAppKey(appKey);
        return SingleResponse.of(user);
    }


    @PostMapping("/data")
    public SingleResponse<?> getData(HttpServletRequest request,@RequestBody WeighPullQuery query) {
        String token = HeaderUtil.getHeaderAuthorization(request);
        String appKey = (String) StpKit.PLATFORM.getLoginIdByToken(token);
        UserDO user = userService.getUserByAppKey(appKey);
        IPage<WeighDataPullVO> page = new Page<>();
        if (ObjectUtil.isNotNull(user)) {
            if (query.getSize() > SdkQueryConstant.DATA_MAX_SIZE) {
                query.setSize(SdkQueryConstant.DATA_MAX_SIZE);
            }
            query.setUid(user.getUid());
            page = weighDataService.getDataPull(query);
        }
        return SingleResponse.of(page);
    }


    @PostMapping("/confirm")
    public SingleResponse<?> getConfirm(HttpServletRequest request,@RequestBody ConfirmPullQuery query) {
        String token = HeaderUtil.getHeaderAuthorization(request);
        String appKey = (String) StpKit.PLATFORM.getLoginIdByToken(token);
        UserDO user = userService.getUserByAppKey(appKey);
        IPage<WeighConfirmPullVO> page = new Page<>();
        if (ObjectUtil.isNotNull(user)) {
            if (query.getSize() > SdkQueryConstant.DATA_MAX_SIZE) {
                query.setSize(SdkQueryConstant.DATA_MAX_SIZE);
            }
            query.setUid(user.getUid());
            page = weighDataConfirmService.getConfirm(query);
        }
        return SingleResponse.of(page);
    }


}
