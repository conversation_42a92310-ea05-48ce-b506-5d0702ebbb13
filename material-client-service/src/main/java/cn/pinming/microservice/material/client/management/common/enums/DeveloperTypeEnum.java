package cn.pinming.microservice.material.client.management.common.enums;

public enum DeveloperTypeEnum {
    START((byte) 0, "使用中"),
    STOP((byte) 1, "停用中"),
    ;

    private byte type;
    private String description;

    DeveloperTypeEnum(byte type, String description) {
        this.type = type;
        this.description = description;
    }

    public byte value() {
        return type;
    }

    public String description() {
        return description;
    }

    public static String descByType(Byte type) {
        for (DeveloperTypeEnum value : DeveloperTypeEnum.values()) {
            if (value.type == type) {
                return value.description;
            }
        }
        return "";
    }
}
