package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 用户业务配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("s_user_business_config")
public class UserBusinessConfigDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 用户id
     */
    private String uid;

    /**
     * 推送类型 1 sdk推送 2 一般推送 3 确认单推送 4 订单转发
     */
    private Byte pushType;

    /**
     * 数据推送地址
     */
    private String weighDataPushUrl;

    /**
     * 数据推送开关 1 关闭 2 开启
     */
    private Integer weighDataPushStatus;

    /**
     * 排除的数据归属方配置
     */
    private String weighDataPushAttribution;

    /**
     * 照片推送地址
     */
    private String weighPicPushUrl;

    /**
     * 照片推送开关 1 关闭 2 开启
     */
    private Integer weighPicPushStatus;

    /**
     * 称重单位(1-默认不处理、2-吨、3-千克)
     */
    private Integer weighUnit;

    /**
     * 请求地址
     */
    private String endpoint;

    /**
     * 请求Header
     */
    private String requestHeader;

    /**
     * 请求模式(none-无、signature-加签)
     */
    private String requestMode;

    /**
     * 应用key
     */
    private String appKey;

    /**
     * 应用密钥key
     */
    private String appSecretKey;

    /**
     * 签名模板
     */
    private String signatureTemplate;

    /**
     * 签名算法
     */
    private String signatureAlgorithm;

    /**
     * 返回类型(JSONObject、JSONArray、String)
     */
    private String responseType;

    /**
     * 返回标识(key=value)
     */
    private String responseFlag;


}
