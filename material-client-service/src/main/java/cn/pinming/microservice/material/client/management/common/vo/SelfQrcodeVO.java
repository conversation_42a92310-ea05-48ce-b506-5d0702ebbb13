package cn.pinming.microservice.material.client.management.common.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 *
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2024/8/20 17:44
 */
@Data
public class SelfQrcodeVO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 业务id
     */
    private String bizId;

    /**
     * 入口名称
     */
    private String name;

    /**
     * 归属方id
     */
    private Long attributionId;

    /**
     * 供应商id 逗号分割
     */
    private String supplierId;

    /**
     * 供应商id列表
     */
    private List<Long> supplierIdList;

    /**
     * 状态 0 启用 1 禁用
     */
    private Byte enabled;

    /**
     * 归属方名称
     */
    private String attributionName;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * url
     */
    private String url;
}
