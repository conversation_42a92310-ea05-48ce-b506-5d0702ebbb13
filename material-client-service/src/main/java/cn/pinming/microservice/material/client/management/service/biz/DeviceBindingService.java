package cn.pinming.microservice.material.client.management.service.biz;

import cn.pinming.microservice.material.client.management.common.form.BindingForm;
import cn.pinming.microservice.material.client.management.common.model.DeviceBindingDO;
import cn.pinming.microservice.material.client.management.common.vo.DeviceAttributionVO;
import cn.pinming.microservice.material.client.management.common.vo.DeviceUserVO;
import cn.pinming.microservice.material.client.management.common.vo.selfcheck.SelfCheckModeConfigVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface DeviceBindingService extends IService<DeviceBindingDO> {
    void own(String deviceSn, String deviceType);

    void isReceive(Long id);

    void binding(BindingForm form);

    void updateBinding(BindingForm form);

    void delete(Long id);

    void unbinding(Long id);

    List<DeviceUserVO> userList();

    List<DeviceAttributionVO> attributionList(Long attributionId);

    String getDevicePwd(String sn, String deviceType);

    Integer getDeviceMode(String deviceSn, String deviceType);

    SelfCheckModeConfigVO getDeviceModeConfig(String deviceSn, String deviceType);
}
