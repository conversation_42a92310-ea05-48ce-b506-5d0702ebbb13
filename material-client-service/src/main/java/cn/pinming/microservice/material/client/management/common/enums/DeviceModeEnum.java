package cn.pinming.microservice.material.client.management.common.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 司机自助确认设备模式 1 运单模式  2 单据回收模式 3 双模式（1,2）(仅适用于独立小设备)
 * 4 仅扫码模式（仅适用于独立小设备） 5 仅毛皮重模式（仅适用于新型一体机终端） 6 简单称重模式（仅适用于新型一体机终端）
 */
@Getter
public enum DeviceModeEnum {
    ONE(1, "运单模式"),
    TWO(2, "单据回收模式"),
    THREE(3, "双模式(仅适用于独立小设备)"),
    FOUR(4, "仅扫码模式(仅适用于独立小设备)"),
    FIVE(5, "仅毛皮重模式(仅适用于新型一体机终端)"),
    SIX(6, "简单称重模式(仅适用于新型一体机终端)"),
    ;

    private final Integer val;
    private final String desc;

    DeviceModeEnum(Integer val, String desc) {
        this.val = val;
        this.desc = desc;
    }

    public static final Map<Integer, String> KEY_MAP = Arrays.stream(values()).collect(Collectors.toMap(DeviceModeEnum::getVal, DeviceModeEnum::getDesc));

    public static final Map<String, Integer> VAL_MAP = Arrays.stream(values()).collect(Collectors.toMap(DeviceModeEnum::getDesc, DeviceModeEnum::getVal));


}
