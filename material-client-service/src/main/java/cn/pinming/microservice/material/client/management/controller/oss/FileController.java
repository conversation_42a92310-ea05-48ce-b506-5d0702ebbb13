package cn.pinming.microservice.material.client.management.controller.oss;

import cn.pinming.microservice.material.client.management.infrastructure.exception.BizErrorException;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizExceptionMessageEnum;
import cn.pinming.microservice.material.client.management.infrastructure.response.SingleResponse;
import cn.pinming.microservice.material.client.management.proxy.FileServiceProxy;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2023/6/15 13:45
 */
@Api(value = "file", tags = {"file"})
@RestController
@RequestMapping("/api/file")
@Slf4j
public class FileController {

    @Resource
    private FileServiceProxy fileServiceProxy;

    // web 端上传文件，私有化部署用
    @ApiOperation(value = "文件上传", responseReference = "SingleResponse«String»", nickname = "upload")
    @PostMapping("/upload")
    public SingleResponse<String> upload(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "文件为空");
        }
        String fileId = fileServiceProxy.uploadFileForUuid(file);
        return SingleResponse.of(fileId);
    }

}
