package cn.pinming.microservice.material.client.management.controller.common;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.pinming.material.v2.Material;
import cn.pinming.material.v2.MaterialClientBuilder;
import cn.pinming.material.v2.model.WeighDataAssemble;
import cn.pinming.material.v2.model.form.WeighDataAssembleForm;
import cn.pinming.microservice.material.client.management.common.enums.DeveloperAppEnum;
import cn.pinming.microservice.material.client.management.common.form.*;
import cn.pinming.microservice.material.client.management.common.form.oss.BaiduOCRForm;
import cn.pinming.microservice.material.client.management.common.mapper.ext.ReceiptModuleDetailConfigExtMapper;
import cn.pinming.microservice.material.client.management.common.model.UserExtConfigDO;
import cn.pinming.microservice.material.client.management.common.vo.*;
import cn.pinming.microservice.material.client.management.common.vo.ocr.OCRVO;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizErrorException;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizExceptionMessageEnum;
import cn.pinming.microservice.material.client.management.infrastructure.response.SingleResponse;
import cn.pinming.microservice.material.client.management.infrastructure.util.PicUtil;
import cn.pinming.microservice.material.client.management.infrastructure.util.StpKit;
import cn.pinming.microservice.material.client.management.infrastructure.util.ocr.BaiduOCRUtil;
import cn.pinming.microservice.material.client.management.service.biz.*;
import cn.pinming.zhjz.cxptz.microservice.filecenter.api.FileCenterService;
import cn.pinming.zhjz.cxptz.microservice.filecenter.api.model.DownloadUrlOptionsDto;
import cn.pinming.zhjz.cxptz.microservice.filecenter.api.model.FileIdentityDto;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.FileNotFoundException;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Api(value = "common", tags = {"common"})
@RestController
@RequestMapping("/api/common")
@Slf4j
public class CommonController {
    @Resource
    private WeighDataService weighDataService;
    @DubboReference
    private FileCenterService fileCenterService;
    @Resource
    private DeveloperService developerService;
    @Resource
    private AuthorizationFileService authorizationFileService;
    @Resource
    private OCRModuleService ocrModuleService;
    @Resource
    private ReceiptRecycleService receiptRecycleService;
    @Value("${imatchocr.ocr}")
    private String imatchocrOcr;
    @Value("${sdk.host:http://localhost:8080}")
    private String sdkHost;
    @NacosValue("${material-ocr.file.upload}")
    private String materialOcrFileUpload;
    @Resource
    private IWeighCurveService weighCurveService;
    @Resource
    private ICheatAlarmLogService cheatAlarmLogService;
    @Resource
    private UserExtConfigService userExtConfigService;
    @Resource
    private IWeighCurveAlarmService weighCurveAlarmService;
    @Resource
    private UserConsumerCombineService userConsumerCombineService;
    @Resource
    private PicUtil picUtil;
    @Resource
    private BaiduOCRUtil baiduOCRUtil;
    @Resource
    private ReceiptModuleDetailConfigExtMapper receiptModuleDetailConfigExtMapper;
    @Resource
    private AppInvocationDailyLogService appInvocationDailyLogService;

    @ApiOperation(value = "照片上传", responseReference = "SingleResponse«?»", nickname = "pic")
    @PostMapping("/pic")
    public SingleResponse<?> pic(@RequestBody String json, @RequestParam(required = false) String deviceType) {
//        log.info("照片上传:{}", json);
        WeighPicStandardForm form = JSONUtil.toBean(json, WeighPicStandardForm.class);
        weighDataService.pic(form, deviceType);
//        log.info("照片上传成功");
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "数据上传", responseReference = "SingleResponse«?»", nickname = "upload")
    @PostMapping("/upload")
    public SingleResponse<?> upload(@RequestBody String json, @RequestParam(required = false) String deviceType) {
//        log.info("称重数据上传:{}", json);
        WeighDataStandardForm bean = JSONUtil.toBean(json, WeighDataStandardForm.class);
        weighDataService.upload(bean, deviceType);
//        log.info("称重数据上传成功");
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "称重曲线上传", responseReference = "SingleResponse«?»", nickname = "curve")
    @PostMapping("/curve")
    public SingleResponse<?> curve(@RequestBody String json, @RequestParam(required = false) String deviceType) {
        CurveForm form = JSONUtil.toBean(json, CurveForm.class);
        weighCurveService.upload(form, deviceType);
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "数据同步", responseReference = "SingleResponse«DataSyncVO»", nickname = "dataSync")
    @GetMapping("/sync/{deviceSn}")
    public SingleResponse<DataSyncVO> sync(@PathVariable("deviceSn") String deviceSn, @RequestParam(required = false) String deviceType) {
        DataSyncVO vo = weighDataService.sync(deviceSn, deviceType);
        return SingleResponse.of(vo);
    }

    /**
     * 获取logo绝对路径，永久
     *
     * @return
     */
    @GetMapping("/pic/{uuid}")
    public SingleResponse<?> uuid(@PathVariable("uuid") String uuid) {
        FileIdentityDto dto = new FileIdentityDto();
        dto.setFileUuid(uuid);
        DownloadUrlOptionsDto optionsDto = new DownloadUrlOptionsDto();
        LocalDate currentDate = LocalDate.now();
        // 将当前日期增加 100 年
        LocalDate futureDate = currentDate.plusYears(100);
        Date date = Date.from(futureDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        optionsDto.setTime(date);
        String s = fileCenterService.acquireFileDownloadUrl(dto, optionsDto);
        return SingleResponse.of(s);
    }

    @ApiOperation(value = "服务初始化", responseReference = "SingleResponse«?»", nickname = "serviceInit")
    @GetMapping("/init")
    public SingleResponse<?> init() {
        developerService.init();
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "伪登录", responseReference = "SingleResponse«String»", nickname = "fakeLogin")
    @GetMapping("/fakeLogin/{uid}")
    public SingleResponse<String> fakeLogin(@PathVariable("uid") String uid) {
        StpKit.MANAGER.login(uid, 60 * 60);
//        StpManagerUtil.login(uid,60 * 60);
        String tokenValue = StpKit.MANAGER.getTokenValue();
        return SingleResponse.of(tokenValue);
    }

    @ApiOperation(value = "单机版授权文件是否有效", responseReference = "SingleResponse«Boolean»", nickname = "authorizationFileIsValid")
    @GetMapping("/authorization/file/valid")
    public SingleResponse<Boolean> authorizationFileIsValid(@RequestParam("id") Long id) {
        Boolean valid = authorizationFileService.authorizationFileIsValid(id);
        return SingleResponse.of(valid);
    }

    @ApiOperation(value = "OCRTest", responseReference = "SingleResponse«String»", nickname = "test")
    @PostMapping("/ocrTest")
    public SingleResponse<String> test(@RequestBody PicForm form) throws Exception {
        String res = HttpUtil.post(imatchocrOcr, JSON.toJSONString(form));
        return SingleResponse.of(res);
    }

    @ApiOperation(value = "获取区域模版详情列表", responseReference = "SingleResponse«List<OCRVO>»", nickname = "listOCRModuleDetail")
    @GetMapping("/list/module/detail/{uid}/{attributionCode}")
    public SingleResponse<List<OCRVO>> listModuleDetail(@PathVariable("uid") String uid, @PathVariable("attributionCode") String attributionCode) {
        List<OCRVO> list = ocrModuleService.listModuleDetail(uid, attributionCode);
        return SingleResponse.of(list);
    }

//    @ApiOperation(value = "获取区域模版详情列表", responseReference = "SingleResponse«List<OCRTemplateVO>»", nickname = "listOcrTemplateDetail")
//    @GetMapping("/template/detail/{uid}/{attributionCode}")
//    public SingleResponse<List<OCRTemplateVO>> listTemplateDetail(@PathVariable String uid, @PathVariable String attributionCode) {
//        List<OCRTemplateVO> list = ocrModuleService.listTemplateDetail(uid, attributionCode);
//        return SingleResponse.of(list);
//    }


    @ApiOperation(value = "单据终端回收", responseReference = "SingleResponse«Boolean»", nickname = "terminalRecycle")
    @PostMapping("/terminalRecycle")
    public SingleResponse<Boolean> terminalRecycle(@RequestPart String deviceSn, @RequestPart MultipartFile[] files) {
        receiptRecycleService.terminalRecycle(deviceSn, files);
        return SingleResponse.of(true);
    }

    @ApiOperation(value = "根据设备码查找租户、用户、归属方", responseReference = "SingleResponse«List<ConsumerVO>»", nickname = "getInfoByPhoneSn")
    @GetMapping("/getInfoByPhoneSn/{phoneSn}")
    public SingleResponse<List<ConsumerVO>> getInfoByPhoneSn(@PathVariable("phoneSn") String phoneSn) {
        List<ConsumerVO> list = userConsumerCombineService.getInfoByPhoneSn(phoneSn);
        return SingleResponse.of(list);
    }

    @ApiOperation(value = "设备码登录", responseReference = "SingleResponse«String»", nickname = "loginByPhoneSn")
    @GetMapping("/loginByPhoneSn/{phoneSn}/{uId}/{attributionId}")
    public SingleResponse<String> loginByPhoneSn(@PathVariable("phoneSn") String phoneSn, @PathVariable("uId") String uId, @PathVariable("attributionId") Long attributionId) {
        String token = userConsumerCombineService.loginByPhoneSn(phoneSn, uId, attributionId);
        return SingleResponse.of(token);
    }

    /**
     * 钢筋小助手数据组装
     * 前端iframe无法传递cookie，采用appKey和appSecret传递验证
     */
    @ApiOperation(value = "组装称重数据", responseReference = "SingleResponse«WeighDataAssemble»", nickname = "checkAssemble")
    @PostMapping("/check/assemble")
    public SingleResponse<WeighDataAssemble> checkAssemble(@RequestBody CheckAssembleForm checkAssembleForm) {
        Material material = new MaterialClientBuilder().build(sdkHost, checkAssembleForm.getAppKey(), checkAssembleForm.getAppSecretKey());
        WeighDataAssembleForm weighDataAssembleForm = new WeighDataAssembleForm();
        weighDataAssembleForm.setFirst(checkAssembleForm.getFirst());
        weighDataAssembleForm.setSecond(checkAssembleForm.getSecond());
        weighDataAssembleForm.setAttributionCode(checkAssembleForm.getAttributionCode());
        return SingleResponse.of(material.weighDataAssemble(weighDataAssembleForm));
    }

    /**
     * 前端组装SDK批量查询称重数据详情
     * 安全考虑数组长度限制为最多10个
     */
    @ApiOperation(value = "批量查询称重数据详情", responseReference = "SingleResponse«List<WeighDataDetailVO>»", nickname = "detailList")
    @GetMapping("/weighData/detailList")
    public SingleResponse<List<WeighDataDetailVO>> detailList(@RequestParam("ids") List<String> ids) {
        return SingleResponse.of(weighDataService.detailList(ids));
    }

    @ApiOperation(value = "基石上传数据照片过期问题修复")
    @PostMapping("/expirePic/fix")
    public SingleResponse<List<ExpirePicFixForm>> expirePicFix(@RequestBody String json) {
        List<ExpirePicFixForm> list = JSONArray.parseArray(json, ExpirePicFixForm.class);
        List<ExpirePicFixForm> result = weighDataService.expirePicFix(list);
        return SingleResponse.of(result);
    }

    @ApiOperation(value = "根据2个recordId刷新业务系统进出场照片")
    @PostMapping("/refreshPic")
    public SingleResponse<?> refreshPic(@RequestBody RefreshPicForm form) {
        RefreshPicVO vo = weighDataService.getPicByTwoRecordId(form);
        return SingleResponse.of(vo);
    }

    @ApiOperation(value = "根据图片base64上传照片")
    @PostMapping("/upload/base64")
    public SingleResponse<?> uploadByBase64(@RequestBody List<Base64Form> form) {
        String collect = form.stream().map(e -> {
            try {
                return picUtil.getUrlByBase64(e.getBase64(), e.getType());
            } catch (FileNotFoundException ex) {
                throw new RuntimeException(ex);
            }
        }).collect(Collectors.joining(","));
        return SingleResponse.of(collect);
    }

    @ApiOperation(value = "根据2个recordId拿业务数据")
    @PostMapping("/getDataByRecordId")
    public SingleResponse<?> getDataByRecordId(@RequestBody List<String> recordIdList) {
        List<WeighDataSimpleVO> list = weighDataService.getWeighDataListByIdS(recordIdList);
        return SingleResponse.of(list);
    }

    @ApiOperation(value = "文件图片上传接口并返回表格HTML")
    @PostMapping("/ocr/upload/file")
    public SingleResponse<?> ocrUploadFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "文件为空");
        }
        // 构建请求参数
        String result = HttpRequest.post(materialOcrFileUpload)
                .form("file", PicUtil.getInputStream(file))
                .timeout(10000)
                .execute()
                .body();
        if (StrUtil.isNotBlank(result)) {
            JSONObject data = JSONUtil.parseObj(result).getJSONObject("data");
            return SingleResponse.of(data);
        }
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "base64图片上传接口并返回表格HTML")
    @PostMapping("/ocr/upload/base64")
    public SingleResponse<?> ocrUploadBase64(@RequestBody @Validated FileBase64Form file) {
        // 构建请求参数
        String result = HttpRequest.post(materialOcrFileUpload)
                .form("file", PicUtil.base64ToInputStreamResource(file.getBase64()))
                .timeout(10000)
                .execute()
                .body();
        if (StrUtil.isNotBlank(result)) {
            JSONObject data = JSONUtil.parseObj(result).getJSONObject("data");
            return SingleResponse.of(data);
        }
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "上报地磅报警数据帧")
    @PostMapping("/alarm/frame")
    public SingleResponse<?> alarmFrame(@RequestBody AlarmFrameForm form) {
        cheatAlarmLogService.saveAlarmFrame(form);
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "称重数据详情", responseReference = "SingleResponse«List<WeighDataPicVO>»", nickname = "commonWeighDataDetail")
    @GetMapping("/weighData/detail")
    public SingleResponse<WeighDataDetailVO> weighDataDetail(@RequestParam("recordId") String recordId) {
        return SingleResponse.of(weighDataService.detail(recordId));
    }

    @ApiOperation(value = "称重曲线", nickname = "weighCurveDetail")
    @GetMapping("/detail/{id}")
    public SingleResponse<WeighCurveVO> curveDetail(@PathVariable String id) {
        WeighCurveVO vo = weighCurveService.detail(id);
        return SingleResponse.of(vo);
    }

    @ApiOperation(value = "百度OCR识别接口", nickname = "baidu ocr识别接口")
    @PostMapping("/baidu/ocr")
    public SingleResponse<?> baiduOCR(@RequestBody BaiduOCRForm form) {
        String uid = form.getUid();
        String url = form.getUrl();

        // 查询配置
        UserExtConfigDO userExtConfigDO = userExtConfigService.lambdaQuery().eq(UserExtConfigDO::getUid, uid).one();
        if (userExtConfigDO != null && userExtConfigDO.getOcrEnable() == 1
                && StrUtil.isNotBlank(userExtConfigDO.getClientSecret())
                && StrUtil.isNotBlank(userExtConfigDO.getClientId())) {
            try {
                String accurate = baiduOCRUtil.accurate(url, uid, userExtConfigDO.getClientId(), userExtConfigDO.getClientSecret());
                //{"error_code":110,"error_msg":"Access token invalid or no longer valid"}
                if (accurate.contains("error_code")) {
                    JSONObject jsonObject = JSONUtil.parseObj(accurate);
                    return SingleResponse.buildFailure("-1", jsonObject.getStr("error_msg"));
                }
                // 记录日志
                appInvocationDailyLogService.saveInvokeLog(DeveloperAppEnum.BAIDU_OCR.value(), uid, -1L, 1);
                return SingleResponse.of(JSONUtil.parseObj(accurate));
            } catch (Exception e) {
                return SingleResponse.buildFailure("-1", "百度OCR识别接口异常");
            }
        }
        return SingleResponse.buildFailure("-1", "OCR未开启或配置错误");
    }

    @ApiOperation(value = "根据模板id获取必须识别字段列表")
    @GetMapping("/{templateId}/require/list")
    public SingleResponse<?> templateRequireList(@PathVariable Long templateId) {
        List<String> result = receiptModuleDetailConfigExtMapper.selectRequireList(templateId);
        return SingleResponse.of(result);
    }


}
