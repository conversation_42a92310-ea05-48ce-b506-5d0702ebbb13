package cn.pinming.microservice.material.client.management.controller.receiptrecycle;


import cn.pinming.material.v2.model.WeighDataAssemble;
import cn.pinming.material.v2.model.form.WeighDataAssembleForm;
import cn.pinming.microservice.material.client.management.common.enums.ReceiptFailTypeEnum;
import cn.pinming.microservice.material.client.management.infrastructure.response.SingleResponse;
import cn.pinming.microservice.material.client.management.common.form.ReceiptRecycleExportForm;
import cn.pinming.microservice.material.client.management.common.form.ReceiptRecycleForm;
import cn.pinming.microservice.material.client.management.common.form.ReceiptRecycleUpdateForm;
import cn.pinming.microservice.material.client.management.common.model.DeviceAttributionDO;
import cn.pinming.microservice.material.client.management.common.query.ReceiptRecycleQuery;
import cn.pinming.microservice.material.client.management.common.query.ReceiptRecycleResultQuery;
import cn.pinming.microservice.material.client.management.common.vo.KeyValueVO;
import cn.pinming.microservice.material.client.management.common.vo.ReceiptRecycleDetailsVO;
import cn.pinming.microservice.material.client.management.common.vo.ReceiptRecycleResultVO;
import cn.pinming.microservice.material.client.management.common.vo.ReceiptRecycleVO;
import cn.pinming.microservice.material.client.management.service.biz.ReceiptRecycleService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 单据回收
 *
 * <AUTHOR>
 * @since 2024-04-03
 */
@Api(value = "单据回收", tags = {"receiptRecycle"})
@RestController
@RequestMapping("/api/receiptRecycle")
public class ReceiptRecycleController {

    @Resource
    private ReceiptRecycleService receiptRecycleService;

    @ApiOperation(value = "回收日志列表", responseReference = "SingleResponse«IPage<WeighDataVO>»", nickname = "list")
    @PostMapping("/list")
    public SingleResponse<IPage<ReceiptRecycleVO>> pageList(@RequestBody ReceiptRecycleQuery query) {
        return SingleResponse.of(receiptRecycleService.pageList(query));
    }

    @ApiOperation(value = "回收单据详情", responseReference = "SingleResponse«ReceiptRecycleDetailsVO»", nickname = "details")
    @GetMapping("/details/{id}")
    public SingleResponse<ReceiptRecycleDetailsVO> details(@PathVariable("id") Long id) {
        return SingleResponse.of(receiptRecycleService.details(id));
    }

    @ApiOperation(value = "单据回收历史归属方", responseReference = "SingleResponse«ReceiptRecycleDetailsVO»", nickname = "receiptRecycleHistory")
    @GetMapping("/history")
    public SingleResponse<DeviceAttributionDO> history() {
        return SingleResponse.of(receiptRecycleService.history());
    }

    @ApiOperation(value = "高拍仪回收", responseReference = "SingleResponse«Boolean»", nickname = "cameraAdd")
    @PostMapping("/cameraAdd")
    public SingleResponse<Boolean> cameraAdd(@RequestBody ReceiptRecycleForm form) {
        receiptRecycleService.cameraAdd(form);
        return SingleResponse.of(true);
    }

    @ApiOperation(value = "单据回收匹配结果列表", responseReference = "SingleResponse«IPage<ReceiptRecycleResultVO>»", nickname = "resultList")
    @PostMapping("/result")
    public SingleResponse<IPage<ReceiptRecycleResultVO>> resultList(@RequestBody ReceiptRecycleResultQuery query) {
        IPage<ReceiptRecycleResultVO> result = receiptRecycleService.resultList(query);
        return SingleResponse.of(result);
    }

    @ApiOperation(value = "组装称重数据", responseReference = "SingleResponse«Boolean»", nickname = "assembleWeigh")
    @PostMapping("/assemble")
    public SingleResponse<WeighDataAssemble> assembleWeigh(@RequestBody WeighDataAssembleForm assembleForm) {
        return SingleResponse.of(receiptRecycleService.assembleWeigh(assembleForm));
    }

    @ApiOperation(value = "修正称重数据", responseReference = "SingleResponse«Boolean»", nickname = "correctionWeigh")
    @PostMapping("/correctionWeigh")
    public SingleResponse<Boolean> correctionWeigh(@RequestBody ReceiptRecycleUpdateForm form) {
        receiptRecycleService.correctionWeigh(form);
        return SingleResponse.of(true);
    }

    @ApiOperation(value = "组装模板数据", responseReference = "SingleResponse«Boolean»", nickname = "assembleModule")
    @GetMapping("/assembleModule/{id}")
    public SingleResponse<Boolean> assembleModule(@PathVariable("id") Long id) {
        receiptRecycleService.assembleModule(id);
        return SingleResponse.of(true);
    }

    @ApiOperation(value = "修正模板数据", responseReference = "SingleResponse«Boolean»", nickname = "correctionModule")
    @PostMapping("/correctionModule")
    public SingleResponse<Boolean> correctionModule(@RequestBody ReceiptRecycleUpdateForm form) {
        receiptRecycleService.correctionModule(form);
        return SingleResponse.of(true);
    }

    @ApiOperation(value = "选择模板", responseReference = "SingleResponse«Boolean»", nickname = "chooseModule")
    @PostMapping("/chooseModule")
    public SingleResponse<Boolean> chooseModule(@RequestBody ReceiptRecycleUpdateForm form) {
        receiptRecycleService.chooseModule(form);
        return SingleResponse.of(true);
    }

    @ApiOperation(value = "确认回收", responseReference = "SingleResponse«Boolean»", nickname = "successRecycle")
    @PostMapping("/success")
    public SingleResponse<Boolean> successRecycle(@RequestBody ReceiptRecycleForm form) {
        receiptRecycleService.successRecycle(form.getId());
        return SingleResponse.of(true);
    }

    @ApiOperation(value = "作废", responseReference = "SingleResponse«Boolean»", nickname = "invalidRecycle")
    @GetMapping("/invalid")
    public SingleResponse<Boolean> invalidRecycle(@RequestParam Long id) {
        receiptRecycleService.invalidRecycle(id);
        return SingleResponse.of(true);
    }

    @ApiOperation(value = "回收失败类型", responseReference = "SingleResponse«List<KeyValueVO>»", nickname = "recycleFailType")
    @GetMapping("/recycleFailType")
    public SingleResponse<List<KeyValueVO>> recycleFailType() {
        return SingleResponse.of(ReceiptFailTypeEnum.all());
    }

    @ApiOperation(value = "单据回收匹配结果导出", nickname = "resultExport")
    @PostMapping("/resultExport")
    public void resultExport(HttpServletResponse response,@RequestBody ReceiptRecycleExportForm form) {
        receiptRecycleService.resultExport(response, form);
    }

}

