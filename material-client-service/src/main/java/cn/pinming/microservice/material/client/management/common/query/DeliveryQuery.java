package cn.pinming.microservice.material.client.management.common.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2024/12/16 17:29
 */
@Data
public class DeliveryQuery {

    @NotBlank(message = "设备SN为空")
    @ApiModelProperty("设备SN")
    private String deviceSn;

    @NotBlank(message = "设备类型为空")
    @ApiModelProperty("设备类型")
    private String deviceType;

    @NotNull(message = "模式为空")
    @ApiModelProperty("模式")
    private Integer mode;

//    @NotBlank(message = "车牌为空")
    @ApiModelProperty("车牌")
    private String truckNo;
}
