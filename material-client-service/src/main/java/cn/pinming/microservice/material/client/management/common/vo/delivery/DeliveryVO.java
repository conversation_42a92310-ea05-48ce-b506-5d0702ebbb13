package cn.pinming.microservice.material.client.management.common.vo.delivery;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2024/8/22 17:18
 */
@Data
public class DeliveryVO {

    @ApiModelProperty("发货单ID")
    private Long id;

    @ApiModelProperty("发货单号")
    private String no;

    @ApiModelProperty("采购方")
    private String project;

    @ApiModelProperty("供应商")
    private String supplierName;

    @ApiModelProperty("账号名称")
    private String accountName;

    @ApiModelProperty("车牌号")
    private String truckNo;

    @ApiModelProperty("司机")
    private String driver;

    @ApiModelProperty("车载货物")
    private String materialCategory;

    @ApiModelProperty("推送状态 1 未推送 2 已推送 3 推送失败")
    private Integer pushState;

    @ApiModelProperty("1 在途，2 到场确认中，3 已到场，4 已作废")
    private Integer status;

    @ApiModelProperty("创建时间")
    private LocalDateTime gmtCreate;

}
