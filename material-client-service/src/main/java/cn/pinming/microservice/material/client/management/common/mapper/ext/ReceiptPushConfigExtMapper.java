package cn.pinming.microservice.material.client.management.common.mapper.ext;

import cn.pinming.microservice.material.client.management.common.model.ReceiptPushConfigDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ReceiptPushConfigExtMapper {

    /**
     * 查找SDK推送配置
     */
    List<ReceiptPushConfigDO> findSdkPushConfig();

    /**
     * 查找用户推送业务配置
     */
    ReceiptPushConfigDO findReceiptPushConfig(@Param("uid") String uid);
}
