package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.material.v2.util.UuidUtil;
import cn.pinming.microservice.material.client.management.common.form.*;
import cn.pinming.microservice.material.client.management.common.model.*;
import cn.pinming.microservice.material.client.management.email.message.*;
import cn.pinming.microservice.material.client.management.email.send.EmailSender;
import cn.pinming.microservice.material.client.management.common.enums.EmailCaptchaEnum;
import cn.pinming.microservice.material.client.management.common.enums.LoginTypeEnum;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizErrorException;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizExceptionMessageEnum;
import cn.pinming.microservice.material.client.management.infrastructure.util.AppSecretUtil;
import cn.pinming.microservice.material.client.management.infrastructure.util.StpKit;
import cn.pinming.microservice.material.client.management.infrastructure.util.UserIdUtil;
import cn.pinming.microservice.material.client.management.infrastructure.util.pwd.DynamicSalt;
import cn.pinming.microservice.material.client.management.infrastructure.util.pwd.PwdMd5Util;
import cn.pinming.microservice.material.client.management.proxy.FileServiceProxy;
import cn.pinming.microservice.material.client.management.common.dto.GraphCaptchaDTO;
import cn.pinming.microservice.material.client.management.common.mapper.DeviceAttributionMapper;
import cn.pinming.microservice.material.client.management.common.mapper.UserMapper;
import cn.pinming.microservice.material.client.management.common.vo.UserVO;
import cn.pinming.microservice.material.client.management.service.biz.DeveloperAppService;
import cn.pinming.microservice.material.client.management.service.biz.DeveloperService;
import cn.pinming.microservice.material.client.management.service.biz.UserConfigService;
import cn.pinming.microservice.material.client.management.service.biz.UserService;
import cn.pinming.springboot.starter.redis.util.RedisUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.code.kaptcha.impl.DefaultKaptcha;
import com.google.code.kaptcha.util.Config;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpSession;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Properties;
import java.util.Random;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/6/12
 */
@Slf4j
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, UserDO> implements UserService {

    @Resource
    private UserMapper userMapper;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private DeviceAttributionMapper deviceAttributionMapper;

    @Resource
    private DeveloperAppService developerAppService;

    @Resource
    private DeveloperService developerService;

    @Resource
    private UserConfigService userConfigService;

    @Value("${user_default_pic}")
    private String userDefaultPic;

    @Resource
    private UserIdUtil userIdUtil;

    @Resource
    private FileServiceProxy fileServiceProxy;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean register(UserRegisterForm registerForm) {
        String email = registerForm.getEmail();
        UserDO userByEmail = getUserByEmail(email);
        if (ObjectUtil.isNotNull(userByEmail)) {
            throw new BizErrorException(BizExceptionMessageEnum.EMAIL_REGISTERED_ERROR);
        }

        String userName = registerForm.getUserName();
        String password = registerForm.getPassword();
        // 生成盐
        String salt = UuidUtil.uuidWithoutConnector();
        // 重组盐
        String dynamicSalt = DynamicSalt.dynamicSaltExtract(salt);
        // 生成[密码+重组盐]加密值
        String passwordSalt = PwdMd5Util.md5PasswordAndSalt(password, dynamicSalt);
        // 保存
        String uid = UuidUtil.uuidWithoutConnector();
        UserDO userDO = new UserDO();
        userDO.setUid(uid);
        userDO.setCreateId(uid);
        userDO.setModifyId(uid);
        userDO.setUserName(userName);
        userDO.setEmail(email);
        userDO.setPassword(password);
        userDO.setPasswordSalt(passwordSalt);
        userDO.setSalt(salt);
        userDO.setAppKey(AppSecretUtil.generate(16, true));
        userDO.setAppSecretKey(AppSecretUtil.generate(32, true));
        userDO.setLogoPic(userDefaultPic);
        userMapper.insert(userDO);
        initAppService(uid);
        DeviceAttributionDO deviceAttributionDO = new DeviceAttributionDO();
        deviceAttributionDO.setUid(userDO.getUid());
        deviceAttributionDO.setName(DEFAULT_NAME);
        deviceAttributionDO.setCode(DEFAULT_CODE);
        return deviceAttributionMapper.insert(deviceAttributionDO) == 1;
    }

    public void initAppService(String uid) {
        List<DeveloperAppDO> list = developerAppService.list();
        List<DeveloperDO> developerList = list.stream().map(obj -> {
            DeveloperDO developerDO = new DeveloperDO();
            developerDO.setAppId(obj.getId());
            developerDO.setCreateId(uid);
            developerDO.setModifyId(uid);
            return developerDO;
        }).collect(Collectors.toList());
        developerService.saveBatch(developerList);

        //初始化s_user_config
        UserConfigDO userConfig = new UserConfigDO();
        userConfig.setUid(uid);
        LocalDateTime expireDateTime = LocalDateTime.now().withHour(23).withMinute(59).withSecond(59).plusYears(1);
        userConfig.setSpaceExpire(expireDateTime);
        userConfigService.save(userConfig);
    }

    @Override
    public String getDynamicSalt(String email) {
        UserDO userDO = getUserByEmail(email);
        if (ObjectUtil.isNull(userDO)) {
            throw new BizErrorException(BizExceptionMessageEnum.USER_NOT_FOUND_ERROR);
        }
        return userDO.getSalt();
    }

    @Override
    public String graphCaptcha(HttpSession session) {
        GraphCaptchaDTO captchaDTO;
        Random random = new Random();
        int randomNumber = random.nextInt(10);
        Object o = redisUtil.get(UserService.GRAPH_CAPTCHA_REDIS_PREFIX + randomNumber);
        if (ObjectUtil.isNull(o)) {
            Object o1 = redisUtil.get(UserService.GRAPH_CAPTCHA_REDIS_PREFIX + 0);
            if (ObjectUtil.isNull(o1)) {
                captchaDTO = captchaGenerator();
            } else {
                captchaDTO = (GraphCaptchaDTO) o1;
            }
            // 刷新缓存
            new Thread(this::captchaCache).start();
        } else {
            captchaDTO = (GraphCaptchaDTO) o;
        }
        // 存储
        session.setAttribute(UserService.GRAPH_CAPTCHA_SESSION_ATTRIBUTE, captchaDTO.getCaptchaText());
        return captchaDTO.getBase64Image();
    }

    @Override
    public Boolean emailCaptchaFree(String email, EmailCaptchaEnum captchaEnum) {
        UserDO userByEmail = getUserByEmail(email);
        String emailCaptcha = String.valueOf(ThreadLocalRandom.current().nextInt(100000, 1000000));
        switch (captchaEnum) {
            case LOGIN:
                if (ObjectUtil.isNull(userByEmail))
                    throw new BizErrorException(BizExceptionMessageEnum.USER_NOT_FOUND_ERROR);
                redisUtil.set(UserService.EMAIL_LOGIN_CAPTCHA_REDIS_PREFIX + email, emailCaptcha, 60 * 5);
                return EmailSender.sendEmail(email, new LoginCaptchaEmailMessage(emailCaptcha));
            case REGISTER:
                if (ObjectUtil.isNotNull(userByEmail))
                    throw new BizErrorException(BizExceptionMessageEnum.EMAIL_REGISTERED_ERROR);
                redisUtil.set(UserService.EMAIL_REGISTER_CAPTCHA_REDIS_PREFIX + email, emailCaptcha, 60 * 5);
                return EmailSender.sendEmail(email, new RegisterCaptchaEmailMessage(emailCaptcha));
        }
        return false;
    }


    @Override
    public Boolean emailCaptcha(EmailCaptchaEnum captchaEnum) {
        UserDO userDO = this.lambdaQuery().eq(UserDO::getUid, userIdUtil.getUId()).one();
        String email = userDO.getEmail();
        String emailCaptcha = String.valueOf(ThreadLocalRandom.current().nextInt(100000, 1000000));
        switch (captchaEnum) {
            case GENERATE:
                redisUtil.set(UserService.EMAIL_GENERATE_CAPTCHA_REDIS_PREFIX + email, emailCaptcha, 60 * 5);
                return EmailSender.sendEmail(email, new GenerateCaptchaEmailMessage(emailCaptcha));
            case DELETE_ATTRIBUTION:
                redisUtil.set(UserService.EMAIL_DELETE_ATTRIBUTION_CAPTCHA_REDIS_PREFIX + email, emailCaptcha, 60 * 5);
                return EmailSender.sendEmail(email, new DeleteAttributionCaptchaEmailMessage(emailCaptcha));
            case BIND_ATTRIBUTION:
                redisUtil.set(UserService.EMAIL_BIND_ATTRIBUTION_CAPTCHA_REDIS_PREFIX + email, emailCaptcha, 60 * 5);
                return EmailSender.sendEmail(email, new BindAttributionCaptchaEmailMessage(emailCaptcha));
            case DELETE_MODULE:
                redisUtil.set(UserService.EMAIL_DELETE_MODULE_REDIS_PREFIX + email, emailCaptcha, 60 * 5);
                return EmailSender.sendEmail(email, new OCRModuleCaptchaEmailMessage(emailCaptcha));
            case UPDATE_PASSWORD:
                redisUtil.set(UserService.EMAIL_UPDATE_PASSWORD_REDIS_PREFIX + email, emailCaptcha, 60 * 5);
                return EmailSender.sendEmail(email, new UpdatePasswordCaptchaEmailMessage(emailCaptcha));
            case UPDATE_EMAIL:
                redisUtil.set(UserService.EMAIL_UPDATE_EMAIL_REDIS_PREFIX + email, emailCaptcha, 60 * 5);
                return EmailSender.sendEmail(email, new UpdateEmailCaptchaEmailMessage(emailCaptcha));
        }
        return false;
    }

    @Override
    public String passwordLogin(PasswordLoginForm loginForm) {
        String password = loginForm.getPassword();
        UserDO userDO = getUserByEmail(loginForm.getEmail());
        if (ObjectUtil.isNull(userDO)) {
            throw new BizErrorException(BizExceptionMessageEnum.USER_NOT_FOUND_ERROR);
        }
        String passwordSalt = userDO.getPasswordSalt();
        if (StringUtils.isBlank(password) || !password.equals(passwordSalt)) {
            throw new BizErrorException(BizExceptionMessageEnum.PASSWORD_ERROR);
        }
        String uid = userDO.getUid();
        // 重新生成动态盐值并加密密码
        String salt = UuidUtil.uuidWithoutConnector();
        // 重组盐
        String dynamicSalt = DynamicSalt.dynamicSaltExtract(salt);
        // 生成[密码+重组盐]加密值
        String newPasswordSalt = PwdMd5Util.md5PasswordAndSalt(userDO.getPassword(), dynamicSalt);
        UserDO entity = new UserDO();
        entity.setId(userDO.getId());
        entity.setPasswordSalt(newPasswordSalt);
        entity.setSalt(salt);
        userMapper.updateById(entity);
        // 登录
        StpKit.DEFAULT.login(uid,"PC");
        // 返回token
        return StpKit.DEFAULT.getTokenValue();
    }

    @Override
    public String emailLogin(EmailLoginForm loginForm) {
        UserDO userDO = getUserByEmail(loginForm.getEmail());
        if (ObjectUtil.isNull(userDO)) {
            throw new BizErrorException(BizExceptionMessageEnum.USER_NOT_FOUND_ERROR);
        }
        // 登录
        StpKit.DEFAULT.login(userDO.getUid(),"PC");
        // 返回token
        return StpKit.DEFAULT.getTokenValue();
    }

    @Override
    public UserVO currentUser() {
        String loginId = userIdUtil.getUId();
        if (StringUtils.isBlank(loginId)) {
            return null;
        }
        UserVO userVO = null;

        String loginType = getLoginType();
        if (StrUtil.isBlank(loginType)) {
            return null;
        }
        QueryWrapper<UserDO> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(UserDO::getUid, loginId);
        UserDO userDO = userMapper.selectOne(wrapper);
        userVO = UserVO.builder()
                .userid(userDO.getUid())
                .userName(userDO.getUserName())
                .email(userDO.getEmail())
                .isAdmin(userDO.getIsAdmin())
                .logoPic(userDO.getLogoPic())
                .isFake(false)
                .build();
        if (loginType.equals(LoginTypeEnum.MANAGER.getStr())) {
            userVO.setIsFake(true);
        }

        return userVO;
    }

    /**
     * 多账号登录场景下 判断当前登录人信息
     *
     * @return
     */
    @Override
    public UserVO currentUserByType() {
        String loginId = userIdUtil.getUId();
        if (StringUtils.isBlank(loginId)) {
            return null;
        }
        UserVO userVO = null;

        String loginType = getLoginType();
        if (StrUtil.isBlank(loginType)) {
            return null;
        }
        QueryWrapper<UserDO> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(UserDO::getUid, loginId);
        UserDO userDO = userMapper.selectOne(wrapper);
        userVO = UserVO.builder()
                .userid(userDO.getUid())
                .userName(userDO.getUserName())
                .email(userDO.getEmail())
                .isAdmin(userDO.getIsAdmin())
                .logoPic(userDO.getLogoPic())
                .isFake(false)
                .build();
        if (loginType.equals(LoginTypeEnum.MANAGER.getStr())) {
            userVO.setIsFake(true);
        }

        return userVO;
    }

    private String getLoginType() {
        String loginType = null;
        try {
            StpKit.DEFAULT.getLoginId();
            loginType = StpKit.DEFAULT.getLoginType();
        } catch (Exception ignored) {
        }
        try {
            StpKit.MANAGER.getLoginId();
            loginType = StpKit.MANAGER.getLoginType();
        } catch (Exception ignored) {
        }

        return loginType;
    }

    @Override
    public void renew(UserUpdateForm form) {
        UserDO userDO = new UserDO();
        UserDO one = this.lambdaQuery()
                .eq(UserDO::getUid, form.getUId())
                .one();
        if (ObjectUtil.isNotNull(one)) {
            BeanUtil.copyProperties(form, userDO);
            userDO.setId(one.getId());
            if (StrUtil.isNotBlank(form.getBase64())) {
                String url = fileServiceProxy.uploadByBase64(form.getBase64());
                userDO.setLogoPic(url);
            }
            this.updateById(userDO);
        }
    }

    @Override
    public UserDO getByUid(String uid) {
        QueryWrapper<UserDO> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(UserDO::getUid, uid);
        return userMapper.selectOne(wrapper);
    }

    @Override
    public void updateUserPassword(UpdatePasswordForm form) {
        UserDO userDO = this.lambdaQuery().eq(UserDO::getUid, userIdUtil.getUId()).one();
        // 邮箱验证码
        String emailCacheKey = UserService.EMAIL_UPDATE_PASSWORD_REDIS_PREFIX + userDO.getEmail();
        String emailCaptchaCache = (String) redisUtil.get(emailCacheKey);
        if (StringUtils.isBlank(emailCaptchaCache)) {
            throw new BizErrorException(BizExceptionMessageEnum.EMAIL_CAPTCHA_INVALID_ERROR);
        }
        if (StringUtils.isBlank(form.getEmailCaptcha()) || !form.getEmailCaptcha().equals(emailCaptchaCache)) {
            throw new BizErrorException(BizExceptionMessageEnum.EMAIL_CAPTCHA_ERROR);
        }
        // 移除邮箱验证码缓存
        redisUtil.del(emailCacheKey);

        // 生成盐
        String salt = UuidUtil.uuidWithoutConnector();
        // 重组盐
        String dynamicSalt = DynamicSalt.dynamicSaltExtract(salt);
        // 生成[密码+重组盐]加密值
        String passwordSalt = PwdMd5Util.md5PasswordAndSalt(form.getPassword(), dynamicSalt);
        //更新密码
        this.lambdaUpdate().set(UserDO::getPassword, form.getPassword())
                .set(UserDO::getPasswordSalt, passwordSalt)
                .set(UserDO::getSalt, salt)
                .eq(UserDO::getId, userDO.getId())
                .update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUserEmail(UpdateEmailForm form) {
        UserDO userDO = this.lambdaQuery().eq(UserDO::getUid, userIdUtil.getUId()).one();
        if (ObjectUtil.isNull(userDO)) {
            throw new BizErrorException(BizExceptionMessageEnum.USER_NOT_FOUND_ERROR);
        }
        String currentEmail = userDO.getEmail();
        String newEmail = form.getEmail().toLowerCase().trim();

        // 检查新邮箱是否与当前邮箱相同
        if (newEmail.equalsIgnoreCase(currentEmail)) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "新邮箱不能与当前邮箱相同");
        }

        // 检查新邮箱是否已被其他用户使用
        UserDO existingUser = getUserByEmail(newEmail);
        if (ObjectUtil.isNotNull(existingUser)) {
            throw new BizErrorException(BizExceptionMessageEnum.EMAIL_REGISTERED_ERROR);
        }

        // 邮箱验证码验证
        String emailCacheKey = UserService.EMAIL_UPDATE_EMAIL_REDIS_PREFIX + currentEmail;
        String emailCaptchaCache = (String) redisUtil.get(emailCacheKey);
        if (StringUtils.isBlank(emailCaptchaCache)) {
            throw new BizErrorException(BizExceptionMessageEnum.EMAIL_CAPTCHA_INVALID_ERROR);
        }
        if (StringUtils.isBlank(form.getEmailCaptcha()) || !form.getEmailCaptcha().equals(emailCaptchaCache)) {
            throw new BizErrorException(BizExceptionMessageEnum.EMAIL_CAPTCHA_ERROR);
        }

        // 移除邮箱验证码缓存
        redisUtil.del(emailCacheKey);

        // 更新邮箱
        this.lambdaUpdate().set(UserDO::getEmail, newEmail)
                .eq(UserDO::getId, userDO.getId())
                .update();
    }

    @Override
    public UserDO getUserByAppKey(String appKey) {
        return lambdaQuery().eq(UserDO::getAppKey, appKey).oneOpt().orElseThrow(() -> new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "租户不存在"));
    }

    /**
     * 根据邮箱获取用户
     *
     * @param email
     * @return
     */
    private UserDO getUserByEmail(String email) {
        QueryWrapper<UserDO> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(UserDO::getEmail, email);
        return userMapper.selectOne(wrapper);
    }

    private GraphCaptchaDTO captchaGenerator() {
        // 创建 kaptcha 对象
        Properties properties = new Properties();
        properties.setProperty("kaptcha.background.clear.from", "217,249,223");
        properties.setProperty("kaptcha.background.clear.to", "217,249,223");
        properties.setProperty("kaptcha.textproducer.font.color", "1,214,113");
        properties.setProperty("kaptcha.border", "no");
        properties.setProperty("kaptcha.image.width", "140");
        properties.setProperty("kaptcha.image.height", "50");
        properties.setProperty("kaptcha.textproducer.char.string", "ABCDEF1234567890");
        Config config = new Config(properties);
        DefaultKaptcha kaptcha = new DefaultKaptcha();
        kaptcha.setConfig(config);

        // 生成验证码文本
        String captchaText = kaptcha.createText();

        // 生成验证码图像
        BufferedImage image = kaptcha.createImage(captchaText);

        // 将验证码图像转换为 Base64 字符串
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            ImageIO.write(image, "png", outputStream);
        } catch (Exception e) {
            log.error("验证码图像生成错误:{}", e.getMessage());
        } finally {
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (Exception e) {
                    log.error("验证码图像生成关闭流错误:{}", e.getMessage());
                }
            }
        }
        String base64Image = Base64.encode(outputStream.toByteArray());
        GraphCaptchaDTO graphCaptchaDTO = GraphCaptchaDTO.builder().captchaText(captchaText).base64Image(base64Image).build();
        return graphCaptchaDTO;
    }

    private void captchaCache() {
        // 生成10个图形验证码
        for (int i = 0; i < 10; i++) {
            GraphCaptchaDTO graphCaptchaDTO = captchaGenerator();
            if (i == 0) {
                // 不超时
                redisUtil.set(UserService.GRAPH_CAPTCHA_REDIS_PREFIX + i, graphCaptchaDTO);
            } else {
                redisUtil.set(UserService.GRAPH_CAPTCHA_REDIS_PREFIX + i, graphCaptchaDTO, 60 * 60);
            }
        }
    }

}
