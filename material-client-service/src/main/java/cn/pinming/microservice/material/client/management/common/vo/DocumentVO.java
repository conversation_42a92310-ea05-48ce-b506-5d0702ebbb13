package cn.pinming.microservice.material.client.management.common.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class DocumentVO {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("资源uuid")
    private String uuid;

    @ApiModelProperty("资源名称")
    private String name;

    @ApiModelProperty("资源说明")
    private String desc;

    @ApiModelProperty("资源类型")
    private String type;

    @ApiModelProperty("上传时间")
    private LocalDateTime time;

}
