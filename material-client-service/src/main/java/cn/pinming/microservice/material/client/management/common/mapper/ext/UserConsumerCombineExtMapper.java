package cn.pinming.microservice.material.client.management.common.mapper.ext;

import cn.pinming.microservice.material.client.management.common.model.UserConsumerCombineDO;
import cn.pinming.microservice.material.client.management.common.vo.ConsumerVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface UserConsumerCombineExtMapper {
    List<ConsumerVO> getInfoByPhoneSn(@Param("phoneSn") String phoneSn);

    UserConsumerCombineDO loginByPhoneSn(@Param("phoneSn") String phoneSn,@Param("uId")  String uId,@Param("attributionId")  Long attributionId);

    List<UserConsumerCombineDO> bindHistoryList(@Param("id") Long id);
}
