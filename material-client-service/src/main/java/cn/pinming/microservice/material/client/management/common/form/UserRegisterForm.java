package cn.pinming.microservice.material.client.management.common.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/6/12
 */
@Data
public class UserRegisterForm {

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("用户名")
    private String userName;

    @ApiModelProperty("密码(md5加密一次)")
    private String password;

    @ApiModelProperty("图形验证码")
    private String graphCaptcha;

    @ApiModelProperty("邮箱验证码")
    private String emailCaptcha;

}
