package cn.pinming.microservice.material.client.management.service.biz;

import cn.pinming.microservice.material.client.management.common.model.AppInvocationDailyLogDO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2023/8/7 16:27
 */
public interface AppInvocationDailyLogService extends IService<AppInvocationDailyLogDO> {
    void saveInvokeLog(Long appId, String uid,Long attributionId, long count);
}
