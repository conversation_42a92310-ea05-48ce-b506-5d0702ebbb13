package cn.pinming.microservice.material.client.management.common.mapper.ext;

import cn.pinming.microservice.material.client.management.common.vo.CheckMaterialConfirmVO;
import cn.pinming.microservice.material.client.management.common.vo.CheckMaterialReverseVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface CheckDetailExtMapper {
    List<CheckMaterialReverseVO> theoryCheckVO(@Param("id") Long id);

    List<CheckMaterialReverseVO> reverseCheckVO(@Param("id") Long id);

    List<CheckMaterialConfirmVO> confirmCheckVO(@Param("id")Long id);
}
