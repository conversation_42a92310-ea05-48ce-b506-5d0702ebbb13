package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 公共推送路由配置
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("d_push_route_config")
public class PushRouteConfigDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 请求方式
     */
    private String method;



    /**
     * 接口地址
     */
    private String endPoint;

    /**
     * 描述
     */
    private String description;


}
