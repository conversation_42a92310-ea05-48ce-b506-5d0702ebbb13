package cn.pinming.microservice.material.client.management.controller.ocr;

import cn.pinming.microservice.material.client.management.common.form.OCRDictForm;
import cn.pinming.microservice.material.client.management.common.query.OCRDictQuery;
import cn.pinming.microservice.material.client.management.common.vo.OCRDictVO;
import cn.pinming.microservice.material.client.management.infrastructure.response.SingleResponse;
import cn.pinming.microservice.material.client.management.service.biz.IOcrDictService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Api(value = "OCR字典-controller", tags = {"OCR字典"})
@RestController
@RequestMapping("/api/OCR-dict")
public class OCRDictController {

    @Resource
    private IOcrDictService ocrDictService;

    @ApiOperation(value = "新增", responseReference = "SingleResponse«?»", nickname = "addOCRDict")
    @PostMapping("/add")
    public SingleResponse<?> addOCRDict(@RequestBody OCRDictForm form) {
        ocrDictService.saveDict(form);
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "编辑", responseReference = "SingleResponse«Boolean»", nickname = "updateOCRDict")
    @PostMapping("/update")
    public SingleResponse<Boolean> updateOCRDict(@RequestBody OCRDictForm form) {
        ocrDictService.updateDict(form);
        return SingleResponse.of(true);
    }

    @ApiOperation(value = "删除", responseReference = "SingleResponse«Boolean»", nickname = "deleteOCRDict")
    @DeleteMapping("/{id}")
    public SingleResponse<Boolean> delete(@PathVariable Long id) {
        ocrDictService.deleteDict(id);
        return SingleResponse.of(true);
    }

    @ApiOperation(value = "模版列表", responseReference = "SingleResponse«Page<OCRDictVO>»", nickname = "pageOCRDict")
    @PostMapping("/page")
    public SingleResponse<?> page(@RequestBody OCRDictQuery query) {
        Page<OCRDictVO> result = ocrDictService.pageByQuery(query);
        return SingleResponse.of(result);
    }

    @ApiOperation(value = "模版列表", responseReference = "SingleResponse«List<OCRDictVO>»", nickname = "listOCRDict")
    @PostMapping("/list")
    public SingleResponse<?> list(@RequestBody OCRDictQuery query) {
        query.setSize(Integer.MAX_VALUE);
        Page<OCRDictVO> result = ocrDictService.pageByQuery(query);
        return SingleResponse.of(result.getRecords());
    }


}
