package cn.pinming.microservice.material.client.management.common.enums;

import java.util.Objects;

public enum DeliveryStatusEnum {
    ONE(1, "在途"),
    TWO(2, "到场确认中"),
    THREE(3, "已到场"),
    FOUR(4, "已作废"),
    FIVE(5, "到场确认中-待闭合"),
    ;

    private final Integer type;
    private final String description;

    DeliveryStatusEnum(Integer type, String description) {
        this.type = type;
        this.description = description;
    }

    public Integer value() {
        return type;
    }

    public String description() {
        return description;
    }

    public static String descByType(Integer type) {
        for (DeliveryStatusEnum value : DeliveryStatusEnum.values()) {
            if (Objects.equals(value.type, type)) {
                return value.description;
            }
        }
        return null;
    }
}
