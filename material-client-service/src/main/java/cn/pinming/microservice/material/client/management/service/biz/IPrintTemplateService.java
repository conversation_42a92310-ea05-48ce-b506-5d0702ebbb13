package cn.pinming.microservice.material.client.management.service.biz;

import cn.pinming.microservice.material.client.management.common.form.PrintTemplateConfigForm;
import cn.pinming.microservice.material.client.management.common.form.supplier.PrintTemplateContentForm;
import cn.pinming.microservice.material.client.management.common.form.supplier.PrintTemplateForm;
import cn.pinming.microservice.material.client.management.common.model.PrintTemplateDO;
import cn.pinming.microservice.material.client.management.common.vo.TemplateParamVO;
import cn.pinming.microservice.material.client.management.common.vo.print.PrintPreviewVO;
import cn.pinming.microservice.material.client.management.common.vo.print.PrintTemplateConfigVO;
import cn.pinming.microservice.material.client.management.common.vo.print.PrintTemplateContentVO;
import cn.pinming.microservice.material.client.management.common.vo.print.PrintTemplateVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 打印模板设置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-20
 */
public interface IPrintTemplateService extends IService<PrintTemplateDO> {

    void saveByForm(PrintTemplateForm form);

    void saveContentByForm(PrintTemplateContentForm form);

    PrintTemplateContentVO queryPrintTemplateById(Long id);

    void enableById(Long id);

    List<PrintTemplateVO> listPrintTemplate(Byte type);

    List<PrintTemplateVO> listPrintTemplate(Byte type, Byte style, Byte formType);

    PrintTemplateConfigVO queryPrintTemplateConfigById(Long id);

    PrintPreviewVO preview(Long templateId, Long bizId);

    List<TemplateParamVO> templateParams(Byte type);

    void modifyTemplateConfig(PrintTemplateConfigForm form);

    void deleteById(Long id);
}
