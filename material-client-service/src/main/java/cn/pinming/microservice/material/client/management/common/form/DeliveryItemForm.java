package cn.pinming.microservice.material.client.management.common.form;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class DeliveryItemForm {

    @ApiModelProperty(value = "订单明细id")
    private Long orderDetailId;

    @ApiModelProperty(value = "发货数量")
    @NotNull(message = "发货数量不能为空")
    @DecimalMin(value = "0", message = "发货数量须大于0", inclusive = false)
    private BigDecimal amount;


}
