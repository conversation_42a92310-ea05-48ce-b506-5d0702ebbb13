package cn.pinming.microservice.material.client.management.infrastructure.exception;

import cn.dev33.satoken.exception.NotLoginException;
import cn.pinming.core.common.exception.CommonExceptionEnum;
import cn.pinming.core.common.exception.ErrorView;
import cn.pinming.material.v2.exception.MaterialException;
import cn.pinming.material.v2.exception.MaterialExceptionMessage;
import cn.pinming.microservice.material.client.management.infrastructure.response.SingleResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.rpc.RpcException;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.validation.ValidationException;
import java.util.Set;

/**
 * <AUTHOR>
 * @description
 */
@Slf4j
@ResponseBody
@ControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(NotLoginException.class)
    @ResponseStatus(value = HttpStatus.INTERNAL_SERVER_ERROR)
    public SingleResponse handleNotLoginError() {
        String errorMessage = BizExceptionMessageEnum.NOT_LOGIN_ERROR.errorMessage();
//        log.error(errorMessage);
        return SingleResponse.buildFailure(BizExceptionMessageEnum.NOT_LOGIN_ERROR.errorCode(), errorMessage);
    }

    @ExceptionHandler(BizErrorException.class)
    @ResponseStatus(value = HttpStatus.OK)
    public SingleResponse handleBizError(BizErrorException bizErrorException) {
        String errorCode = bizErrorException.errorCode();
        String errorMessage = bizErrorException.errorMessage();
//        log.error(errorMessage);
        return SingleResponse.buildFailure(errorCode, errorMessage);
    }

    @ExceptionHandler(MaterialException.class)
    @ResponseStatus(value = HttpStatus.INTERNAL_SERVER_ERROR)
    public SingleResponse handleMaterialError(MaterialException materialException) {
        String errorCode = materialException.errorCode();
        String errorMessage = materialException.errorMessage();
//        log.error(errorMessage);
        return SingleResponse.buildFailure(errorCode, errorMessage);
    }

    @ExceptionHandler(ValidationException.class)
    @ResponseStatus(value = HttpStatus.INTERNAL_SERVER_ERROR)
    public SingleResponse handleValidationError(ValidationException validationException) {
        Throwable cause = validationException.getCause();
        if (cause instanceof MaterialException) {
            MaterialException materialException = (MaterialException) cause;
            String errorCode = materialException.errorCode();
            String errorMessage = materialException.errorMessage();
//            log.error(errorMessage);
            return SingleResponse.buildFailure(errorCode, errorMessage);
        }
        throw validationException;
    }


    @ExceptionHandler(ConstraintViolationException.class)
    public SingleResponse constraintViolationException(ConstraintViolationException e) {
        if (log.isInfoEnabled()) {
            log.info(e.getMessage(), e);
        }
        String msg;
        Set<ConstraintViolation<?>> constraintViolations = e.getConstraintViolations();
        if (!constraintViolations.isEmpty()) {
            msg = constraintViolations.iterator().next().getMessage();
        } else {
            msg = e.getMessage();
        }
        return SingleResponse.buildFailure(CommonExceptionEnum.PARAM_VALIDATE_ERROR.getErrorCode(), msg);
    }

    @ExceptionHandler(RuntimeException.class)
    public SingleResponse runtimeException(RuntimeException e) {
        e.printStackTrace();
        if (log.isErrorEnabled()) {
            log.error(e.getMessage(), e);
        }
        String errorCode, errorMsg;
        if (e instanceof ErrorView) {
            errorCode = ((ErrorView) e).getErrorCode();
            errorMsg = ((ErrorView) e).getErrorMsg();
        } else {
            errorCode = CommonExceptionEnum.SYS_ERROR.getErrorCode();
            errorMsg = CommonExceptionEnum.SYS_ERROR.getErrorMsg();
        }
        return SingleResponse.buildFailure(errorCode, errorMsg);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public SingleResponse methodArgumentNotValidExceptionHandler(MethodArgumentNotValidException e) {
//        log.error("POST参数异常。", e);
        String defaultMessage = e.getBindingResult().getFieldError().getDefaultMessage();
        return SingleResponse.buildFailure("-200", defaultMessage);
    }

    @ExceptionHandler(BindException.class)
    public SingleResponse bindExceptionHandler(BindException e) {
//        log.error("GET参数异常。", e);
        String defaultMessage = e.getBindingResult().getFieldError().getDefaultMessage();
        return SingleResponse.buildFailure("-200", defaultMessage);
    }

    @ExceptionHandler({RpcException.class})
    public SingleResponse rpcException(RpcException e) {
        if (log.isInfoEnabled()) {
            log.info(e.getMessage(), e);
        }
        return SingleResponse.buildFailure("-200", e.getMessage());
    }

    @ExceptionHandler(Exception.class)
    @ResponseStatus(value = HttpStatus.INTERNAL_SERVER_ERROR)
    public SingleResponse handleError(Exception ex) {
        ex.printStackTrace();
//        log.error(ex.getMessage());
        return SingleResponse.buildFailure(MaterialExceptionMessage.SYSTEM_ERROR.errorCode(), ex.getLocalizedMessage());
    }


}

