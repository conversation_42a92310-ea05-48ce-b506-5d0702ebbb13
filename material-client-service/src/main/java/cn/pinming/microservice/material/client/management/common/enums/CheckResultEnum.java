package cn.pinming.microservice.material.client.management.common.enums;

public enum CheckResultEnum {
    YES(1, "合格"),
    NO(2, "不合格"),
    ;

    private int type;
    private String description;

    CheckResultEnum(int type, String description) {
        this.type = type;
        this.description = description;
    }

    public int value() {
        return type;
    }

    public String description() {
        return description;
    }

    public static String desc(int type) {
        for (CheckResultEnum value : CheckResultEnum.values()) {
            if (value.type == type) {
                return value.description;
            }
        }
        return "";
    }
}
