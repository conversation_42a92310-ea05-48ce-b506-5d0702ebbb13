package cn.pinming.microservice.material.client.management.common.vo.selfcheck;


import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class CargoVO {

    @ApiModelProperty("基石平台上为此货物生成的唯一ID，不可以为空")
    @JsonProperty("jsCargoId")
    private Long id;

    @ApiModelProperty("运单id")
    private Long deliveryId;

    @ApiModelProperty("外部系统明细ID")
    private String detailId;

    @ApiModelProperty("业务系统中的货物唯一ID，可以为空，但强烈建议设置")
    private String cargoId;

    @ApiModelProperty("货物名称，不能为空")
    @JsonProperty("cargoName")
    private String name;

    @ApiModelProperty("货物规格，不能为空")
    @JsonProperty("cargoModel")
    private String spec;

    @ApiModelProperty("货物其它参数，可为空")
    @JsonProperty("cargoParameter")
    private String argument;

    @ApiModelProperty("货物品牌，可为空")
    @JsonProperty("cargoBrand")
    private String brand;

    @ApiModelProperty("订单明细采购数量，不能为空，数字类型")
    @JsonProperty("waybillCounts")
    private BigDecimal amount;

    @ApiModelProperty("订单采购数量单位")
    @JsonProperty("waybillUnit")
    private String unit;

    @ApiModelProperty("货物将被使用去处的ID，比如WBS元素的ID；可以为空，但强烈建议设置")
    private String cargoUsageId;

    @ApiModelProperty("货物将被使用去处的名称，可以为空，但是强烈建议设置")
    @JsonProperty("cargoUsage")
    private String position;

    @ApiModelProperty("货物备注，可为空")
    private String remark;

    @ApiModelProperty("扣水扣杂比例，取值范围为0-1，不能为空")
    private BigDecimal deductRatio;

    @ApiModelProperty("扣重")
    private BigDecimal deductWeight;

    @ApiModelProperty("该货物的称重换算系数，如2.334意思为1立方米 = 2.334吨，不能为空，数字类型")
    private BigDecimal scaleFactor;

    @ApiModelProperty("用于设置该货物称重换算系数时，参照的重量单位：0 = 千克；1 = 吨，不能为空")
    @JsonProperty("weightUnitForReference")
    private Integer unitType;

    @ApiModelProperty("运单明细 1 在途，2 待确认，3 自动确认中，4 已确认，5 已作废")
    private Integer status;

}
