package cn.pinming.microservice.material.client.management.common.form;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.Future;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 *
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2023/8/7 17:36
 */
@Data
public class UserSpaceForm {

    @NotNull(message = "主键id为空")
    private Long id;

    /**
     * 购买空间， 单位GB
     */
    @NotNull(message = "请选择存储空间")
//    @Digits(integer = 10, fraction = 4, message = "Amount must have at most 10 digits in total, with 4 decimal places")
    private BigDecimal spaceSize;

    /**
     * 购买空间到期时间
     */
    @NotNull(message = "有效期限为空")
    @Future(message = "请选择将来的日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime spaceExpire;

}
