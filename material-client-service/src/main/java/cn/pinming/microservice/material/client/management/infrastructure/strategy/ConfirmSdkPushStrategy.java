package cn.pinming.microservice.material.client.management.infrastructure.strategy;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import cn.pinming.material.v2.model.dto.ConfirmResDTO;
import cn.pinming.microservice.material.client.management.common.BaseDO;
import cn.pinming.microservice.material.client.management.common.dto.push.PushConfirmDTO;
import cn.pinming.microservice.material.client.management.common.enums.SdkPushTypeEnum;
import cn.pinming.microservice.material.client.management.common.mapper.ext.WeighDataConfirmExtMapper;
import cn.pinming.microservice.material.client.management.common.model.WeighDataConfirmDO;
import cn.pinming.microservice.material.client.management.common.vo.push.PushRouteConfigVO;
import cn.pinming.microservice.material.client.management.infrastructure.util.ConfirmDataPushUtil;
import cn.pinming.microservice.material.client.management.service.biz.DeviceAttributionService;
import cn.pinming.microservice.material.client.management.service.biz.IWeighDataConfirmService;
import cn.pinming.microservice.material.client.management.service.push.sdk.dto.SdkRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2024/9/9 17:19
 */
@Slf4j
@Component("CONFIRM")
public class ConfirmSdkPushStrategy extends AbstractSkdPushStrategy<PushConfirmDTO> {
    @Resource
    private ConfirmDataPushUtil confirmDataPushUtil;
    @Resource
    private IWeighDataConfirmService weighDataConfirmService;
    @Resource
    private WeighDataConfirmExtMapper weighDataConfirmExtMapper;
    @Resource
    private DeviceAttributionService attributionService;

    @Override
    Long getRouteConfigId() {
        return SdkPushTypeEnum.CONFIRM.getVal();
    }

    /**
     * 查询数据.
     *
     * @param config
     */
    @Override
    Map<String, List<PushConfirmDTO>> queryDataListMap(PushRouteConfigVO config, Map<Long, String> attributionCodeMap) {
        Map<String, List<PushConfirmDTO>> result = new HashMap<>();
        //查询归属方
        String uid = config.getUid();
//        log.error("确认单 查询归属方：{}", uid);
        Set<Long> attributionCodeList = attributionCodeMap.keySet();
        List<String> supplierList = getSupplierList(uid, false, true);
//        log.error("确认单 查询供应商：{}", supplierList.size());
        // 查询确认单
        List<WeighDataConfirmDO> list = weighDataConfirmExtMapper.selectConfirmToPush(uid, attributionCodeList, supplierList);
//        log.error("确认单-确认单数量：{}", list.size());
        if (CollUtil.isNotEmpty(list)) {
            Map<Long, List<WeighDataConfirmDO>> attributionMap = list.stream().collect(Collectors.groupingBy(WeighDataConfirmDO::getAttributionId));
//            log.error("确认单 查询结果归属方code：{}", JSONUtil.toJsonStr(attributionMap.keySet()));
//            log.error("确认单 查询归属方code：{}", JSONUtil.toJsonStr(attributionCodeList));
            attributionMap.forEach((id, v) -> {
                if (attributionCodeMap.containsKey(id)) {
                    List<Long> ids = v.stream().map(BaseDO::getId).collect(Collectors.toList());
//                    log.error("归属方id:{},确认单推送ids：{}", uid, JSONUtil.toJsonStr(ids));
                    List<PushConfirmDTO> confirmList = confirmDataPushUtil.getConfirmPushData(ids);
//                    log.error("归属方id:{},code:{}, 推送数据：{}", uid, attributionCodeMap.get(id), confirmList.size());
                    result.put(attributionCodeMap.get(id), confirmList);
                }
            });
        }
        return result;
    }

    @Override
    void afterCompletion(SdkRespDTO result, List<PushConfirmDTO> pushedData) {
        if (CollUtil.isNotEmpty(pushedData) && CollUtil.isNotEmpty(result.getData())) {
            try {
                Set<Long> pushedIds = pushedData.stream()
                        .map(PushConfirmDTO::getId)
                        .collect(Collectors.toSet());

                List<ConfirmResDTO> confirmedList = JSONUtil.toList(result.getData().toString(), ConfirmResDTO.class);
                Set<Long> confirmedIds = confirmedList.stream()
                        .map(ConfirmResDTO::getId)
                        .collect(Collectors.toSet());

                Set<Long> validIds = pushedIds.stream()
                        .filter(confirmedIds::contains)
                        .collect(Collectors.toSet());

                if (CollUtil.isNotEmpty(validIds)) {
                    log.info("[确认数据推送成功] 推送数量: {}, 确认数量: {}, 有效更新数量: {}, 有效IDs: {}",
                            pushedIds.size(), confirmedIds.size(), validIds.size(), validIds);

                    weighDataConfirmService.lambdaUpdate()
                            .in(WeighDataConfirmDO::getId, validIds)
                            .set(WeighDataConfirmDO::getPushState, 2)
                            .update();
                } else {
                    log.warn("[确认数据推送异常] 推送的数据与确认的数据无交集, 推送IDs: {}, 确认IDs: {}", pushedIds, confirmedIds);
                }
            } catch (Exception e) {
                log.error("[确认数据推送异常] 处理返回数据失败", e);
            }
        }
    }
}
