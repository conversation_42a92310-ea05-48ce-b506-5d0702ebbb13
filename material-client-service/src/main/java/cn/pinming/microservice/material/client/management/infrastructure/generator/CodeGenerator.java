package cn.pinming.microservice.material.client.management.infrastructure.generator;

import com.baomidou.mybatisplus.core.exceptions.MybatisPlusException;
import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.config.*;
import com.baomidou.mybatisplus.generator.config.converts.MySqlTypeConvert;
import com.baomidou.mybatisplus.generator.config.rules.DbColumnType;
import com.baomidou.mybatisplus.generator.config.rules.IColumnType;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.engine.VelocityTemplateEngine;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;
import java.util.Scanner;

/**
 * <AUTHOR>
 */
public class CodeGenerator {

    private static final String MYSQL_TINYINT_BOOLEAN = "mysql.tinyint.boolean";
    private static final String MYSQL_TINYINT_TYPE = "tinyint(1)";

    /**
     * <p>
     * 读取控制台内容
     * </p>
     */
    public static String scanner(String tip) {
        Scanner scanner = new Scanner(System.in);
        System.out.println("请输入" + tip + "：");
        if (scanner.hasNext()) {
            String ipt = scanner.next();
            if (StringUtils.isNotBlank(ipt)) {
                return ipt;
            }
        }
        throw new MybatisPlusException("请输入正确的" + tip + "！");
    }

    public static void main(String[] args) {
        String canonicalName = CodeGenerator.class.getCanonicalName().replaceFirst(".infrastructure.generator.CodeGenerator", "");
        Properties jdbcProperties = new Properties();
        InputStream is = Thread.currentThread().getContextClassLoader().getResourceAsStream("jdbc.properties");
        try {
            jdbcProperties.load(is);
        } catch (IOException e) {
            throw new MybatisPlusException("load jdbc.properties error.");
        }

        // 代码生成器
        AutoGenerator mpg = new AutoGenerator();

        // 全局配置
        GlobalConfig gc = new GlobalConfig();
        String projectPath = System.getProperty("user.dir");
        gc.setOutputDir(projectPath + "/material-client-service/src/main/java/");
        gc.setAuthor("pms");
        gc.setOpen(false);
        gc.setEntityName("%sDO");
        gc.setFileOverride(true);
        mpg.setGlobalConfig(gc);

        // 数据源配置
        DataSourceConfig dsc = new DataSourceConfig();
        dsc.setUrl(jdbcProperties.getProperty("mysql.url"));
        dsc.setDriverName(jdbcProperties.getProperty("mysql.driver.name"));
        dsc.setUsername(jdbcProperties.getProperty("mysql.username"));
        dsc.setPassword(jdbcProperties.getProperty("mysql.password"));
        if (!Boolean.parseBoolean(jdbcProperties.getProperty(MYSQL_TINYINT_BOOLEAN))) {
            dsc.setTypeConvert(new MySqlTypeConvert() {
                @Override
                public IColumnType processTypeConvert(GlobalConfig config, String fieldType) {
                    String t = fieldType.toLowerCase();
                    if (t.contains(MYSQL_TINYINT_TYPE)) {
                        return DbColumnType.INTEGER;
                    }
                    return super.processTypeConvert(config, fieldType);
                }
            });
        }
        mpg.setDataSource(dsc);

        // 包配置
        PackageConfig pc = new PackageConfig();
        pc.setParent(canonicalName);
        pc.setModuleName("repository");
        pc.setEntity("model");
        pc.setMapper("mapper");
        mpg.setPackageInfo(pc);

        // 配置模板
        TemplateConfig templateConfig = new TemplateConfig();
//        templateConfig.setXml(null);
//        templateConfig.setService(null);
//        templateConfig.setServiceImpl(null);
//        templateConfig.setController(null);
//        templateConfig.setMapper(null);
        mpg.setTemplate(templateConfig);

        // 策略配置
        StrategyConfig strategy = new StrategyConfig();
        strategy.setTablePrefix("m_", "s_", "op_", "f_", "d_", "n_", "a_", "b_", "hn_", "hb_", "r_");
        strategy.setNaming(NamingStrategy.underline_to_camel);
        strategy.setColumnNaming(NamingStrategy.underline_to_camel);
        strategy.setSuperEntityClass(canonicalName + ".repository.BaseDO");
        strategy.setEntityLombokModel(true);
        strategy.setRestControllerStyle(true);
        // 父类中的公共字段
        strategy.setSuperEntityColumns("id", "gmt_create", "create_id", "gmt_modify", "modify_id", "deleted");
        strategy.setInclude(scanner("表名，多个英文逗号分割").split(","));
        strategy.setControllerMappingHyphenStyle(true);
        strategy.setTablePrefix(pc.getModuleName() + "_");
        mpg.setStrategy(strategy);
        mpg.setTemplateEngine(new VelocityTemplateEngine());
        mpg.execute();
    }

}
