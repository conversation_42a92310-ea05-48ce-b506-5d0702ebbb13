package cn.pinming.microservice.material.client.management.infrastructure.task;

import cn.hutool.core.collection.CollUtil;
import cn.pinming.microservice.material.client.management.common.enums.RecycleStatusEnum;
import cn.pinming.microservice.material.client.management.common.mapper.ext.ReceiptPushConfigExtMapper;
import cn.pinming.microservice.material.client.management.common.model.DeviceAttributionDO;
import cn.pinming.microservice.material.client.management.common.model.ReceiptPushConfigDO;
import cn.pinming.microservice.material.client.management.common.model.ReceiptRecycleDO;
import cn.pinming.microservice.material.client.management.service.biz.DeviceAttributionService;
import cn.pinming.microservice.material.client.management.service.biz.IWeighDataConfirmService;
import cn.pinming.microservice.material.client.management.service.biz.ReceiptRecycleService;
import cn.pinming.microservice.material.client.management.service.business.ReceiptRecyclePushBusinessService;
import cn.pinming.microservice.material.client.management.service.business.RecycleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 单据回收定时任务
 */
@Component
@Slf4j
public class ReceiptRecycleTask {

    @Resource
    private ReceiptRecycleService receiptRecycleService;
    @Resource
    private ReceiptPushConfigExtMapper receiptPushConfigExtMapper;
    @Resource
    private DeviceAttributionService deviceAttributionService;
    @Resource
    private ReceiptRecyclePushBusinessService receiptRecyclePushBusinessService;
    @Resource
    private RecycleService recycleService;
    @Value("${receiptRecycleTask.isEnable:false}")
    private boolean isEnable;
    @Value("${push.ocr.enable:false}")
    private boolean ocrEnable;
    @Resource
    private IWeighDataConfirmService weighDataConfirmService;

    /**
     * 处理待回收单据任务
     * 每5分钟执行一次
     */
    @Scheduled(cron = "0 0/5 * * * ?")
    public void processWaitRecycleTask() {
        if (!isEnable) {
            return;
        }
        log.info("处理待回收单据任务开始");
        List<ReceiptRecycleDO> list = receiptRecycleService.lambdaQuery().eq(ReceiptRecycleDO::getRecycleStatus, RecycleStatusEnum.WAIT.getValue())
                .isNotNull(ReceiptRecycleDO::getRecyclePic).isNull(ReceiptRecycleDO::getLocalId)
                .orderByDesc(ReceiptRecycleDO::getGmtCreate).last("limit 100").list();
        if (CollUtil.isNotEmpty(list)) {
            for (ReceiptRecycleDO receiptRecycleDO : list) {
                recycleService.syncProcessRecycle(receiptRecycleDO);
            }
        }
        log.info("处理待回收单据任务结束");
    }

    /**
     * 每整点30分钟时执行一次 推送数据
     */
    @Scheduled(cron = "0/30 * * * * ? ")
    public void pushData() {
        if (!isEnable) {
            return;
        }
        log.info("单据回收数据推送定时任务start");
        // 需推送租户配置
        List<ReceiptPushConfigDO> sdkPushConfigList = receiptPushConfigExtMapper.findSdkPushConfig();
        if (CollUtil.isEmpty(sdkPushConfigList)) {
            return;
        }
        // 数据需推送归属方
        List<ReceiptRecyclePushBusinessService.ReceiptPushConfigDTO> dataNeedPushAttributions = findNeedPushAttributions(sdkPushConfigList);
        if (CollUtil.isEmpty(dataNeedPushAttributions)) {
            return;
        }
        for (ReceiptRecyclePushBusinessService.ReceiptPushConfigDTO receiptPushConfigDTO : dataNeedPushAttributions) {
            receiptRecyclePushBusinessService.pushReceiptRecycleList(receiptPushConfigDTO);//推送租户单据回收数据
        }
    }



    /**
     * 需要推送数据方
     *
     * @return list
     */
    private List<ReceiptRecyclePushBusinessService.ReceiptPushConfigDTO> findNeedPushAttributions(List<ReceiptPushConfigDO> sdkPushConfigList) {
        List<String> users = sdkPushConfigList.stream().map(ReceiptPushConfigDO::getUid).distinct().collect(Collectors.toList());
        List<DeviceAttributionDO> allAttributionList = deviceAttributionService.lambdaQuery().in(DeviceAttributionDO::getUid, users).list();
        if (CollUtil.isEmpty(allAttributionList)) {
            return null;
        }
        Map<String, List<Long>> attributionMap = allAttributionList.stream().collect(Collectors.groupingBy(DeviceAttributionDO::getUid, Collectors.mapping(DeviceAttributionDO::getId, Collectors.toList())));
        //过滤数据
        List<ReceiptRecyclePushBusinessService.ReceiptPushConfigDTO> list = new ArrayList<>();
        sdkPushConfigList.forEach(userBusinessConfigDO -> {
            List<Long> attributionList = attributionMap.get(userBusinessConfigDO.getUid());
            if (CollUtil.isEmpty(attributionList)) {
                return;
            }
            ReceiptRecyclePushBusinessService.ReceiptPushConfigDTO userBusinessConfigDTO = receiptRecyclePushBusinessService.buildReceiptPushConfigDTO(userBusinessConfigDO, attributionList);
            if (userBusinessConfigDTO == null) {
                return;
            }
            list.add(userBusinessConfigDTO);
        });
        return list;
    }

    /**
     * 每两分钟执行一次，确认单单据回收
     */
    @Scheduled(cron = "0/30 * * * * ? ")
    public void confirmRecycle() {
        if (!ocrEnable) {
            return;
        }
        log.info("确认单据回收ocr开始");
        weighDataConfirmService.confirmRecycle();
        log.info("确认单据回收ocr结束");
    }
}
