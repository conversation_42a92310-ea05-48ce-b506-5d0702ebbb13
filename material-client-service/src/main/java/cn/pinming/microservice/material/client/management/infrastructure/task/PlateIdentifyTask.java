package cn.pinming.microservice.material.client.management.infrastructure.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.microservice.material.client.management.common.dto.AnprDTO;
import cn.pinming.microservice.material.client.management.common.dto.PlateIdentifyDTO;
import cn.pinming.microservice.material.client.management.common.enums.RiskGradeEnum;
import cn.pinming.microservice.material.client.management.common.mapper.ext.PlateIdentifyExtMapper;
import cn.pinming.microservice.material.client.management.common.model.PlateIdentifyDO;
import cn.pinming.microservice.material.client.management.service.biz.PlateIdentifyService;
import cn.pinming.microservice.material.client.management.service.biz.PlateService;
import cn.pinming.microservice.material.client.management.service.biz.WeighDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.TreeSet;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static cn.pinming.microservice.material.client.management.infrastructure.constant.ConfigConstant.LRP_NAME;

/**
 * 车牌识别任务.
 * <p>
 * https://www.processon.com/view/link/6538c206e59b6f1d77c6720a
 *
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2023/10/27 10:55
 */
@Component
@Slf4j
public class PlateIdentifyTask {

    @Resource
    private PlateService plateService;
    @Resource
    private PlateIdentifyExtMapper plateIdentifyExtMapper;
    @Resource
    private WeighDataService weighDataService;
    @Resource
    private PlateIdentifyService plateIdentifyService;

    @Value("${plate.anpr.enable:false}")
    private Boolean plateAnprEnable;

    @Scheduled(cron = "0 0/5 * * * ? ")
    public void run() {
        log.info("车牌识别任务:{}", plateAnprEnable);
        if (plateAnprEnable) {
            log.info("车牌识别任务开始执行");
            //查询任务表 当前时间前5分钟的数据
//            LocalDateTime dateTime = LocalDateTimeUtil.now().minusMinutes(5);
            List<PlateIdentifyDTO> taskList = plateIdentifyExtMapper.selectTaskList(null);
//            log.error("{}", JSONUtil.toJsonStr(taskList));
            if (CollUtil.isNotEmpty(taskList)) {
                taskList.forEach(obj -> CompletableFuture.supplyAsync(() -> {
                    //调用车牌识别服务 返回识别结果
                    return plateService.identifyByFileIds(obj.getFileIds());
                }).thenAcceptAsync(anprList -> {
                    //判断识别结果是否为空
                    if (CollUtil.isEmpty(anprList)) {
                        // 如果识别为空 且 没有照片 -》 高风险
                        // 如果识别为空 且 有照片 -》 中风险
                        RiskGradeEnum riskGradeEnum = StrUtil.isBlank(obj.getFileIds()) ? RiskGradeEnum.HIGH : RiskGradeEnum.MIDDLE;
                        weighDataService.updateRiskGradeTask(obj.getId(), riskGradeEnum.name(), LRP_NAME);
                    } else {
                        // 流程3 更新记录字段流程
                        List<AnprDTO> list = anprList.stream().map(o -> AnprDTO.builder().score(o.getScore()).plateNo(o.getRecognition().getPlateNo()).build()).collect(
                                Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(AnprDTO::getPlateNo))), ArrayList::new)
                        );
                        plateService.updatePlateAndRiskGrade(obj, list);
                    }
                    plateIdentifyService.lambdaUpdate().eq(PlateIdentifyDO::getRecordId, obj.getRecordId()).set(PlateIdentifyDO::getIsChecked, (byte) 1).update();
                }));
            }
            log.info("车牌识别任务执行结束");
        }
    }

}
