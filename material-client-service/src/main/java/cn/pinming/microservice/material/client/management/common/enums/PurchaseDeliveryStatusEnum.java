package cn.pinming.microservice.material.client.management.common.enums;


public enum PurchaseDeliveryStatusEnum {
    ONE(1, "待发货"),
    TWO(2, "发货中"),
    THREE(3, "发货完毕"),
    ;

    private final Integer type;
    private final String description;

    PurchaseDeliveryStatusEnum(Integer type, String description) {
        this.type = type;
        this.description = description;
    }

    public Integer value() {
        return type;
    }

    public String description() {
        return description;
    }
//
//    public static String descByType(Long type) {
//        for (PurchaseDeliveryStatusEnum value : PurchaseDeliveryStatusEnum.values()) {
//            if (Objects.equals(value.type, type)) {
//                return value.description;
//            }
//        }
//        return "";
//    }
}
