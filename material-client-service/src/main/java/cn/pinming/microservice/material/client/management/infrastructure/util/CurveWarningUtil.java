package cn.pinming.microservice.material.client.management.infrastructure.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.microservice.material.client.management.common.dto.WeighDataSavedDTO;
import cn.pinming.microservice.material.client.management.common.model.WeighCurveConfigDO;
import cn.pinming.microservice.material.client.management.service.biz.IWeighCurveConfigService;
import cn.pinming.microservice.material.client.management.service.biz.impl.WeighCurveServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
public class CurveWarningUtil {
    @Resource
    private WeighCurveServiceImpl weighCurveService;
    @Resource
    private IWeighCurveConfigService weighCurveConfigService;

    public void judge(String weighCurveData, LocalDateTime createTime,Long attributionId) throws JsonProcessingException {
        if (StrUtil.isBlank(weighCurveData) || ObjectUtil.isNull(createTime)) {
            return;
        }
        List<Double> weights = new ArrayList<>();
        List<LocalDateTime> times = new ArrayList<>();
        List<WeighDataSavedDTO> points = new ArrayList<>();
        weighCurveService.handle(weights, times, points, weighCurveData, createTime);

        if (CollUtil.isEmpty(weights) || CollUtil.isEmpty(times)) {
            return;
        }

        WeighCurveConfigDO one = weighCurveConfigService.lambdaQuery()
                .eq(WeighCurveConfigDO::getAttributionId, attributionId)
                .one();
        if (ObjectUtil.isNull(one)) {
            return;
        }
        Long betweenMax = null;
        List<Long> betweenList = new ArrayList<>();
        for (int i = 1; i < times.size(); i++) {
            long between = ChronoUnit.MILLIS.between(times.get(i - 1), times.get(i));
            betweenList.add(between);
        }
        Optional<Long> max = betweenList.stream().max(Long::compareTo);
        if (max.isPresent()) {
            betweenMax = max.get();
            if (ObjectUtil.isNotNull(one.getPlatformDuration())) {
                double maxMinute = (double) betweenMax / 60000;
                if (maxMinute - one.getPlatformDuration() > 0) {
                    // TODO:2025/4/18 平台期超时
                }
            }
        }

        long time = ChronoUnit.MILLIS.between(times.get(times.size() - 1), times.get(0));
        if (ObjectUtil.isNotNull(one.getSustainDuration())) {
            if ((double) time - one.getSustainDuration() > 0) {
                // TODO:2025/4/18 总时长超时
            }
        }

        Optional<Double> weightMax = weights.stream().max(Double::compareTo);
        if (weightMax.isPresent()) {
            if (points.size() == 1) {
                if (ObjectUtil.isNotNull(one.getWeight())) {
                    if (NumberUtil.sub(BigDecimal.valueOf(weightMax.get()),BigDecimal.valueOf(points.get(0).getWeight())).compareTo(one.getWeight()) > 0) {
                        // TODO:2025/4/18 (最大值-称重点数值)超限
                    }
                }
            }else {
                // TODO:2025/4/18 称重点数量异常
            }

        }
    }
}
