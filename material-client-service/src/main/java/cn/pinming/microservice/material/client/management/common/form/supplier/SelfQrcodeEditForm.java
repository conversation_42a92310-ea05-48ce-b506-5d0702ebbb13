package cn.pinming.microservice.material.client.management.common.form.supplier;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class SelfQrcodeEditForm {

    @ApiModelProperty(value = "id")
    @NotNull(message = "id为空")
    private Long id;

    @ApiModelProperty(value = "供应商id")
    @Size(min = 1, message = "供应商id为空")
    @NotNull(message = "供应商id为空")
    private List<Long> supplierIds;

}
