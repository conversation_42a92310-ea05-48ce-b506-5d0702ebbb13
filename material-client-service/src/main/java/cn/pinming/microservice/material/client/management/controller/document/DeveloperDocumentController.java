package cn.pinming.microservice.material.client.management.controller.document;

import cn.pinming.microservice.material.client.management.infrastructure.response.SingleResponse;
import cn.pinming.microservice.material.client.management.common.form.DeveloperDocumentForm;
import cn.pinming.microservice.material.client.management.common.form.DocumentForm;
import cn.pinming.microservice.material.client.management.common.vo.DocumentVO;
import cn.pinming.microservice.material.client.management.service.biz.DocumentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Optional;


@Api(value = "开发者文档资源管理", tags = {"developer-document"})
@RestController
@RequestMapping("/api/document/developer")
public class DeveloperDocumentController {

    @Resource
    private DocumentService documentService;

    @ApiOperation(value = "开发者文档创建", responseReference = "SingleResponse«Boolean»", nickname = "developerDocumentCreate")
    @PostMapping("/create")
    public SingleResponse<Boolean> developerDocumentCreate(@RequestBody DocumentForm documentForm) {
        documentService.developerDocumentCreate(documentForm);
        return SingleResponse.of(true);
    }

    @ApiOperation(value = "开发者文档保存", responseReference = "SingleResponse«Boolean»", nickname = "developerDocumentSave")
    @PostMapping("/save")
    public SingleResponse<Boolean> developerDocumentSave(@RequestBody DeveloperDocumentForm documentForm) {
        documentService.developerDocumentSave(documentForm);
        return SingleResponse.of(true);
    }

    @ApiOperation(value = "开发者文档列表", responseReference = "SingleResponse«List<DocumentVO>»", nickname = "developerDocumentList")
    @GetMapping("/list")
    public SingleResponse<List<DocumentVO>> developerDocumentList() {
        List<DocumentVO> list = documentService.developerDocumentList();
        return SingleResponse.of(list);
    }

    @ApiOperation(value = "开发者文档内容", responseReference = "SingleResponse«String»", nickname = "developerDocumentContent")
    @GetMapping("/preview/{id}")
    public SingleResponse<String> developerDocumentContent(@PathVariable Long id) {
        String content = documentService.developerDocumentContent(id);
        return SingleResponse.of(Optional.ofNullable(content).orElse(""));
    }

    @ApiOperation(value = "开发者文档下载", responseReference = "SingleResponse«String»", nickname = "developerDocumentDownload")
    @GetMapping("/download/{uuid}")
    public SingleResponse<String> developerDocumentDownload(@PathVariable String uuid) {
        // 2099年
        String url = documentService.developerDocumentDownload(uuid, new Date(4070880000000L));
        return SingleResponse.of(url);
    }

    @ApiOperation(value = "开发者文档删除", responseReference = "SingleResponse«Boolean»", nickname = "developerDocumentDelete")
    @DeleteMapping("/{id}")
    public SingleResponse<Boolean> developerDocumentDelete(@PathVariable Long id) {
        documentService.developerDocumentDelete(id);
        return SingleResponse.of(true);
    }

}
