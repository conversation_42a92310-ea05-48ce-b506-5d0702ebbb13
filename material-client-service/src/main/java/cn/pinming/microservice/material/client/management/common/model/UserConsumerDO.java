package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 租户用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("s_user_consumer")
public class UserConsumerDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 租户id
     */
    private String uid;

    /**
     * 用户名称
     */
    private String name;

    /**
     * 关联归属方
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String attributions;




}
