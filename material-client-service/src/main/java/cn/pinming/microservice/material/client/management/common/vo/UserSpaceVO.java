package cn.pinming.microservice.material.client.management.common.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 *
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2023/8/7 17:20
 */
@Data
public class UserSpaceVO {

    private Long id;

    /**
     * 购买空间， 单位GB
     */
    private BigDecimal spaceSize;

    /**
     * 购买空间到期时间
     */
    private LocalDateTime spaceExpire;

}
