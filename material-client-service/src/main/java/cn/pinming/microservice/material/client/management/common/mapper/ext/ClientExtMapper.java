//package cn.pinming.microservice.material.client.management.common.mapper.ext;
//
//import cn.pinming.microservice.material.client.management.common.model.ClientDO;
//import cn.pinming.microservice.material.client.management.common.vo.ClientVO;
//import com.baomidou.mybatisplus.core.mapper.BaseMapper;
//import com.baomidou.mybatisplus.core.metadata.IPage;
//import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
//
///**
// * <p>
// * 基石客户端维护表 Mapper 接口
// * </p>
// *
// * <AUTHOR>
// * @since 2023-06-16
// */
//public interface ClientExtMapper extends BaseMapper<ClientDO> {
//
//}
