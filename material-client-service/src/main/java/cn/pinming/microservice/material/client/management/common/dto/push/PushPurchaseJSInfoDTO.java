package cn.pinming.microservice.material.client.management.common.dto.push;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 基石转发订单数据体给第三方json结构体-源头基石账号信息
 */
@Data
public class PushPurchaseJSInfoDTO {
    @ApiModelProperty(value = "订单源头基石归属方code")
    private String jsAttributionCode;

    @ApiModelProperty(value = "基石订单源头id")
    private String jsPurchaseId;

    @ApiModelProperty(value = "订单源头基石租户id")
    private String jsTenantId;
}
