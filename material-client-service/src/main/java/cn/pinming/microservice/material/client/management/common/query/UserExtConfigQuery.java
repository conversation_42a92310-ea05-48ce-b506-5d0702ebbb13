package cn.pinming.microservice.material.client.management.common.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class UserExtConfigQuery {
    @ApiModelProperty(value = "租户id")
    @NotNull
    private String uid;

    @ApiModelProperty(value = "appId")
    @NotNull
    private Long appId;

    @ApiModelProperty(value = "查询类型 1 以租户维度  2 以归属方维度")
    @NotNull
    private byte type;
}
