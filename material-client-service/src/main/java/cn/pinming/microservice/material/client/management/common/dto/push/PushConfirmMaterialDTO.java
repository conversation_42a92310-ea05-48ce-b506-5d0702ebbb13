package cn.pinming.microservice.material.client.management.common.dto.push;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class PushConfirmMaterialDTO {
    @ApiModelProperty("基石运单明细id")
    private String id ;

    @ApiModelProperty(value = "运单明细数量")
    private BigDecimal waybillCounts;

    @ApiModelProperty(value = "第三方系统订单明细id")
    private String cargoId;

    @ApiModelProperty("订单明细材料名称")
    private String name ;

    @ApiModelProperty("订单明细材料规格")
    private String spec;

    @ApiModelProperty(value = "订单明细材料品牌 ")
    private String brand;

    @ApiModelProperty(value = "订单明细采购数量")
    private BigDecimal amount;

    @ApiModelProperty(value = "订单明细扣水扣杂比例")
    private BigDecimal deductRatio;

    @ApiModelProperty(value = "订单明细称重换算系数，如2.334意思为1立方米 = 2.334吨")
    private BigDecimal scaleFactor;

    @ApiModelProperty(value = "用于设置该货物称重换算系数时参照的重量单位：0 = 千克；1 = 吨")
    private Integer unitType;

    @ApiModelProperty(value = "订单明细数量单位")
    private String waybillUnit;

    @ApiModelProperty(value = "订单明细备注")
    private String remark;

    @ApiModelProperty("订单来源")
    private String source;

}
