package cn.pinming.microservice.material.client.management.controller.weighCurve;


import cn.pinming.microservice.material.client.management.common.vo.WeighCurveVO;
import cn.pinming.microservice.material.client.management.infrastructure.response.SingleResponse;
import cn.pinming.microservice.material.client.management.service.biz.IWeighCurveService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 原始记录称重曲线 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Api(value = "称重曲线数据", tags = {"curve"})
@RestController
@RequestMapping("api/curve")
public class WeighCurveController {
    @Resource
    private IWeighCurveService weighCurveService;

    @ApiOperation(value = "称重曲线")
    @GetMapping("/detail/{id}")
    public SingleResponse<WeighCurveVO> detail(@PathVariable String id) {
        WeighCurveVO vo = weighCurveService.detail(id);
        return SingleResponse.of(vo);
    }
}

