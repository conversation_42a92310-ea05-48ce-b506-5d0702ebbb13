package cn.pinming.microservice.material.client.management.common.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class ReceiptRecycleBatchEditForm {

    @ApiModelProperty("批次id")
    @NotNull(message = "批次id为空")
    private Long id;

    @ApiModelProperty("扩展编码")
    private String extCode;

    @ApiModelProperty("设备id列表 设备类型: RECEIPT_RECYCLE")
    private List<Long> deviceIdList;
}
