package cn.pinming.microservice.material.client.management.controller.msg;


import cn.pinming.core.web.response.Response;
import cn.pinming.core.web.response.SuccessResponse;
import cn.pinming.microservice.material.client.management.common.enums.MsgRobotTypeEnum;
import cn.pinming.microservice.material.client.management.common.form.MsgRobotForm;
import cn.pinming.microservice.material.client.management.common.form.MsgRobotTypeForm;
import cn.pinming.microservice.material.client.management.common.query.MsgRobotPageQuery;
import cn.pinming.microservice.material.client.management.common.vo.MsgRobotVO;
import cn.pinming.microservice.material.client.management.infrastructure.response.SingleResponse;
import cn.pinming.microservice.material.client.management.service.biz.IMsgRobotConfigService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 消息机器人配置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
@Api(value = "msg-robot", tags = {"msg-robot"})
@RestController
@RequestMapping("/api/msg-robot")
public class MsgRobotConfigController {

    @Resource
    private IMsgRobotConfigService msgRobotConfigService;

    @ApiOperation(value = "分页列表", response = MsgRobotVO.class)
    @PostMapping("/page")
    public SingleResponse<?> page(@RequestBody MsgRobotPageQuery query) {
        Page<MsgRobotVO> page = msgRobotConfigService.selectPageByQuery(query);
        return SingleResponse.of(page);
    }

    @ApiOperation(value = "保存")
    @PostMapping("/save")
    public SingleResponse<?> save(@Validated @RequestBody MsgRobotForm form) {
        msgRobotConfigService.saveOrUpdateConfig(form);
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "删除")
    @DeleteMapping("/{id}")
    public SingleResponse<?> del(@PathVariable Long id) {
        msgRobotConfigService.deleteById(id);
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "发送测试消息")
    @PostMapping("/test")
    public SingleResponse<?> test(@Validated @RequestBody MsgRobotForm form) {
        msgRobotConfigService.sendMsg(form);
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "消息类型列表")
    @GetMapping("/type/list")
    public ResponseEntity<Response> typeList() {
        List<MsgRobotTypeForm> result = MsgRobotTypeEnum.KEY_MAP.entrySet().stream()
                .map(entry -> new MsgRobotTypeForm(entry.getKey(), entry.getValue())).collect(Collectors.toList());
        return ResponseEntity.ok(new SuccessResponse(result));
    }
}

