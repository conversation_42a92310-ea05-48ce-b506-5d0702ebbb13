package cn.pinming.microservice.material.client.management.common.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class DeviceVO {
    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "设备机器码")
    private String deviceSn;

    @ApiModelProperty(value = "设备类型(称重一体机-WEIGH、单据回收终端机-RECEIPT_RECYCLE)")
    private String deviceType;

    @ApiModelProperty(value = "设备id")
    private Long deviceId;

    @ApiModelProperty(value = "当前租户")
    private String userName;

    @ApiModelProperty(value = "当前归属方")
    private String attributionName;

    @ApiModelProperty(value = "准入时间")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "是否启用 1 启用 2 禁用")
    private Byte isUsed;

    @ApiModelProperty(value = "0 自有 2租赁 3购买")
    private Byte source;

    @ApiModelProperty(value = "设备名称")
    private String name;

    @ApiModelProperty(value = "创建人")
    private String createName;

    @ApiModelProperty(value = "授权到期日期")
    private LocalDate expireDate;

    @ApiModelProperty(value = "备注")
    private String remark;

}
