package cn.pinming.microservice.material.client.management.common.vo.selfcheck;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SelfCheckModeConfigVO {

    @ApiModelProperty("司机自助确认设备模式 1 运单模式  2 单据回收模式 3 双模式（1,2）(仅适用于独立小设备) 4 仅扫码模式（仅适用于独立小设备） 5 仅毛皮重模式（仅适用于新型一体机终端） 6 简单称重模式（仅适用于新型一体机终端）")
    private Integer selfCheckMode;

    @ApiModelProperty("是否自动称重 0 关闭 1 开启")
    private Integer autoWeight;

    private Integer timeout;

    @ApiModelProperty(value = "最短时长 单位 分")
    private Integer sameTruckMinDuration;
}

