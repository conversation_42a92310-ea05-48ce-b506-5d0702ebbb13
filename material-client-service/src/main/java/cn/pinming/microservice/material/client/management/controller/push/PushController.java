package cn.pinming.microservice.material.client.management.controller.push;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizErrorException;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizExceptionMessageEnum;
import cn.pinming.microservice.material.client.management.infrastructure.response.Response;
import cn.pinming.microservice.material.client.management.infrastructure.response.SingleResponse;
import cn.pinming.microservice.material.client.management.infrastructure.util.UserIdUtil;
import cn.pinming.microservice.material.client.management.common.BaseDO;
import cn.pinming.microservice.material.client.management.common.form.oss.UserPushConfigForm;
import cn.pinming.microservice.material.client.management.common.mapper.ext.PushConfigurationExtMapper;
import cn.pinming.microservice.material.client.management.common.model.PushConfigurationDO;
import cn.pinming.microservice.material.client.management.common.vo.PushConfigurationVO;
import cn.pinming.microservice.material.client.management.service.biz.PushConfigurationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Api(value = "push", tags = {"push"})
@RestController
@RequestMapping("/api/push")
@Slf4j
public class PushController {
    @Resource
    private PushConfigurationService pushConfigurationService;
    @Resource
    private PushConfigurationExtMapper pushConfigurationExtMapper;
    @Resource
    private UserIdUtil userIdUtil;
    @ApiOperation(value = "管理员-原始记录推送-设置", responseReference = "SingleResponse«?»", nickname = "pushConfigurationIsConfig")
    @GetMapping("/config/{id}")
    public SingleResponse<?> config(@PathVariable("id") Long id, @RequestParam(value = "users",required = false) String users) {
        pushConfigurationService.config(id,users);
        return SingleResponse.of(Response.buildSuccess());
    }

    @ApiOperation(value = "管理员-原始记录推送-启用禁用", responseReference = "SingleResponse«?»", nickname = "pushConfigurationIsEnable")
    @GetMapping("/isEnable/{id}/{isEnable}")
    public SingleResponse<?> isEnable(@PathVariable("id") Long id,@PathVariable("isEnable")Byte isEnable) {
        pushConfigurationService.lambdaUpdate()
                .eq(BaseDO::getId,id)
                .set(PushConfigurationDO::getIsEnable,isEnable)
                .update();
        return SingleResponse.of(SingleResponse.buildSuccess());
    }

    @ApiOperation(value = "管理员-原始记录推送-列表显示", responseReference = "SingleResponse«List<PushConfigurationVO>»", nickname = "pushConfigurationShow")
    @GetMapping("/show")
    public SingleResponse<List<PushConfigurationVO>> pushConfigurationShow() {
        List<PushConfigurationVO> list = pushConfigurationService.pushConfigurationShow();
        return SingleResponse.of(list);
    }

    @ApiOperation(value = "租户-原始记录推送-设置", responseReference = "SingleResponse«?»", nickname = "userPushConfigurationConfig")
    @PostMapping("/user/isEnable")
    public SingleResponse<?> userConfig(@RequestBody List<UserPushConfigForm> list) {
        if (CollUtil.isNotEmpty(list)) {
            // 归属方设置校验
            List<String> attributionIdList = new ArrayList<>();
            list.forEach(e -> {
                if (StrUtil.isNotBlank(e.getAttributionIds())) {
                    List<String> split = StrUtil.split(e.getAttributionIds(), ",");
                    attributionIdList.addAll(split);
                }
            });
            if (CollUtil.isNotEmpty(attributionIdList)) {
                boolean b = attributionIdList.stream().distinct().count() != attributionIdList.size();
                if (b) {
                    throw new BizErrorException(BizExceptionMessageEnum.PUSH_ATTRIBUTION_ERROR);
                }
            }

            list.forEach(e -> {
                pushConfigurationService.lambdaUpdate()
                        .eq(BaseDO::getId,e.getId())
                        .set(PushConfigurationDO::getAttributionIds,e.getAttributionIds())
                        .update();
            });
        }

        return SingleResponse.of(SingleResponse.buildSuccess());
    }

    @ApiOperation(value = "租户-原始记录推送-列表显示", responseReference = "SingleResponse«List<PushConfigurationVO>»", nickname = "userPushConfigurationShow")
    @GetMapping("/user/show")
    public SingleResponse<List<PushConfigurationVO>> userPushConfigurationShow() {
        String uId = userIdUtil.getUId();
        List<PushConfigurationVO> list = new ArrayList<>();
        if (StrUtil.isNotBlank(uId)) {
            list = pushConfigurationExtMapper.userPushConfigurationShow(uId);
        }
        return SingleResponse.of(list);
    }
}
