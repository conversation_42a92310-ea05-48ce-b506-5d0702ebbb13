package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.microservice.material.client.management.common.enums.IsEffectiveEnum;
import cn.pinming.microservice.material.client.management.common.enums.RecycleStatusEnum;
import cn.pinming.microservice.material.client.management.common.mapper.ReceiptRecycleModuleMapper;
import cn.pinming.microservice.material.client.management.common.model.ReceiptModuleDetailConfigDO;
import cn.pinming.microservice.material.client.management.common.model.ReceiptRecycleDO;
import cn.pinming.microservice.material.client.management.common.model.ReceiptRecycleModuleDO;
import cn.pinming.microservice.material.client.management.common.vo.ReceiptRecycleModuleVO;
import cn.pinming.microservice.material.client.management.service.biz.ReceiptModuleDetailConfigService;
import cn.pinming.microservice.material.client.management.service.biz.ReceiptRecycleModuleService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 单据回收模板表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-03
 */
@Service
public class ReceiptRecycleModuleServiceImpl extends ServiceImpl<ReceiptRecycleModuleMapper, ReceiptRecycleModuleDO> implements ReceiptRecycleModuleService {
    @Resource
    private ReceiptModuleDetailConfigService receiptModuleDetailConfigService;

    @Override
    public boolean verifyAndCorrectionModule(ReceiptRecycleDO receiptRecycleDO, Long moduleId) {
        List<ReceiptRecycleModuleDO> receiptRecycleModuleDOList = this.lambdaQuery()
                .eq(ReceiptRecycleModuleDO::getReceiptRecycleId, receiptRecycleDO.getId())
                .eq(ReceiptRecycleModuleDO::getModuleId, moduleId).list();
        if (CollUtil.isEmpty(receiptRecycleModuleDOList)) {
            return true;
        }
        //全部有效则返回
        if (receiptRecycleModuleDOList.stream().noneMatch(r -> Objects.equals(r.getIsEffective(), IsEffectiveEnum.NO.getValue()))) {
            return true;
        }
        List<ReceiptModuleDetailConfigDO> receiptModuleDetailConfigDOList = receiptModuleDetailConfigService.lambdaQuery().eq(ReceiptModuleDetailConfigDO::getModuleId, moduleId)
                .eq(ReceiptModuleDetailConfigDO::getType, 2).list();
        if (CollUtil.isNotEmpty(receiptModuleDetailConfigDOList)) {
            Map<Long, ReceiptRecycleModuleDO> ocrModuleDetailMap = receiptRecycleModuleDOList.stream().collect(Collectors.toMap(ReceiptRecycleModuleDO::getOcrModuleDetailId, Function.identity()));
            for (ReceiptModuleDetailConfigDO receiptModuleDetailConfigDO : receiptModuleDetailConfigDOList) {
                if (!ocrModuleDetailMap.containsKey(receiptModuleDetailConfigDO.getModuleDetailId()) || StrUtil.isEmpty(ocrModuleDetailMap.get(receiptModuleDetailConfigDO.getModuleDetailId()).getKeyValue())) {
                    return false;
                }
            }
        }
        //修正回收模板数据组
        this.lambdaUpdate().eq(ReceiptRecycleModuleDO::getReceiptRecycleId, receiptRecycleDO.getId())
                .eq(ReceiptRecycleModuleDO::getModuleId, moduleId)
                .eq(ReceiptRecycleModuleDO::getIsEffective, IsEffectiveEnum.NO.getValue())
                .set(ReceiptRecycleModuleDO::getIsEffective, IsEffectiveEnum.YES.getValue())
                .set(ReceiptRecycleModuleDO::getGmtModify, LocalDateTime.now())
                .update();
        return true;
    }

    @Override
    public List<ReceiptRecycleModuleVO> queryReceiptRecycleModuleVO(ReceiptRecycleDO receiptRecycleDO) {
        //模板数据组装
        List<ReceiptRecycleModuleDO> receiptRecycleModuleDOList = this.lambdaQuery()
                .eq(ReceiptRecycleModuleDO::getReceiptRecycleId, receiptRecycleDO.getId())
                .eq(receiptRecycleDO.getModuleId() != null, ReceiptRecycleModuleDO::getModuleId, receiptRecycleDO.getModuleId())
                .eq(Objects.equals(receiptRecycleDO.getRecycleStatus(), RecycleStatusEnum.SUCCESS.getValue()), ReceiptRecycleModuleDO::getIsEffective, IsEffectiveEnum.YES.getValue())
                .list();
        if (CollUtil.isEmpty(receiptRecycleModuleDOList)) {
            return null;
        }
        //非成功状态下查询必须回收字段
        List<Long> mustIdList = new ArrayList<>();
        if (!Objects.equals(receiptRecycleDO.getRecycleStatus(), RecycleStatusEnum.SUCCESS.getValue())) {
            List<ReceiptModuleDetailConfigDO> receiptModuleDetailConfigDOList = receiptModuleDetailConfigService.lambdaQuery()
                    .in(ReceiptModuleDetailConfigDO::getModuleId, receiptRecycleModuleDOList.stream().map(ReceiptRecycleModuleDO::getModuleId).distinct().collect(Collectors.toList()))
                    .eq(ReceiptModuleDetailConfigDO::getType, 2).list();
            mustIdList.addAll(receiptModuleDetailConfigDOList.stream().map(ReceiptModuleDetailConfigDO::getModuleDetailId).collect(Collectors.toList()));
        }
        return receiptRecycleModuleDOList.stream().map(receiptRecycleModuleDO -> {
            ReceiptRecycleModuleVO receiptRecycleModuleVO = new ReceiptRecycleModuleVO();
            BeanUtil.copyProperties(receiptRecycleModuleDO, receiptRecycleModuleVO);
            receiptRecycleModuleVO.setIsMustRecycle(!mustIdList.isEmpty() && mustIdList.contains(receiptRecycleModuleVO.getOcrModuleDetailId()));
            return receiptRecycleModuleVO;
        }).collect(Collectors.toList());
    }
}
