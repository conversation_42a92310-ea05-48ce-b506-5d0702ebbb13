package cn.pinming.microservice.material.client.management.common.enums;

public enum DeviceStatusEnum {
    PENDING((byte) 0, "待审核"),
    PASS((byte) 1, "已审核"),
    REJECT((byte) 2, "拒绝"),
    ;

    private byte type;
    private String description;

    DeviceStatusEnum(byte type, String description) {
        this.type = type;
        this.description = description;
    }

    public byte value() {
        return type;
    }

    public String description() {
        return description;
    }

    public static String desc(Byte value) {
        for (DeviceStatusEnum statusEnum : DeviceStatusEnum.values()) {
            if (statusEnum.type == value) {
                return statusEnum.description;
            }
        }
        return null;
    }
}
