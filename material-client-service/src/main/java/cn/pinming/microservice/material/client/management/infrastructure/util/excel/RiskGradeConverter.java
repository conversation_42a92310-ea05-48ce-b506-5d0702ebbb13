package cn.pinming.microservice.material.client.management.infrastructure.util.excel;

import cn.pinming.microservice.material.client.management.common.enums.RiskGradeEnum;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.ReadConverterContext;
import com.alibaba.excel.converters.WriteConverterContext;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;

import java.util.Objects;

/**
 *
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2024/11/15 10:15
 */
public class RiskGradeConverter implements Converter<String> {

    @Override
    public Class<String> supportJavaTypeKey() {
        return String.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public String convertToJavaData(ReadConverterContext<?> context) {
        ReadCellData<?> readCellData = context.getReadCellData();
        return RiskGradeEnum.VAL_MAP.get(readCellData.getStringValue());
    }

    @Override
    public WriteCellData<String> convertToExcelData(WriteConverterContext<String> context) {
        return new WriteCellData<>(Objects.requireNonNull(RiskGradeEnum.KEY_MAP.get(context.getValue())));
    }
}
