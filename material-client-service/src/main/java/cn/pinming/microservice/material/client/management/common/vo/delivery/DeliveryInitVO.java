package cn.pinming.microservice.material.client.management.common.vo.delivery;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2024/8/22 16:59
 */
@Data
public class DeliveryInitVO {

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("客户名称: 订单拥有者名称")
    private String owner;

    @ApiModelProperty("客户采购单ID")
    private String orderExtId;

    @ApiModelProperty("订购项目")
    private String project;

    @ApiModelProperty("项目地址")
    private String address;

    @ApiModelProperty("收货人")
    private String receiver;

    @ApiModelProperty("收货人手机号")
    private String mobile;

    @ApiModelProperty("收货日期")
    private LocalDate receiveDate;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("上一车牌号")
    private String previousTruckNo;

    @ApiModelProperty("上一次发货时间")
    private LocalDateTime deliveryTime;

    @ApiModelProperty("发货清单")
    private List<DeliveryItemDTO> list;

    @ApiModelProperty("打印模板ID")
    private Long printTemplateId;

}
