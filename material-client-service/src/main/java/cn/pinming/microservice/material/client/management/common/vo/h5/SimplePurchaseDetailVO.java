package cn.pinming.microservice.material.client.management.common.vo.h5;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2024/9/5 14:40
 */
@Data
public class SimplePurchaseDetailVO {

    @ApiModelProperty(value = "采购单ID")
    private Long orderId;

    @ApiModelProperty(value = "明细ID")
    private Long id;

    @ApiModelProperty(value = "货物名称")
    private String name;

    @ApiModelProperty(value = "规格")
    private String spec;

    @ApiModelProperty(value = "采购单位")
    private String unit;

    @ApiModelProperty(value = "其他参数")
    private String argument;

    @ApiModelProperty(value = "计划使用部位")
    private String position;

//    @ApiModelProperty(value = "采购数量")
//    private BigDecimal amount;

}
