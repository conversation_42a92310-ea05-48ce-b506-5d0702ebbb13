package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 单据回收表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("d_receipt_recycle")
public class ReceiptRecycleDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 用户id
     */
    private String uid;

    /**
     * 回收来源(1-单据回收终端、2-高拍仪、3-外部推送 4-司机自助称重设备)
     */
    private Integer recycleSource;

    /**
     * 状态(1-待回收、2-回收成功、3-回收失败)
     */
    private Integer recycleStatus;

    /**
     * 归属方主键id
     */
    private Long attributionId;

    /**
     * 模版id
     */
    private Long moduleId;

    /**
     * 单据识别类型(1-自助称重类单据、2-非称重类单据)
     */
    private Integer ocrType;

    /**
     * 设备机器码
     */
    private String deviceSn;

    /**
     * 外部辅助码
     */
    private String auxiliaryCode;

    /**
     * 过磅类型(1-收料、2-发料)
     */
    private Integer weighType;

    /**
     * 车牌号
     */
    private String truckNo;

    /**
     * 毛重
     */
    private BigDecimal weightGross;

    /**
     * 毛重时间
     */
    private LocalDateTime weightGrossTime;

    /**
     * 皮重
     */
    private BigDecimal weightTare;

    /**
     * 皮重时间
     */
    private LocalDateTime weightTareTime;

    /**
     * 净重
     */
    private BigDecimal weightNet;

    /**
     * 称重单位（吨）
     */
    private String unit;

    /**
     * 回收时间
     */
    private LocalDateTime recycleTime;

    /**
     * 回收图片
     */
    private String recyclePic;

    /**
     * 回收失败类型数组
     */
    private String receiptFailTypes;

    /**
     * 推送状态 1 未推送 2 队列中 3 已推送
     */
    private Byte pushStatus;

    /**
     * 队列开始时间
     */
    private LocalDateTime waitTime;

    /**
     * 确认单本地id
     */
    private String localId;

    /**
     * 批次id
     */
    private Long batchId;

}
