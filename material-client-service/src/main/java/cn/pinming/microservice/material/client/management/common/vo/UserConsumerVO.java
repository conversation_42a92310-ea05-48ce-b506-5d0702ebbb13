package cn.pinming.microservice.material.client.management.common.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class UserConsumerVO implements Serializable {
    private static final long serialVersionUID = -8949358297916814457L;
    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "用户id")
    private Long consumeId;

    @ApiModelProperty(value = "用户名称")
    private String name;

    @ApiModelProperty(value = "是否启用 0 启用 1 禁用")
    private Integer isEnable;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "设备sn")
    private String phoneSn;

    @ApiModelProperty(value = "关联归属方id")
    private String attributions;

    @ApiModelProperty(value = "关联归属方名称")
    private String attributionNames;

}
