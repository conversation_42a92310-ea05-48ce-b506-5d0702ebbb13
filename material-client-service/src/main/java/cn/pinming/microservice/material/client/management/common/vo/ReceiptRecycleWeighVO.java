package cn.pinming.microservice.material.client.management.common.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class ReceiptRecycleWeighVO implements Serializable {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("称重数据id")
    private Long weighDataId;

    @ApiModelProperty("终端记录id")
    private String recordId;

    @ApiModelProperty("设备机器码")
    private String deviceSn;

    @ApiModelProperty("重量")
    private BigDecimal weight;

    @ApiModelProperty("称重单位")
    private String unit;

    @ApiModelProperty("称重时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime weighTime;

    @ApiModelProperty("风险等级（低：LOW，中：MIDDLE，高：HIGH）")
    private String riskGrade;

    @ApiModelProperty("文件id集合")
    private String fileIds;

    @ApiModelProperty("图片地址列表")
    private List<String> fileIdUrls;

    @ApiModelProperty("是否有效(0-否、1-是)")
    private Integer isEffective;

}
