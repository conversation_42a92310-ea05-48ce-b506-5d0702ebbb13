package cn.pinming.microservice.material.client.management.service.biz;

import cn.pinming.microservice.material.client.management.common.form.MsgRobotForm;
import cn.pinming.microservice.material.client.management.common.model.MsgRobotConfigDO;
import cn.pinming.microservice.material.client.management.common.query.MsgRobotPageQuery;
import cn.pinming.microservice.material.client.management.common.vo.MsgRobotVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 消息机器人配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
public interface IMsgRobotConfigService extends IService<MsgRobotConfigDO> {

    Page<MsgRobotVO> selectPageByQuery(MsgRobotPageQuery query);

    void saveOrUpdateConfig(MsgRobotForm form);

    void deleteById(Long id);

    void sendMsg(MsgRobotForm form);
}
