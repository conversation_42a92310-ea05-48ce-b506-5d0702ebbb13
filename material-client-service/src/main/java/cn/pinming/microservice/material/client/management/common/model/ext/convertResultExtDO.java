package cn.pinming.microservice.material.client.management.common.model.ext;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class convertResultExtDO {
    @ApiModelProperty(value = "发货数量")
    private BigDecimal weightSend;

    @ApiModelProperty(value = "折算数量")
    private BigDecimal convertValue;

    @ApiModelProperty(value = "偏差数量")
    private BigDecimal deviation;

    @ApiModelProperty(value = "转换系数")
    private BigDecimal scaleFactor;

    @ApiModelProperty(value = "转换单位")
    private String unitOfMeasurement;
}
