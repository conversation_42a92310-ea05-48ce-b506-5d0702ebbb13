package cn.pinming.microservice.material.client.management.controller.device;

import cn.pinming.microservice.material.client.management.infrastructure.response.SingleResponse;
import cn.pinming.microservice.material.client.management.infrastructure.util.UserIdUtil;
import cn.pinming.microservice.material.client.management.common.form.BindingForm;
import cn.pinming.microservice.material.client.management.common.mapper.ext.DeviceExtMapper;
import cn.pinming.microservice.material.client.management.common.vo.DeviceAttributionVO;
import cn.pinming.microservice.material.client.management.common.vo.DeviceUserVO;
import cn.pinming.microservice.material.client.management.common.vo.DeviceVO;
import cn.pinming.microservice.material.client.management.service.biz.DeviceBindingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(value = "设备", tags = {"deviceBinding"})
@RestController
@RequestMapping("/api/deviceBinding")
public class DeviceBindingController {
    @Resource
    private DeviceExtMapper deviceExtMapper;
    @Resource
    private DeviceBindingService deviceBindingService;
    @Resource
    private UserIdUtil userIdUtil;

    @ApiOperation(value = "占有设备", responseReference = "SingleResponse«?»", nickname = "own")
    @GetMapping("/own/{deviceSn}/{deviceType}")
    public SingleResponse<?> own(@PathVariable("deviceSn") String deviceSn, @PathVariable("deviceType") String deviceType) {
        deviceBindingService.own(deviceSn, deviceType);
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "移除设备", responseReference = "SingleResponse«?»", nickname = "delete")
    @GetMapping("/delete/{id}")
    public SingleResponse<?> delete(@PathVariable("id") Long id) {
        deviceBindingService.delete(id);
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "是否接收", responseReference = "SingleResponse«?»", nickname = "isReceive")
    @GetMapping("/isReceive/{deceiveId}")
    public SingleResponse<?> isReceive(@PathVariable("deceiveId") Long id) {
        deviceBindingService.isReceive(id);
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "绑定设备", responseReference = "SingleResponse«?»", nickname = "binding")
    @PostMapping("/binding")
    public SingleResponse<?> binding(@RequestBody BindingForm form) {
        deviceBindingService.binding(form);
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "更新绑定设备信息", responseReference = "SingleResponse«?»", nickname = "updateBinding")
    @PostMapping("/updateBinding")
    public SingleResponse<?> updateBinding(@RequestBody BindingForm form) {
        deviceBindingService.updateBinding(form);
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "解绑设备", responseReference = "SingleResponse«?»", nickname = "unbinding")
    @GetMapping("/unbinding/{id}")
    public SingleResponse<?> unbinding(@PathVariable("id") Long id) {
        deviceBindingService.unbinding(id);
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "用户设备列表", responseReference = "SingleResponse«List<DeviceUserVO>»", nickname = "userList")
    @GetMapping("/userList")
    public SingleResponse<List<DeviceUserVO>> userList() {
        List<DeviceUserVO> list = deviceBindingService.userList();
        return SingleResponse.of(list);
    }

    @ApiOperation(value = "可选设备列表", responseReference = "SingleResponse«List<DeviceVO>»", nickname = "select")
    @GetMapping("/select")
    public SingleResponse<List<DeviceVO>> select() {
        String uId = userIdUtil.getUId();
        List<DeviceVO> list = deviceExtMapper.selectPassDeviceList(uId);
        return SingleResponse.of(list);
    }

    @ApiOperation(value = "归属方设备列表", responseReference = "SingleResponse«List<DeviceAttributionVO>»", nickname = "attributionList")
    @GetMapping("/list/{attributionId}")
    public SingleResponse<List<DeviceAttributionVO>> attributionList(@PathVariable Long attributionId) {
        List<DeviceAttributionVO> list = deviceBindingService.attributionList(attributionId);
        return SingleResponse.of(list);
    }
}
