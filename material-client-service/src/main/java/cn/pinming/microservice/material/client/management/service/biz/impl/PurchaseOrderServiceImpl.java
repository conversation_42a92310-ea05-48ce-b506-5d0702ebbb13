package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.pinming.material.v2.model.dto.PurchaseResDTO;
import cn.pinming.microservice.material.client.management.common.enums.PushLogStatusEnum;
import cn.pinming.microservice.material.client.management.common.model.*;
import cn.pinming.microservice.material.client.management.common.enums.DeveloperAppEnum;
import cn.hutool.core.util.ObjectUtil;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizErrorException;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizExceptionMessageEnum;
import cn.pinming.microservice.material.client.management.infrastructure.util.CheckUtil;
import cn.pinming.microservice.material.client.management.infrastructure.util.UserIdUtil;
import cn.pinming.microservice.material.client.management.common.form.PurchaseForm;
import cn.pinming.microservice.material.client.management.common.mapper.DeliveryMapper;
import cn.pinming.microservice.material.client.management.common.mapper.PurchaseOrderMapper;
import cn.pinming.microservice.material.client.management.common.mapper.ext.DeviceAttributionExtMapper;
import cn.pinming.microservice.material.client.management.common.query.PurchasePageQuery;
import cn.pinming.microservice.material.client.management.common.vo.delivery.DeliveryInitVO;
import cn.pinming.microservice.material.client.management.common.vo.delivery.DeliveryItemDTO;
import cn.pinming.microservice.material.client.management.common.vo.h5.SimplePurchaseDetailVO;
import cn.pinming.microservice.material.client.management.common.vo.h5.SimplePurchaseVO;
import cn.pinming.microservice.material.client.management.common.vo.purchase.PurchaseDetailVO;
import cn.pinming.microservice.material.client.management.common.vo.purchase.PurchaseItemVO;
import cn.pinming.microservice.material.client.management.common.vo.purchase.PurchaseVO;
import cn.pinming.microservice.material.client.management.service.biz.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 采购单(订单)主表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-21
 */
@Service
public class PurchaseOrderServiceImpl extends ServiceImpl<PurchaseOrderMapper, PurchaseOrderDO> implements IPurchaseOrderService {

    @Resource
    private UserService userService;
    @Resource
    private IPurchaseOrderDetailService purchaseOrderDetailService;
    @Resource
    private DeliveryMapper deliveryMapper;
    @Resource
    private UserIdUtil userIdUtil;
    @Resource
    private IDeliveryService deliveryService;
    @Resource
    private CheckUtil checkUtil;
    @Resource
    private SupplierConfigService supplierConfigService;
    @Resource
    private DeviceAttributionService deviceAttributionService;
    @Resource
    private DeviceAttributionExtMapper deviceAttributionExtMapper;
    @Resource
    private PushLogService pushLogService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PurchaseResDTO add(String appKeyHeader, PurchaseForm form) {
        UserDO userDO = userService.getUserByAppKey(appKeyHeader);
        checkUtil.appCheck(userDO.getUid(), DeveloperAppEnum.PURCHASE_SYNC);
        String orderExtId = form.getOrderExtId();
        PurchaseOrderDO order = lambdaQuery().eq(PurchaseOrderDO::getOrderExtId, orderExtId).eq(PurchaseOrderDO::getCreateId, userDO.getUid()).one();

        // 判断外部供应商id是否绑定
        String supplierExtId = form.getSupplierExtId();
        SupplierConfigDO supplierConfigDO = supplierConfigService.lambdaQuery().eq(SupplierConfigDO::getSupplierExtId, supplierExtId).eq(SupplierConfigDO::getCreateId, userDO.getUid())
                .oneOpt().orElseThrow(() -> new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "外部供应商id未绑定"));

//        String position = form.getList().stream().map(PurchaseItemForm::getPosition).collect(Collectors.joining(";"));
        if (order != null) {
            // 订单已存在，更新订单信息
            BeanUtils.copyProperties(form, order);
            order.setDeliveryStatus(1);
            order.setSyncTime(LocalDateTime.now());
            updateById(order);
        } else {
            // 订单不存在，新增订单
            order = new PurchaseOrderDO();
            BeanUtils.copyProperties(form, order);
            // 查询归属方code所属的归属方id
            DeviceAttributionDO attributionDO = deviceAttributionExtMapper.getDeviceAttributionsByCode(form.getAttributionCode(), userDO.getUid());
            if (attributionDO != null) {
                order.setAttributionId(attributionDO.getId());
            } else {
                throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "归属方code未绑定归属方id");
            }
            order.setCreateId(userDO.getUid());
            order.setSupplierId(supplierConfigDO.getSupplierSysId());
            order.setSyncTime(LocalDateTime.now());
            save(order);
        }
        Long id = order.getId();
        // 删除原有订单明细
        purchaseOrderDetailService.lambdaUpdate().eq(PurchaseOrderDetailDO::getOrderId, id).remove();
        // 新增订单明细
        List<PurchaseOrderDetailDO> list = form.getList().stream().map(item -> {
            PurchaseOrderDetailDO detail = new PurchaseOrderDetailDO();
            BeanUtils.copyProperties(item, detail);
            detail.setOrderId(id);
            detail.setCreateId(userDO.getUid());
            return detail;
        }).collect(Collectors.toList());
        purchaseOrderDetailService.saveBatch(list);

        PurchaseResDTO purchaseResDTO = new PurchaseResDTO();
        Map<String,String> detailMap = new HashMap<>();
        purchaseResDTO.setPurchaseId(order.getId().toString());
        list.forEach(e -> {
            detailMap.put(e.getExtId(),e.getId().toString());
        });
        purchaseResDTO.setPurchaseDetailMap(detailMap);
        return purchaseResDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancel(String appKeyHeader, String orderExtId) {
        UserDO userDO = userService.getUserByAppKey(appKeyHeader);
        lambdaUpdate().eq(PurchaseOrderDO::getOrderExtId, orderExtId).eq(PurchaseOrderDO::getCreateId, userDO.getUid()).set(PurchaseOrderDO::getStatus, 2).update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancel(Long orderId) {
        PurchaseOrderDO orderDO = lambdaQuery().eq(PurchaseOrderDO::getId, orderId).oneOpt().orElseThrow(() -> new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "订单不存在"));
        String uId = userIdUtil.getUId();
        if (!uId.equals(orderDO.getCreateId())) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "无法操作非本租户订单数据");
        }
        Integer status = orderDO.getStatus() == 1 ? 2 : 1;
        lambdaUpdate().eq(PurchaseOrderDO::getId, orderId).eq(PurchaseOrderDO::getCreateId, uId).set(PurchaseOrderDO::getStatus, status).update();
    }

    @Override
    public Page<PurchaseVO> pageByQuery(PurchasePageQuery query) {
        query.setUid(userIdUtil.getUId());
        return getBaseMapper().pageByQuery(query);
    }

    @Override
    public PurchaseDetailVO detailById(Long id) {
        String uid = userIdUtil.getUId();
        PurchaseDetailVO result = getBaseMapper().selectDetailById(id);
        // 查询订单明细
        List<PurchaseItemVO> list = getBaseMapper().selectDetailListById(id, uid);
        result.setDetailList(list);
        return result;
    }

    @Override
    public DeliveryInitVO initDelivery(Long purchaseOrderId) {
        DeliveryInitVO result = new DeliveryInitVO();
        // 如果purchaseOrderId为空
        // 查询最近一次发货单所属的订单id
        DeliveryDO previousDelivery = deliveryService.lambdaQuery().eq(DeliveryDO::getType,1).and(w -> w.eq(DeliveryDO::getCreateId, userIdUtil.getUId()).or().eq(DeliveryDO::getSupplierId, userIdUtil.getUId())).orderByDesc(DeliveryDO::getGmtCreate).last("limit 1").one();
        if (purchaseOrderId == null) {
            if (previousDelivery != null) {
                purchaseOrderId = previousDelivery.getPurchaseOrderId(); // 最近一次发货单所属的订单id
            } else {
                return result;
            }
        }

        PurchaseOrderDO purchaseOrder = getById(purchaseOrderId);
        if (purchaseOrder == null) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "采购单不存在");
        }
        BeanUtils.copyProperties(purchaseOrder, result);

        String createId = purchaseOrder.getCreateId();
        UserDO user = userService.getByUid(createId);
        result.setOwner(user.getUserName());

        if (previousDelivery != null) {
            result.setPreviousTruckNo(previousDelivery.getTruckNo());
            result.setDeliveryTime(previousDelivery.getGmtCreate());
            result.setPrintTemplateId(previousDelivery.getPrintTemplateId());
        }
        // 查询订单明细
        List<DeliveryItemDTO> list = deliveryMapper.selectListByPurchaseOrderId(purchaseOrderId);
        result.setList(list);
        return result;
    }

    @Override
    public List<SimplePurchaseVO> listPurchaseOrder(String supplierExtId, Long attributionId) {
        // 获取归属方code
        DeviceAttributionDO attribution = deviceAttributionService.getById(attributionId);
        if (attribution == null) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "归属方不存在");
        }

//        String attributionCode = attribution.getCode();
//        List<String> codeList = StrUtil.split(attributionCode, StrUtil.COMMA);

        // 查询订单列表 可用订单范围：属于该归属方该供应商的订单，不包括状态为已作废或发货完毕的订单
        List<SimplePurchaseVO> list = getBaseMapper().selectH5PurchaseOrderList(supplierExtId, attributionId);
        if (CollUtil.isNotEmpty(list)) {
            List<Long> orderIdList = list.stream().map(SimplePurchaseVO::getId).collect(Collectors.toList());
            // 查询订单明细
            List<PurchaseOrderDetailDO> detailList = purchaseOrderDetailService.lambdaQuery().in(PurchaseOrderDetailDO::getOrderId, orderIdList).list();
            Map<Long, List<PurchaseOrderDetailDO>> detailMap = detailList.stream().collect(Collectors.groupingBy(PurchaseOrderDetailDO::getOrderId));
            list.forEach(item -> {
                Long id = item.getId();
                if (detailMap.containsKey(id)) {
                    item.setList(BeanUtil.copyToList(detailMap.get(id), SimplePurchaseDetailVO.class));
                }
            });
            return list;
        }
        return Collections.emptyList();
    }

    @Override
    public SimplePurchaseVO getPurchaseOrder(Long purchaseId, Long attributionId) {
        // 获取归属方code
        DeviceAttributionDO attribution = deviceAttributionService.getById(attributionId);
        if (attribution == null) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "归属方不存在");
        }

        SimplePurchaseVO result = getBaseMapper().selectH5PurchaseOrder(purchaseId, attributionId);
        if (result != null) {
            List<PurchaseOrderDetailDO> detailList = purchaseOrderDetailService.lambdaQuery().eq(PurchaseOrderDetailDO::getOrderId, purchaseId).list();
            if (CollUtil.isNotEmpty(detailList)) {
                result.setList(BeanUtil.copyToList(detailList, SimplePurchaseDetailVO.class));
            }
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void finish(String appKeyHeader, String orderExtId) {
        UserDO userDO = userService.getUserByAppKey(appKeyHeader);
        lambdaUpdate().eq(PurchaseOrderDO::getOrderExtId, orderExtId).eq(PurchaseOrderDO::getCreateId, userDO.getUid()).set(PurchaseOrderDO::getDeliveryStatus, 3).update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean pushPurchaseConfirm(Long id,String appKeyHeader) {
        UserDO userDO = userService.getUserByAppKey(appKeyHeader);

        PurchaseOrderDO one = this.lambdaQuery()
                .eq(PurchaseOrderDO::getId, id)
                .one();
        if (ObjectUtil.isNotNull(one)) {
            // 数据确认
            this.lambdaUpdate()
                    .eq(PurchaseOrderDO::getId, id)
                    .set(PurchaseOrderDO::getPushStatus, 2)
                    .update();
            // 日志
            pushLogService.createLog(userDO.getUid(), "确认了id为:" + id + "的订单", PushLogStatusEnum.THREE.value(), null);
            return true;
        }

        return false;
    }


}
