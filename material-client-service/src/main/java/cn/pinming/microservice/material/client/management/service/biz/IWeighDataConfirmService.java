package cn.pinming.microservice.material.client.management.service.biz;

import cn.pinming.material.v2.model.PageList;
import cn.pinming.material.v2.model.QueryPage;
import cn.pinming.material.v2.model.WeighDataAssemble;
import cn.pinming.material.v2.model.query.WeighDataQuery;
import cn.pinming.microservice.material.client.management.common.form.ConfirmPicForm;
import cn.pinming.microservice.material.client.management.common.model.WeighDataConfirmDO;
import cn.pinming.microservice.material.client.management.common.model.ext.WeighDataConfirmOriginExtDO;
import cn.pinming.microservice.material.client.management.common.query.ConfirmPullQuery;
import cn.pinming.microservice.material.client.management.common.query.WeighDataPageQuery;
import cn.pinming.microservice.material.client.management.common.vo.WeighConfirmPullVO;
import cn.pinming.microservice.material.client.management.common.vo.WeighDataConfirmDetailDTO;
import cn.pinming.microservice.material.client.management.common.vo.WeighDataConfirmVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 现场称重确认单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-22
 */
public interface IWeighDataConfirmService extends IService<WeighDataConfirmDO> {

    void add(WeighDataConfirmOriginExtDO form);

    IPage<WeighDataConfirmVO> col(WeighDataPageQuery query);

    WeighDataConfirmDetailDTO detail(String localId,Long id);

    WeighDataAssemble getData(String deviceSn, String record1, String record2);

    void savePic(ConfirmPicForm form);

    void confirmRecycle();

    PageList<WeighDataConfirmDetailDTO> confirmQuery(WeighDataQuery query);

    IPage<WeighConfirmPullVO> getConfirm(ConfirmPullQuery query);
}
