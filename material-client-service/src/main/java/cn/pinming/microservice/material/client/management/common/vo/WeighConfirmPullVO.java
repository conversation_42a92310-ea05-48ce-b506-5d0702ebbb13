package cn.pinming.microservice.material.client.management.common.vo;

import cn.pinming.microservice.material.client.management.common.dto.push.PushConfirmMaterialDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class WeighConfirmPullVO implements Serializable {
    @ApiModelProperty(value = "终端本地确认单id")
    private String localId;

    @ApiModelProperty(value = "确认单号")
    private String confirmNo;

    @ApiModelProperty(value = "设备机器码")
    private String deviceSn;

    @ApiModelProperty("车牌号")
    private String truckNo;

    @ApiModelProperty(value = "称重类型 1-收料过磅；2-发料过磅")
    private Integer weighType;

    @ApiModelProperty("毛重")
    private BigDecimal weightGross;

    @ApiModelProperty("皮重")
    private BigDecimal weightTare;

    @ApiModelProperty(value = "扣重")
    private BigDecimal weightDeduct;

    @ApiModelProperty(value = "净重")
    private BigDecimal weightNet;

    @ApiModelProperty(value = "扣杂(含水率)")
    private BigDecimal moistureContent;

    @ApiModelProperty(value = "实重")
    private BigDecimal weightActual;

    @ApiModelProperty(value = "换算系数")
    private BigDecimal ratio;

    @ApiModelProperty("实际数量")
    private BigDecimal actualCount;

    @ApiModelProperty(value = "称重单位")
    private String weightUnit;

    @ApiModelProperty("面单应收量：发货数量")
    private BigDecimal weightSend;

    @ApiModelProperty(value = "偏差数量")
    private BigDecimal deviationCount;

    @ApiModelProperty("偏差率")
    private BigDecimal deviationRate;

    @ApiModelProperty("进场时间")
    private LocalDateTime enterTime;

    @ApiModelProperty("出场时间")
    private LocalDateTime leaveTime;

    @ApiModelProperty("推送状态 1 未推送 2 已推送 3 推送失败")
    private Integer pushState;

    @ApiModelProperty(value = "进场照片")
    private String enterPic;

    @ApiModelProperty(value = "出场照片")
    private String leavePic;

    @ApiModelProperty(value = "单据照片")
    private String documentPic;

    @ApiModelProperty(value = "签名照片")
    private String signPic;

    @ApiModelProperty(value = "签名人照片")
    private String signerPic;

    @ApiModelProperty(value = "签名时间")
    private LocalDateTime signatureTime;

    @ApiModelProperty(value = "称重数据id1")
    private String recordId1;

    @ApiModelProperty(value = "称重数据id2")
    private String recordId2;

    @ApiModelProperty(value = "称重单位")
    private String unitInuse;

    @ApiModelProperty(value = "发货单单号")
    private String deliveryNo;

    @ApiModelProperty("第三方系统订单id")
    private String orderExtId;

    @ApiModelProperty("第三方系统供应商id")
    private String supplierExtId;

    @ApiModelProperty(value = "发货单计划使用部位")
    private String deliveryPosition;

    @ApiModelProperty(value = "发货单收货项目")
    private String deliveryProject;

    @ApiModelProperty(value = "发货单收货地址")
    private String deliveryAddress;

    @ApiModelProperty(value = "发货单收货人")
    private String deliveryReceiver;

    @ApiModelProperty(value = "发货单收货人电话")
    private String deliveryMobile;

    @ApiModelProperty(value = "发货单收货日期")
    private LocalDate deliveryReceiveDate;

    @ApiModelProperty(value = "发货单备注")
    private String deliveryRemark;

    @ApiModelProperty(value = "发货单车牌号")
    private String deliveryTruckNo;

    @ApiModelProperty(value = "发货单司机")
    private String deliveryDriver;

    @ApiModelProperty(value = "发货单司机电话")
    private String deliveryDriverMobile;

    @ApiModelProperty(value = "发货单创建时间")
    private LocalDateTime deliveryCreateTime;

    @ApiModelProperty(value = "归属方主code")
    private String primaryCode;

    @ApiModelProperty(value = "运单明细列表")
    List<PushConfirmMaterialDTO> deliveryDetails;
}
