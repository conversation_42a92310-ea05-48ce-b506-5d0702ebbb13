package cn.pinming.microservice.material.client.management.infrastructure.util.ocr;

import cn.hutool.core.util.StrUtil;
import cn.pinming.springboot.starter.redis.util.RedisUtil;
import okhttp3.*;
import org.json.JSONObject;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2025/4/27 09:32
 */
@Component
public class BaiduOCRUtil {

    @Resource
    private RedisUtil redisUtil;

    public static final OkHttpClient HTTP_CLIENT = new OkHttpClient().newBuilder().readTimeout(300, TimeUnit.SECONDS).build();

    public String accurate(String originalUrl, String uid, String ak, String sk) throws IOException {
        MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
        // url转义处理
        String encode = URLEncoder.encode(originalUrl, StandardCharsets.UTF_8.name());
        RequestBody body = RequestBody.create(mediaType, "url=" + encode + "&detect_language=true&language_type=CHN_ENG&eng_granularity=word&recognize_granularity=big&detect_direction=true&vertexes_location=false&paragraph=true&probability=false&char_probability=false&multidirectional_recognize=true");
        Request request = new Request.Builder()
                .url("https://aip.baidubce.com/rest/2.0/ocr/v1/accurate?access_token=" + getAuth(uid, ak, sk))
                .method("POST", body)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .addHeader("Accept", "application/json")
                .build();
        Response response = HTTP_CLIENT.newCall(request).execute();
        return response.body().string();
    }

    public String getAuth(String uid, String ak, String sk) {
        String auth = (String) redisUtil.get("material-ocr:auth:" + uid);
        if (auth == null) {
            auth = getAccessToken(ak, sk);
            if (StrUtil.isNotBlank(auth)) {
                redisUtil.set("material-ocr:auth:" + uid, auth, 3600 * 24 * 25); // 25天过期
            }
        }
        return auth;
    }

    private static String getAccessToken(String ak, String sk) {
        // 获取token地址
        String authHost = "https://aip.baidubce.com/oauth/2.0/token?";
        String getAccessTokenUrl = authHost
                // 1. grant_type为固定参数
                + "grant_type=client_credentials"
                // 2. 官网获取的 API Key
                + "&client_id=" + ak
                // 3. 官网获取的 Secret Key
                + "&client_secret=" + sk;
        try {
            URL realUrl = new URL(getAccessTokenUrl);
            // 打开和URL之间的连接
            HttpURLConnection connection = (HttpURLConnection) realUrl.openConnection();
            connection.setRequestMethod("GET");
            connection.connect();
            // 获取所有响应头字段
            Map<String, List<String>> map = connection.getHeaderFields();
            // 遍历所有的响应头字段
            for (String key : map.keySet()) {
                System.err.println(key + "--->" + map.get(key));
            }
            // 定义 BufferedReader输入流来读取URL的响应
            BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            StringBuilder result = new StringBuilder();
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
            System.err.println("result:" + result);
            JSONObject jsonObject = new JSONObject(result.toString());
            return jsonObject.getString("access_token");
        } catch (Exception e) {
            System.err.print("获取token失败！");
            e.printStackTrace(System.err);
        }
        return null;
    }


}
