package cn.pinming.microservice.material.client.management.common.form;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class OCRDeliveryAddForm {

//    @ApiModelProperty("车牌号")
//    @NotBlank(message = "车牌号为空")
//    private String truckNo;
//
//    @ApiModelProperty("司机姓名")
//    @NotBlank(message = "司机姓名为空")
//    private String driver;
//
//    @ApiModelProperty("司机手机号")
//    @NotBlank(message = "司机手机号为空")
//    private String driverMobile;

    @ApiModelProperty("归属方ID")
    @NotNull(message = "归属方id为空")
    private Long attributionId;

}
