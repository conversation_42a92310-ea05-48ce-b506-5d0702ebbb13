package cn.pinming.microservice.material.client.management.common.model.ext;

import cn.hutool.json.JSONObject;
import lombok.Data;

@Data
public class PrintTemplateExtDO {

    /**
     * 模板名称
     */
    private String name;

    /**
     * 模板内容
     */
    private JSONObject content;

    /**
     * 打印业务类型  0 司机确认单 1 发货单
     */
    private Byte type;

    /**
     * 小票类型  进场小票 离场小票 完整确认单小票
     */
    private Byte formType;

    /**
     * 打印小票样式  小盒子打印样式 一体机打印样式
     */
    private Byte style;

    /**
     * 打印份数
     */
    private Integer printLimit;

}
