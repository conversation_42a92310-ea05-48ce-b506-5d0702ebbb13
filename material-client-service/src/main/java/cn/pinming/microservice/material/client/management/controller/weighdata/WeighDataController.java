package cn.pinming.microservice.material.client.management.controller.weighdata;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.pinming.microservice.base.common.util.ExcelUtil;
import cn.pinming.microservice.material.client.management.common.dto.WeighDataExportDTO;
import cn.pinming.microservice.material.client.management.common.query.WeighDataPageQuery;
import cn.pinming.microservice.material.client.management.common.vo.WeighDataDetailVO;
import cn.pinming.microservice.material.client.management.common.vo.WeighDataPicVO;
import cn.pinming.microservice.material.client.management.common.vo.WeighDataUpdateLogVO;
import cn.pinming.microservice.material.client.management.common.vo.WeighDataVO;
import cn.pinming.microservice.material.client.management.infrastructure.response.SingleResponse;
import cn.pinming.microservice.material.client.management.service.biz.WeighDataService;
import cn.pinming.microservice.material.management.service.IMaterialReceiveService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Api(value = "称重数据", tags = {"weighData"})
@RestController
@RequestMapping("/api/weighData")
@Slf4j
public class WeighDataController {
    @Resource
    private WeighDataService        weighDataService;
    @DubboReference
    private IMaterialReceiveService materialReceiveService;

    @ApiOperation(value = "数据列表", responseReference = "SingleResponse«IPage<WeighDataVO>»", nickname = "list")
    @PostMapping("/list")
    public SingleResponse<IPage<WeighDataVO>> list(@RequestBody WeighDataPageQuery query) {
        IPage<WeighDataVO> page = weighDataService.col(query);
        return SingleResponse.of(page);
    }

    @ApiOperation(value = "数据列表导出", nickname = "listExport")
    @PostMapping("/list/export")
    public void listExport(@RequestBody WeighDataPageQuery query, HttpServletResponse response) {
        query.setCurrent(1L);
        query.setSize(Integer.MAX_VALUE);
        IPage<WeighDataVO> page = weighDataService.col(query);
        if (CollUtil.isNotEmpty(page.getRecords())) {
            List<WeighDataVO> records = page.getRecords();
            List<String> recordIdList = records.stream().map(WeighDataVO::getRecordId).collect(Collectors.toList());
            List<String> isUsedRecordIdList = materialReceiveService.checkRecordIsUsed(recordIdList);
            records.forEach(vo -> {
                String recordId = vo.getRecordId();
                if (CollUtil.isNotEmpty(isUsedRecordIdList) && isUsedRecordIdList.contains(recordId)) {
                    vo.setIsUsed("是");
                } else {
                    vo.setIsUsed("否");
                }
            });
            List<WeighDataExportDTO> result = BeanUtil.copyToList(page.getRecords(), WeighDataExportDTO.class);
            ExcelUtil.export(response, result, "称重数据列表");
        }
    }

    @ApiOperation(value = "仅毛皮重称重记录导出", nickname = "grossTareExport")
    @PostMapping("/grossTare/export")
    public void grossTareExport(@RequestBody WeighDataPageQuery query, HttpServletResponse response) {
        weighDataService.grossTareExport(query, response);
    }

    @ApiOperation(value = "现场称重确认单导出", nickname = "confirmOrderExport")
    @PostMapping("/confirm/order/export")
    public void confirmOrderExport(@RequestBody WeighDataPageQuery query, HttpServletResponse response) {
        weighDataService.confirmOrderExport(query, response);
    }


    @ApiOperation(value = "称重数据详情", responseReference = "SingleResponse«List<WeighDataPicVO>»", nickname = "weighDataDetail")
    @GetMapping("/detail")
    public SingleResponse<WeighDataDetailVO> detail(@RequestParam("recordId") String recordId) {
        return SingleResponse.of(weighDataService.detail(recordId));
    }

    @ApiOperation(value = "图片数据查询", responseReference = "SingleResponse«List<WeighDataPicVO>»", nickname = "pic")
    @GetMapping("/pic/{weighDataId}")
    public SingleResponse<List<WeighDataPicVO>> pic(@PathVariable("weighDataId") String weighDataId) {
        List<WeighDataPicVO> result = weighDataService.picList(weighDataId);
        return SingleResponse.of(result);
    }

    @ApiOperation(value = "小票信息查询", responseReference = "SingleResponse«List<WeighDataVO>»", nickname = "card")
    @GetMapping("/card/{weighDataId}")
    public SingleResponse<WeighDataVO> card(@PathVariable("weighDataId") String weighDataId) {
        WeighDataVO vo = weighDataService.card(weighDataId);
        return SingleResponse.of(vo);
    }

    @ApiOperation(value = "修改风险等级", responseReference = "SingleResponse«Boolean»", nickname = "updateRiskGrade")
    @PutMapping("/risk/{id}")
    public SingleResponse<Boolean> updateRiskGrade(@PathVariable("id") Long id, @RequestParam("riskGrade") String riskGrade) {
        weighDataService.updateRiskGrade(id, riskGrade);
        return SingleResponse.of(true);
    }

    @ApiOperation(value = "修改车牌", responseReference = "SingleResponse«Boolean»", nickname = "updateTruckNo")
    @PutMapping("/truck/{id}")
    public SingleResponse<Boolean> updateTruckNo(@PathVariable("id") Long id, @RequestParam("truckNo") String truckNo,
                                                 @RequestParam("flag") Boolean flag) {
        weighDataService.updateTruckNo(id, Optional.ofNullable(truckNo).orElse(""), flag);
        return SingleResponse.of(true);
    }

    @ApiOperation(value = "修改日志", responseReference = "SingleResponse«List<WeighDataUpdateLogVO>»", nickname = "updateLog")
    @GetMapping("/log/{id}")
    public SingleResponse<List<WeighDataUpdateLogVO>> updateLog(@PathVariable("id") Long id) {
        List<WeighDataUpdateLogVO> list = weighDataService.updateLog(id);
        return SingleResponse.of(list);
    }

    @ApiOperation(value = "批量重置称重数据推送状态", responseReference = "SingleResponse«Boolean»", nickname = "updatePushStatus")
    @PostMapping("/updatePushStatus")
    public SingleResponse<Boolean> updatePushStatus(@RequestBody List<String> recordIdList) {
        weighDataService.updatePushStatus(recordIdList);
        return SingleResponse.of(true);
    }
}
