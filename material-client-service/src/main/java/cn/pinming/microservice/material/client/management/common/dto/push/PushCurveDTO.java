package cn.pinming.microservice.material.client.management.common.dto.push;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 称重曲线推送DTO
 * 严格按照 curveDetail 接口的返回结构
 *
 * <AUTHOR>
 */
@Data
public class PushCurveDTO implements Serializable {

    /**
     * 终端记录id
     */
    private String recordId;

    /**
     * 称重重量列表
     */
    private List<Double> weights;

    /**
     * 称重点时间列表
     */
    private List<String> times;

    /**
     * 称重点
     */
    private List<WeighDataDTO> weighPoints;

    @Data
    public static class WeighDataDTO {
        private Double weight;
        private String unit;
        private String time;
        private String weighDataId;
    }
}



