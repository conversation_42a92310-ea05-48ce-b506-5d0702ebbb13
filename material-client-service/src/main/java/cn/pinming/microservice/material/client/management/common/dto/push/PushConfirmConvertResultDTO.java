package cn.pinming.microservice.material.client.management.common.dto.push;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class PushConfirmConvertResultDTO {
    @ApiModelProperty(value = "折算系数")
    private BigDecimal scaleFactor;

    @ApiModelProperty(value = "折算数量")
    private BigDecimal convertValue;

    @ApiModelProperty(value = "折算单位")
    private String unitOfMeasurement;

    @ApiModelProperty(value = "偏差数量")
    private BigDecimal deviation;

}
