package cn.pinming.microservice.material.client.management.controller.developer;

import cn.hutool.core.util.ObjectUtil;
import cn.pinming.microservice.material.client.management.infrastructure.response.SingleResponse;
import cn.pinming.microservice.material.client.management.infrastructure.util.UserIdUtil;
import cn.pinming.microservice.material.client.management.common.BaseDO;
import cn.pinming.microservice.material.client.management.common.form.GenerateForm;
import cn.pinming.microservice.material.client.management.common.form.UserBusinessConfigForm;
import cn.pinming.microservice.material.client.management.common.model.DeveloperDO;
import cn.pinming.microservice.material.client.management.common.model.UserBusinessConfigDO;
import cn.pinming.microservice.material.client.management.common.model.UserDO;
import cn.pinming.microservice.material.client.management.common.model.ext.DeveloperExtDO;
import cn.pinming.microservice.material.client.management.service.biz.DeveloperAppService;
import cn.pinming.microservice.material.client.management.service.biz.DeveloperService;
import cn.pinming.microservice.material.client.management.service.biz.UserBusinessConfigService;
import cn.pinming.microservice.material.client.management.service.biz.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Api(value = "app管理", tags = {"app"})
@RestController
@RequestMapping("/api/app")
public class DeveloperController {
    @Resource
    private DeveloperService developerService;
    @Resource
    private DeveloperAppService developerAppService;
    @Resource
    private UserIdUtil userIdUtil;
    @Resource
    private UserService userService;
    @Resource
    private UserBusinessConfigService userBusinessConfigService;

    @ApiOperation(value = "生成", responseReference = "SingleResponse«?»", nickname = "generate")
    @PostMapping("/generate")
    public SingleResponse<?> generate(@RequestBody GenerateForm form) {
        String uId = userIdUtil.getUId();
        developerService.generate(uId,form);
        return SingleResponse.of(SingleResponse.buildSuccess());
    }

    @ApiOperation(value = "详情", responseReference = "SingleResponse«?»", nickname = "info")
    @GetMapping("/info")
    public SingleResponse<?> info() {
        String uId = userIdUtil.getUId();
        UserDO userDO = userService.lambdaQuery().eq(UserDO::getUid, uId).select(UserDO::getAppKey, UserDO::getAppSecretKey).one();
        return SingleResponse.of(userDO);
    }

    @ApiOperation(value = "启用/停用", responseReference = "SingleResponse«?»", nickname = "isUsed")
    @GetMapping("/isUsed/{id}/{type}")
    public SingleResponse<?> isUsed(@PathVariable("id") Long id, @PathVariable("type") byte type) {
        developerService.updateDeveloperType(id, type);
        return SingleResponse.of(SingleResponse.buildSuccess());
    }

    @ApiOperation(value = "列表", responseReference = "SingleResponse«List<DeveloperExtDO>»", nickname = "list")
    @GetMapping("/list")
    public SingleResponse<List<DeveloperExtDO>> list() {
        String uId = userIdUtil.getUId();
        List<DeveloperExtDO> list = developerService.selectAppList(uId);
        return SingleResponse.of(list);
    }

    @ApiOperation(value = "风险等级配置查询", responseReference = "SingleResponse«String»", nickname = "appRiskGrade")
    @GetMapping("/risk/grade")
    public SingleResponse<String> appRiskGrade(@RequestParam Long id) {
        DeveloperDO developerDO = developerService.lambdaQuery()
                .eq(DeveloperDO::getCreateId, userIdUtil.getUId())
                .eq(DeveloperDO::getAppId, id)
                .one();
        return SingleResponse.of(ObjectUtil.isNotNull(developerDO) ? developerDO.getRiskGrade() : "");
    }

    @ApiOperation(value = "风险等级配置修改", responseReference = "SingleResponse«Boolean»", nickname = "appRiskGradeUpdate")
    @PutMapping("/risk/grade")
    public SingleResponse<Boolean> appRiskGradeUpdate(@RequestParam Long id, @RequestParam String riskGrade) {
        String uId = userIdUtil.getUId();
        DeveloperDO developerDO = developerService.lambdaQuery()
                .eq(DeveloperDO::getCreateId, uId)
                .eq(DeveloperDO::getAppId, id)
                .one();
        if (ObjectUtil.isNull(developerDO)) {
            developerDO = new DeveloperDO();
            developerDO.setAppId(id);
            developerDO.setRiskGrade(riskGrade);
            developerDO.setType((byte) 0);
            developerService.save(developerDO);
            return SingleResponse.of(true);
        }
        DeveloperDO entity = new DeveloperDO();
        entity.setId(developerDO.getId());
        entity.setRiskGrade(riskGrade);
        entity.setModifyId(uId);
        developerService.updateById(entity);
        return SingleResponse.of(true);
    }

    @ApiOperation(value = "推送订阅配置", responseReference = "SingleResponse«?»", nickname = "appPushConfig")
    @PostMapping("/push/config")
    public SingleResponse<?> appPushConfig(@RequestBody UserBusinessConfigForm form) {
        userBusinessConfigService.appPushConfig(form);
        return SingleResponse.of(SingleResponse.buildSuccess());
    }

    @ApiOperation(value = "推送订阅配置展示", responseReference = "SingleResponse«UserBusinessConfigDO»", nickname = "appPushConfigShow")
    @GetMapping("/push/config/show")
    public SingleResponse<UserBusinessConfigDO> appPushConfigShow() {
        String uId = userIdUtil.getUId();
        UserBusinessConfigDO one = userBusinessConfigService.lambdaQuery()
                .eq(UserBusinessConfigDO::getUid, uId)
                .one();
        return SingleResponse.of(one);
    }


    @ApiOperation(value = "钢筋小助手-租户设置手动输入实称重量", responseReference = "SingleResponse«?»", nickname = "updateInputEnableConfig")
    @GetMapping("/extUpdate/inputEnable/{id}/{isInputEnable}")
    public SingleResponse<?> updateInputEnableConfig(@PathVariable("id")Long id ,@PathVariable("isInputEnable")Byte isInputEnable) {
        developerService.lambdaUpdate()
                .eq(BaseDO::getId,id)
                .set(DeveloperDO::getIsInputEnable,isInputEnable)
                .update();
        return SingleResponse.of(SingleResponse.buildSuccess());
    }

    @ApiOperation(value = "钢筋小助手-租户显示是否手动输入实称重量", responseReference = "SingleResponse«DeveloperDO»", nickname = "showInputEnableConfig")
    @GetMapping("/extShow/inputEnable/{uId}")
    public SingleResponse<DeveloperDO> showInputEnableConfig(@PathVariable("uId")String uId) {
        DeveloperDO one = developerService.lambdaQuery()
                .eq(BaseDO::getCreateId, uId)
                .eq(DeveloperDO::getAppId, 7)
                .one();
        return SingleResponse.of(one);
    }
}
