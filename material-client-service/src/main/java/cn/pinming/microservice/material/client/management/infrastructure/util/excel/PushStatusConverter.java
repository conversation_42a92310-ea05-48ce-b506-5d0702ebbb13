package cn.pinming.microservice.material.client.management.infrastructure.util.excel;

import cn.pinming.microservice.material.client.management.common.enums.PushStatusEnum;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.ReadConverterContext;
import com.alibaba.excel.converters.WriteConverterContext;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;

import java.util.Objects;

/**
 *
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2024/11/15 10:15
 */
public class PushStatusConverter implements Converter<Byte> {

    @Override
    public Class<Byte> supportJavaTypeKey() {
        return Byte.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public Byte convertToJavaData(ReadConverterContext<?> context) {
        ReadCellData<?> readCellData = context.getReadCellData();
        return PushStatusEnum.VAL_MAP.get(readCellData.getStringValue());
    }

    @Override
    public WriteCellData<String> convertToExcelData(WriteConverterContext<Byte> context) {
        return new WriteCellData<>(Objects.requireNonNull(PushStatusEnum.KEY_MAP.get(context.getValue())));
    }
}
