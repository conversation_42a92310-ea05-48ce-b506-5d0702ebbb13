package cn.pinming.microservice.material.client.management.common.mapper.ext;

import cn.pinming.microservice.material.client.management.common.dto.WeighDataCheckDTO;
import cn.pinming.microservice.material.client.management.common.vo.DataSyncVO;
import cn.pinming.microservice.material.client.management.common.vo.DeviceVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DeviceExtMapper {
    String occupiedCheck(@Param("deviceSn") String deviceSn, @Param("deviceType") String deviceType);

    WeighDataCheckDTO selectInfoByDeviceSn(@Param("deviceSn") String deviceSn, @Param("appId") Long appId,@Param("deviceType") String deviceType);

    List<DeviceVO> selectPassDeviceList(@Param("uId") String uId);

    DataSyncVO syncInfo(@Param("deviceSn") String deviceSn,@Param("deviceType") String deviceType);

    List<DeviceVO> selectListByDeviceType(@Param("deviceType") String deviceType);
}
