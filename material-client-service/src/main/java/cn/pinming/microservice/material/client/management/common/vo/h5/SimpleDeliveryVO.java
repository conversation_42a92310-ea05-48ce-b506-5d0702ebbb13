package cn.pinming.microservice.material.client.management.common.vo.h5;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2024/9/5 14:40
 */
@Data
public class SimpleDeliveryVO {

    @ApiModelProperty(value = "类型 1 - 运单模式的发货单(运单);2 - OCR单据回收预生成单据")
    private Byte type;

    @ApiModelProperty(value = "运单ID")
    private Long id;

    @ApiModelProperty(value = "客户名称")
    private String owner;

    @ApiModelProperty(value = "要货日期")
    private LocalDate receiveDate;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "订单编号")
    private String no;

    @ApiModelProperty("发货车牌")
    private String truckNo;

    @ApiModelProperty("发货司机")
    private String driver;

    @ApiModelProperty("发货司机手机号")
    private String driverMobile;

    @ApiModelProperty(value = "明细列表")
    private List<SimpleDeliveryDetailVO> list;

}
