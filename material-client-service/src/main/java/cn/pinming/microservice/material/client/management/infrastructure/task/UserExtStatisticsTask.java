package cn.pinming.microservice.material.client.management.infrastructure.task;

//@Component
//@Slf4j
public class UserExtStatisticsTask {
//    @Resource
//    private UserExtConfigService userExtConfigService;
//    @Resource
//    private AppInvocationDailyLogService appInvocationDailyLogService;
//    @Value("${rocketmq.isEnable:false}")
//    private boolean isEnable;
//
//    //执行时间暂定为5分钟一次，后续优化
////    @Scheduled(cron = "0 0/5 * * * ? ")
////    @Transactional(rollbackFor = Exception.class)
//    public void run() {
//        if (!isEnable) {
//            return;
//        }
//        List<UserExtConfigDO> list = userExtConfigService.lambdaQuery().list();
//        if (CollUtil.isNotEmpty(list)) {
//            List<Long> appIdList = list.stream().map(UserExtConfigDO::getAppId).distinct().collect(Collectors.toList());
//            List<String> uidList = list.stream().map(UserExtConfigDO::getUid).distinct().collect(Collectors.toList());
//
//            // 租户 + appId 调用次数统计
//            List<AppInvocationDailyLogDO> invacationList = appInvocationDailyLogService.lambdaQuery()
//                    .in(BaseDO::getCreateId, uidList)
//                    .in(AppInvocationDailyLogDO::getAppId, appIdList)
//                    .list();
//            if (CollUtil.isNotEmpty(invacationList)) {
//                List<UserExtConfigDO> result = new ArrayList<>();
//                Map<String, Long> invacationMap = invacationList.stream().collect(Collectors.groupingBy(log -> log.getCreateId() + log.getAppId(),
//                        Collectors.summingLong(AppInvocationDailyLogDO::getUsedApiTotal)));
//
//                for (UserExtConfigDO userExtConfigDO : list) {
//                    String key = userExtConfigDO.getUid() + userExtConfigDO.getAppId();
//                    if (invacationMap.containsKey(key)) {
//                        UserExtConfigDO configDO = new UserExtConfigDO();
//                        configDO.setId(userExtConfigDO.getId());
//                        configDO.setApiUseTotal(invacationMap.get(key));
//                        configDO.setModifyId(userExtConfigDO.getCreateId());
//                        result.add(configDO);
//                    }
//                }
//                userExtConfigService.updateBatchById(result);
//            }
//        }
//    }
}
