package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 称重记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("d_weigh_data")
public class WeighDataDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 终端记录id
     */
    private String recordId;

    /**
     * 用户id
     */
    private String uid;

    /**
     * 归属方id
     */
    private Long attributionId;

    /**
     * 设备机器码
     */
    private String deviceSn;

    /**
     * 外部辅助码
     */
    private String auxiliaryCode;

    /**
     * 车牌号
     */
    private String truckNo;

    /**
     * 材料名称
     */
    private String material;

    /**
     * 重量
     */
    private BigDecimal weight;

    /**
     * 称重单位
     */
    private String unit;

    /**
     * 1 载车称重 2 净货称重
     */
    private Byte type;

    /**
     * 风险等级（低：LOW，中：MIDDLE，高：HIGH）
     */
    private String riskGrade;

    /**
     * 称重时间
     */
    private LocalDateTime weighTime;

    /**
     * 使用车牌识别事后checkout的车牌号
     */
    private String lprTruckNo;

    /**
     * 推送状态 1 未推送 2 队列中 3 已推送
     */
    private Byte pushStatus;

    /**
     * 首次队列开始时间 为什么明确为"首次"，之后还可能会有多次失败，导致数据一直处于"队列中"状态，推不推就根据有没有回调确认
     */
    private LocalDateTime waitTime;

    /**
     * 报警作弊id
     */
    private String cheatAlarmId;
}
