package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 车牌待检任务表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("d_plate_identify")
public class PlateIdentifyDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 终端记录id
     */
    private String recordId;

    /**
     * 0 未检查 1 已检查
     */
    private Byte isChecked;


}
