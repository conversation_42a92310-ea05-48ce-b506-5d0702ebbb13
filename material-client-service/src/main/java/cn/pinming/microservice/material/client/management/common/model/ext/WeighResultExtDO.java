package cn.pinming.microservice.material.client.management.common.model.ext;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class WeighResultExtDO {
    @ApiModelProperty(value = "实重")
    private BigDecimal actualWeight;

    @ApiModelProperty(value = "扣杂")
    private BigDecimal deductRatio;

    @ApiModelProperty(value = "扣重")
    private BigDecimal deductWeight;

    @ApiModelProperty(value = "净重")
    private BigDecimal netWeight;

    @ApiModelProperty(value = "称重单位")
    private String unitInuse;

    @ApiModelProperty(value = "毛重")
    private WeighDataGrossExtDO grossWeight;

    @ApiModelProperty(value = "皮重")
    private WeighDataTareExtDO tareWeight;

    @ApiModelProperty(value = "进场时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime enterTime;

    @ApiModelProperty(value = "出场时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime leaveTime;
}
