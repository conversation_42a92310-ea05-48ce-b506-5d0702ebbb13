package cn.pinming.microservice.material.client.management.controller.print;


import cn.pinming.microservice.material.client.management.common.enums.TemplateFormTypeEnum;
import cn.pinming.microservice.material.client.management.common.enums.TemplateStyleEnum;
import cn.pinming.microservice.material.client.management.common.form.PrintTemplateConfigForm;
import cn.pinming.microservice.material.client.management.common.form.supplier.PrintTemplateContentForm;
import cn.pinming.microservice.material.client.management.common.form.supplier.PrintTemplateForm;
import cn.pinming.microservice.material.client.management.common.vo.TemplateParamVO;
import cn.pinming.microservice.material.client.management.common.vo.print.PrintPreviewVO;
import cn.pinming.microservice.material.client.management.common.vo.print.PrintTemplateConfigVO;
import cn.pinming.microservice.material.client.management.common.vo.print.PrintTemplateContentVO;
import cn.pinming.microservice.material.client.management.common.vo.print.PrintTemplateVO;
import cn.pinming.microservice.material.client.management.infrastructure.response.SingleResponse;
import cn.pinming.microservice.material.client.management.service.biz.IPrintTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <p>
 * 打印模板设置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-20
 */
@Api(value = "打印模板管理", tags = {"print-template"})
@RestController
@RequestMapping("/api/print/template")
public class PrintTemplateController {

    @Resource
    private IPrintTemplateService printTemplateService;

    @ApiOperation(value = "模板列表", responseReference = "SingleResponse«List<PrintTemplateVO>»", nickname = "printTemplateList")
    @GetMapping("/list")
    public SingleResponse<?> list(@Valid @RequestParam @NotNull(message = "模板类型不能为空") Byte type) {
        List<PrintTemplateVO> result = printTemplateService.listPrintTemplate(type);
        return SingleResponse.of(result);
    }

    @ApiOperation(value = "新增、编辑模板名称", responseReference = "SingleResponse«?»", nickname = "printTemplateSave")
    @PostMapping("/save")
    public SingleResponse<?> save(@Validated @RequestBody PrintTemplateForm form) {
        printTemplateService.saveByForm(form);
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "新增、编辑模板内容", responseReference = "SingleResponse«?»", nickname = "printTemplateContentSave")
    @PostMapping("/content")
    public SingleResponse<?> saveContent(@Validated @RequestBody PrintTemplateContentForm form) {
        printTemplateService.saveContentByForm(form);
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "获取模板内容", responseReference = "SingleResponse«PrintTemplateContentVO»", nickname = "printTemplateContent")
    @GetMapping("/{id}/content")
    public SingleResponse<?> content(@PathVariable Long id) {
        PrintTemplateContentVO result = printTemplateService.queryPrintTemplateById(id);
        return SingleResponse.of(result);
    }

    @ApiOperation(value = "获取模板配置", responseReference = "SingleResponse«PrintTemplateConfigVO»", nickname = "printTemplateConfig")
    @GetMapping("/{id}/config")
    public SingleResponse<?> templateConfig(@PathVariable Long id) {
        PrintTemplateConfigVO result = printTemplateService.queryPrintTemplateConfigById(id);
        return SingleResponse.of(result);
    }

    @ApiOperation(value = "编辑模板配置", responseReference = "SingleResponse«?»", nickname = "modifyTemplateConfig")
    @PostMapping("/config")
    public SingleResponse<?> editTemplateConfig(@Validated @RequestBody PrintTemplateConfigForm form) {
        printTemplateService.modifyTemplateConfig(form);
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "删除模板", responseReference = "SingleResponse«?»", nickname = "printTemplateDelete")
    @DeleteMapping("/{id}")
    public SingleResponse<?> del(@PathVariable Long id) {
        printTemplateService.deleteById(id);
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "启用禁用模板", responseReference = "SingleResponse«?»", nickname = "printTemplateEnable")
    @GetMapping("/{id}/enable")
    public SingleResponse<?> enable(@PathVariable Long id) {
        printTemplateService.enableById(id);
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "打印预览接口", responseReference = "SingleResponse«?»", nickname = "printPreview")
    @GetMapping("/preview/{templateId}/{bizId}")
    public SingleResponse<?> preview(@PathVariable Long templateId, @PathVariable Long bizId) {
        PrintPreviewVO result = printTemplateService.preview(templateId, bizId);
        return SingleResponse.of(result);
    }

    @ApiOperation(value = "打印模版参数列表", responseReference = "SingleResponse«?»", nickname = "printParamList")
    @GetMapping("/template/{type}/params")
    public SingleResponse<?> templateParams(@PathVariable Byte type) {
        List<TemplateParamVO> result = printTemplateService.templateParams(type);
        return SingleResponse.of(result);
    }

    @ApiOperation(value = "打印模版类型列表", responseReference = "SingleResponse«?»", nickname = "printTypeList")
    @GetMapping("/style")
    public SingleResponse<?> templateStyle() {
        return SingleResponse.of(TemplateStyleEnum.KEY_MAP);
    }

    @ApiOperation(value = "打印小票类型列表", responseReference = "SingleResponse«?»", nickname = "printFromTypeList")
    @GetMapping("/formType")
    public SingleResponse<?> formTypeStyle() {
        return SingleResponse.of(TemplateFormTypeEnum.KEY_MAP);
    }

    @ApiOperation(value = "打印模板列表", responseReference = "SingleResponse«List<PrintTemplateVO>»", nickname = "printTemplateListV2")
    @GetMapping("/{type}/{style}/list")
    public SingleResponse<?> list(@PathVariable Byte type, @PathVariable Byte style, @RequestParam(required = false) Byte formType) {
        List<PrintTemplateVO> result = printTemplateService.listPrintTemplate(type, style, formType);
        return SingleResponse.of(result);
    }


}

