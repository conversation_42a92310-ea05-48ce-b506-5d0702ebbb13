package cn.pinming.microservice.material.client.management.common.model;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 地磅作弊报警日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-08
 */
@Data
@TableName("d_cheat_alarm_log")
public class CheatAlarmLogDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private String id;

    /**
     * 租户id
     */
    private String uid;

    /**
     * 设备sn
     */
    private String deviceSn;

    /**
     * 归属方id
     */
    private Long attributionId;

    /**
     * 数据帧
     */
    private String data;

//    private Integer isRiskRemoved;

    private String fileId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime riskRemoveTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    /**
     * 是否删除
     */
    @TableLogic
    private Boolean deleted;

}
