package cn.pinming.microservice.material.client.management.common.form;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class DeliveryAddSelfCheckForm {

    @ApiModelProperty("基石订单ID")
    @NotNull(message = "订单ID为空")
    private Long orderId;

    @ApiModelProperty("发货明细列表")
    @NotNull(message = "发货明细列表为空")
    @Size(min = 1, message = "发货明细列表为空")
    @Valid
    private List<DeliveryItemForm> list;

    @ApiModelProperty("归属方ID")
    private Long attributionId;

    @ApiModelProperty("车牌")
    private String truckNo;

}
