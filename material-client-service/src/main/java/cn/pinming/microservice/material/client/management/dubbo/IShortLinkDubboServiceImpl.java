package cn.pinming.microservice.material.client.management.dubbo;

import cn.pinming.microservice.material.client.management.api.IShortLinkDubboService;
import cn.pinming.microservice.material.client.management.service.biz.IShortLinkService;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2025/2/11 15:52
 */
@DubboService
public class IShortLinkDubboServiceImpl implements IShortLinkDubboService {

    @Resource
    private IShortLinkService shortLinkService;

    @Override
    public String getLongLink(String shortLink) {
        return shortLinkService.getLongLink(shortLink);
    }

    @Override
    public String createShortLink(String longLink) {
        return shortLinkService.createShortLink(longLink);
    }
}
