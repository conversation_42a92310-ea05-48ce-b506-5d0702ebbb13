package cn.pinming.microservice.material.client.management.common.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class CheckReverseVO implements Serializable {
    private static final long serialVersionUID = 7710995936717298666L;

    @ApiModelProperty(value = "自洽校验")
    private List<CheckMaterialReverseVO> theoryCheckVO;
    @ApiModelProperty(value = "复核校验")
    private List<CheckMaterialReverseVO> reverseCheckVO;
    @ApiModelProperty(value = "总重偏差")
    private CheckMaterialReverseVO totalCheckVO;
}
