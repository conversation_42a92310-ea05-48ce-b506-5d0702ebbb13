package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 钢筋验收表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("d_check")
public class CheckDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 验收编号
     */
    private String no;

    /**
     * 租户id
     */
    private String uid;

    /**
     * 归属方主键id
     */
    private Long attributionId;

    /**
     * 设备sn
     */
    private String phoneSn;

    /**
     * 用户id
     */
    private Long consumeId;

    /**
     * 车牌号码
     */
    private String truckNo;

    /**
     * 车辆到场时间
     */
    private LocalDateTime truckTime;

    /**
     * 车辆称重图片
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String truckPic;

    /**
     * 货/铭牌图片
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String goodsPic;

    /**
     * 送货单图片
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String sendPic;

    /**
     * 外部代码
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String extNo;

    /**
     * 收料地址详情
     */
    private String location;

    /**
     * 是否归档 1 否, 2 是
     */
    private Integer isVerify;

    /**
     * 验收结果 1 合格进场, 2 不合格进场
     */
    private Integer checkResult;

    /**
     * 推送状态 1 未推, 2 已推
     */
    private Integer pushStatus;

    /**
     * 验收意见
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String checkRemark;
}
