package cn.pinming.microservice.material.client.management.infrastructure.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

@EnableAsync
@Configuration
public class ExecutorConfig {
    /**
     * 单据回收线程池配置
     */
    @Bean("receiptExecutor")
    public Executor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数
        executor.setCorePoolSize(50);
        // 队列大小
        executor.setQueueCapacity(10);
        // 最大线程数
        executor.setMaxPoolSize(200);
        // 线程空闲保持时间
        executor.setKeepAliveSeconds(30);
        // 线程名前缀
        executor.setThreadNamePrefix("receiptExecutor-");
        // 设置拒绝策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardPolicy());//直接丢掉这个任务并且不会有任何异常
        executor.initialize();
        return executor;
    }

    @Bean("sdkPushExecutor")
    public Executor sdkPushExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数
        executor.setCorePoolSize(50);
        // 队列大小
        executor.setQueueCapacity(10);
        // 最大线程数
        executor.setMaxPoolSize(200);
        // 线程空闲保持时间
        executor.setKeepAliveSeconds(30);
        // 线程名前缀
        executor.setThreadNamePrefix("sdkPushExecutor-");
        // 设置拒绝策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardPolicy());//直接丢掉这个任务并且不会有任何异常
        executor.initialize();
        return executor;
    }

    @Bean("sdkPushLogExecutor")
    public Executor sdkPushLogExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数
        executor.setCorePoolSize(10);
        // 队列大小
        executor.setQueueCapacity(50);
        // 最大线程数
        executor.setMaxPoolSize(100);
        // 线程空闲保持时间
        executor.setKeepAliveSeconds(10);
        // 线程名前缀
        executor.setThreadNamePrefix("sdkPushLogExecutor-");
        // 设置拒绝策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardPolicy());//直接丢掉这个任务并且不会有任何异常
        executor.initialize();
        return executor;
    }

}
