package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <p>
 * 发货单(运单)明细
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("d_delivery_detail")
public class DeliveryDetailDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 发货单id
     */
    private Long deliveryId;

    /**
     * 订单明细id
     */
    private Long purchaseOrderDetailId;

    /**
     * 发货数量
     */
    private BigDecimal amount;

    /**
     * 1 在途，2 待确认，3 自动确认中，4 已确认，5 已作废
     */
    private Integer status;


}
