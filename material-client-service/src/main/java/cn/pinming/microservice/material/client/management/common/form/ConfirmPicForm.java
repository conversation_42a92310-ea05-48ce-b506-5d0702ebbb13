package cn.pinming.microservice.material.client.management.common.form;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class ConfirmPicForm {
    public static String TOP = "top";
    public static String BOTTOM = "bottom";
    public static String SIGNATURE = "signature";
    public static String PERSON = "person";


    @ApiModelProperty(value = "图片uuid")
    @NotBlank(message = "图片uuid不能为空")
    private String uuid;

    @ApiModelProperty(value = "确认数据本地id")
    @NotBlank(message = "确认数据本地id不能为空")
    private String localId;

    @ApiModelProperty(value = "图片类型 top 单据照片 bottom 单据照片 signature 签名 person 签名人")
    @NotBlank(message = "图片类型不能为空")
    private String type;
}
