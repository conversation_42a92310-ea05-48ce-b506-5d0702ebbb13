package cn.pinming.microservice.material.client.management.common.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class ReceiptRecycleBatchForm {

    @ApiModelProperty("批次名称")
    @NotBlank(message = "批次名称为空")
    private String name;

    @ApiModelProperty("归属方id")
    @NotNull(message = "归属方id为空")
    private Long attributionId;

    @ApiModelProperty("扩展编码")
    private String extCode;
}
