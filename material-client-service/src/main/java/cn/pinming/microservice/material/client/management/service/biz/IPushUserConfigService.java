package cn.pinming.microservice.material.client.management.service.biz;

import cn.pinming.microservice.material.client.management.common.form.GatewayForm;
import cn.pinming.microservice.material.client.management.common.form.PushConfigForm;
import cn.pinming.microservice.material.client.management.common.model.PushUserConfigDO;
import cn.pinming.microservice.material.client.management.common.vo.push.PushRouteConfigVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 租户推送配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-11
 */
public interface IPushUserConfigService extends IService<PushUserConfigDO> {

    void savePushConfig(PushConfigForm form);

    List<PushRouteConfigVO> selectOpenedRouteConfigList(Long routeConfigId);

    void saveGateway(GatewayForm form);
}
