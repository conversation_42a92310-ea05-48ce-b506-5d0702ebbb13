package cn.pinming.microservice.material.client.management.common.dto;


import lombok.Data;

import java.util.List;
import java.util.Map;


@Data
public class OCRConvertDTO {
    /**
     * 结果
     */
    private Map<String, Map<String, String[]>> result;
    /**
     * 模版id
     */
    private String id;
    /**
     * 其他id
     */
    private String other_id;
    /**
     * 二维码内容集合
     */
    private List<String> barcodes;
}
