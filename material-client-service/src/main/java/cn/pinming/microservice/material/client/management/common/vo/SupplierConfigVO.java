package cn.pinming.microservice.material.client.management.common.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class SupplierConfigVO {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("供应商名称")
    private String name;

    @ApiModelProperty("来自业务系统的供应商唯一ID")
    private String supplierExtId;

    @ApiModelProperty("供应商在基石平台的ID(UID)")
    private String supplierSysId;

    @ApiModelProperty("运单推送 0 关闭 1 开启")
    private Integer deliveryPush;

    @ApiModelProperty("确认单推送 0 关闭 1 开启")
    private Integer confirmPush;

    @ApiModelProperty("租户ID")
    private String createId;

    @ApiModelProperty("创建时间")
    private LocalDateTime gmtCreate;

    @ApiModelProperty("归属方id")
    private Long attributionId;

    @ApiModelProperty("组装超时时间 单位 分")
    private Integer timeout;

    @ApiModelProperty("供应商材料分类 逗号分隔")
    private String group;

    @ApiModelProperty("是否允许多车一料 0 不允许 1 允许")
    private Integer multiCargo;

    @ApiModelProperty(value = "最短时长 单位 分")
    private Integer sameTruckMinDuration;
}
