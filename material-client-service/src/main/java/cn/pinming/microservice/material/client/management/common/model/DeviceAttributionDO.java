package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 设备管理-设备数据归属方表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("s_device_attribution")
public class DeviceAttributionDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 用户id
     */
    private String uid;

    /**
     * 归属方名称
     */
    private String name;

    /**
     * 归属方code
     */
    private String code;

    /**
     * 主归属方(用于推送展示)
     */
    private String primaryCode;

    /**
     * 归属方logo
     */
    private String logoPic;

    /**
     * 所属终端名称是否使用本归属方名称 1 使用 2 不使用
     */
    private Byte isName;

    /**
     * 所属终端名称是否使用本归属方logo 1 使用 2 不使用
     */
    private Byte isLogo;

    @TableField(exist = false)
    private boolean isUsed;
}
