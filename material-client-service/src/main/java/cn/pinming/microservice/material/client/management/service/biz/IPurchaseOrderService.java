package cn.pinming.microservice.material.client.management.service.biz;

import cn.pinming.material.v2.model.dto.PurchaseResDTO;
import cn.pinming.microservice.material.client.management.common.form.PurchaseForm;
import cn.pinming.microservice.material.client.management.common.model.PurchaseOrderDO;
import cn.pinming.microservice.material.client.management.common.query.PurchasePageQuery;
import cn.pinming.microservice.material.client.management.common.vo.delivery.DeliveryInitVO;
import cn.pinming.microservice.material.client.management.common.vo.purchase.PurchaseDetailVO;
import cn.pinming.microservice.material.client.management.common.vo.purchase.PurchaseVO;
import cn.pinming.microservice.material.client.management.common.vo.h5.SimplePurchaseVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 采购单(订单)主表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-21
 */
public interface IPurchaseOrderService extends IService<PurchaseOrderDO> {

    PurchaseResDTO add(String appKeyHeader, PurchaseForm form);

    void cancel(String appKeyHeader, String id);

    void cancel(Long orderId);

    Page<PurchaseVO> pageByQuery(PurchasePageQuery query);

    PurchaseDetailVO detailById(Long id);

    DeliveryInitVO initDelivery(Long purchaseOrderId);

    List<SimplePurchaseVO> listPurchaseOrder(String supplierExtId, Long attributionId);

    SimplePurchaseVO getPurchaseOrder(Long purchaseId, Long attributionId);

    boolean pushPurchaseConfirm(Long id,String appKeyHeader);

    void finish(String appKeyHeader, String orderExtId);
}
