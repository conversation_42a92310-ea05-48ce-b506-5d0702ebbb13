package cn.pinming.microservice.material.client.management.common.vo;

import cn.pinming.microservice.material.client.management.common.model.WeighCurveAlarmDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class WeighDataDetailVO extends WeighDataVO implements Serializable {

    @ApiModelProperty("图片列表")
    private List<WeighDataPicVO> picList;

    @ApiModelProperty("曲线报警信息")
    private WeighCurveAlarmDO curveAlarm;
}
