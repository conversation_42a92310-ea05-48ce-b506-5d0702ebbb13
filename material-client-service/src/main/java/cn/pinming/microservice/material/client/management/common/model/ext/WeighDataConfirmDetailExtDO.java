package cn.pinming.microservice.material.client.management.common.model.ext;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class WeighDataConfirmDetailExtDO {
    @ApiModelProperty(value = "签字照片")
    private String signaturePicUuid;

    @ApiModelProperty(value = "签名人照片")
    private String signerPhotoUuid;

    @ApiModelProperty(value = "车牌号")
    private String truckNo;

    @ApiModelProperty(value = "签名时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime signatureTime;

    @ApiModelProperty(value = "称重类型：1-收料过磅；2-发料过磅，不可为空")
    private Integer weighingType;

    @ApiModelProperty(value = "送货单照片")
    private List<String> waybillPhotoUuids;

    @ApiModelProperty(value = "换算结果")
    private convertResultExtDO convertResult;

    @ApiModelProperty(value = "称重数据")
    private WeighResultExtDO weightResult;
}
