package cn.pinming.microservice.material.client.management.controller.receiptrecycle;

import cn.pinming.microservice.material.client.management.infrastructure.response.SingleResponse;
import cn.pinming.microservice.material.client.management.common.form.ReceiptModuleConfigForm;
import cn.pinming.microservice.material.client.management.common.form.ReceiptModuleDetailConfigForm;
import cn.pinming.microservice.material.client.management.common.model.ReceiptModuleConfigDO;
import cn.pinming.microservice.material.client.management.common.vo.ReceiptModuleDetailConfigVO;
import cn.pinming.microservice.material.client.management.service.biz.ReceiptModuleConfigService;
import cn.pinming.microservice.material.client.management.service.biz.ReceiptModuleDetailConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(value = "单据模板配置", tags = {"receiptModuleConfig"})
@RestController
@RequestMapping("/api")
public class ReceiptModuleConfigController {

    @Resource
    private ReceiptModuleDetailConfigService receiptModuleDetailConfigService;
    @Resource
    private ReceiptModuleConfigService receiptModuleConfigService;

    @ApiOperation(value = "单据回收-列表显示,必须键值-保存", responseReference = "SingleResponse«?»", nickname = "recycleModuleDetailSave")
    @PostMapping("/OCR/moduleRecycle/save/{moduleId}/{type}")
    public SingleResponse<?> ocrModuleRecycleSave(@PathVariable("moduleId")Long moduleId, @PathVariable("type")Byte type,
                                                  @RequestBody(required = false) @Validated List<ReceiptModuleDetailConfigForm> list) {
        receiptModuleDetailConfigService.ocrModuleRecycleSave(moduleId,type,list);
        return SingleResponse.of(SingleResponse.buildSuccess());
    }

    @ApiOperation(value = "单据回收-列表显示,必须键值-回显", responseReference = "SingleResponse« List<ocrModuleRecycleVO>»", nickname = "recycleModuleDetailShow")
    @GetMapping("/OCR/moduleRecycle/{moduleId}/{type}")
    public SingleResponse<List<ReceiptModuleDetailConfigVO>> ocrModuleRecycleShow(@PathVariable("moduleId")Long moduleId,
                                                                                  @PathVariable("type")Byte type) {
        List<ReceiptModuleDetailConfigVO> list = receiptModuleDetailConfigService.ocrModuleRecycleShow(moduleId,type,null);
        return SingleResponse.of(list);
    }


    @ApiOperation(value = "单据回收-单据模板配置-保存", responseReference = "SingleResponse«?»", nickname = "receiptModuleConfigSave")
    @PostMapping("/receiptModuleConfig/save")
    public SingleResponse<?> receiptModuleConfigSave(@RequestBody ReceiptModuleConfigForm form) {
        receiptModuleConfigService.receiptModuleConfigSave(form);
        return SingleResponse.of(SingleResponse.buildSuccess());
    }

    @ApiOperation(value = "单据回收-获取单据模板配置", responseReference = "SingleResponse«ReceiptModuleConfigDO»", nickname = "receiptModuleConfigShow")
    @GetMapping("/receiptModuleConfig/{moduleId}")
    public SingleResponse<ReceiptModuleConfigDO> receiptModuleConfigShow(@PathVariable("moduleId")Long moduleId) {
        return SingleResponse.of(receiptModuleConfigService.lambdaQuery().eq(ReceiptModuleConfigDO::getModuleId, moduleId).one());
    }


}
