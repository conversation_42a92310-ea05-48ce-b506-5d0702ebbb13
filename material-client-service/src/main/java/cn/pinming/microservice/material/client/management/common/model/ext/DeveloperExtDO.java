package cn.pinming.microservice.material.client.management.common.model.ext;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 开发者表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("s_developer")
public class DeveloperExtDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * app分类
     */
    private String name;

    /**
     * appId
     */
    private Long appId;

    /**
     * appService
     */
    private String appService;

    /**
     * 0 使用中 1 停用中 2 已删除
     */
    private Byte type;

    /**
     * 是否允许手动输入  1 允许 2 不允许
     */
    private Byte isInputEnable;
}
