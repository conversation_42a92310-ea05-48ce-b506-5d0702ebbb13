package cn.pinming.microservice.material.client.management.common.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
public enum TemplateStyleEnum {
    BOX((byte) 0, "小盒子打印样式"),
    ALL_IN_ONE((byte) 1, "一体机打印样式"),
    ;

    public static final Map<Byte, String> KEY_MAP = Arrays.stream(values()).collect(Collectors.toMap(TemplateStyleEnum::getVal, TemplateStyleEnum::getDesc));

    public static final Map<String, Byte> VAL_MAP = Arrays.stream(values()).collect(Collectors.toMap(TemplateStyleEnum::getDesc, TemplateStyleEnum::getVal));
    /**
     * 状态值
     */
    private final Byte val;
    /**
     * 状态的描述
     */
    private final String desc;

    TemplateStyleEnum(Byte val, String desc) {
        this.val = val;
        this.desc = desc;
    }

}
