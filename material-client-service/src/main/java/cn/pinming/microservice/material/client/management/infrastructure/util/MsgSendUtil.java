package cn.pinming.microservice.material.client.management.infrastructure.util;


import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import cn.pinming.exception.BOException;
import cn.pinming.microservice.material.client.management.common.dto.MsgResultDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

@Slf4j
public class MsgSendUtil {

    private static final String DING_DING_URL = "https://oapi.dingtalk.com/robot/send?access_token={}&timestamp={}&sign={}";

    private static final String WECHAT_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key={}";

    /**
     * 发送钉钉消息
     *
     * @param accessToken 钉钉机器人token
     * @param secret      加签
     * @param msg         消息体
     */
//    @SneakyThrows
    public static void sendDingDingMsg(String accessToken, String secret, String msg) throws Exception {
        Long timestamp = System.currentTimeMillis();
        String stringToSign = timestamp + "\n" + secret;
        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacSHA256"));
        byte[] signData = mac.doFinal(stringToSign.getBytes(StandardCharsets.UTF_8));
        String sign = URLEncoder.encode(new String(Base64.encodeBase64(signData)), "UTF-8");
        String url = StrUtil.format(DING_DING_URL, accessToken, timestamp, sign);
        String result = HttpUtil.post(url, msg);
        if (JSONUtil.isTypeJSON(result)) {
            MsgResultDTO resultDTO = JSONUtil.toBean(result, MsgResultDTO.class);
            if (resultDTO.getErrcode() != 0) {
                log.error("sendWechatMsg error, resultDTO: {}", resultDTO);
                throw new BOException(String.valueOf(resultDTO.getErrcode()), resultDTO.getErrmsg());
            }
        }
    }

    /**
     * 发送微信消息
     *
     * @param key 微信机器人key
     * @param msg 消息体
     */
//    @SneakyThrows
    public static void sendWechatMsg(String key, String msg) {
        String url = StrUtil.format(WECHAT_URL, key);
        String result = HttpUtil.post(url, msg);
        if (JSONUtil.isTypeJSON(result)) {
            MsgResultDTO resultDTO = JSONUtil.toBean(result, MsgResultDTO.class);
            if (resultDTO.getErrcode() != 0) {
                log.error("sendWechatMsg error, resultDTO: {}", resultDTO);
                throw new BOException(String.valueOf(resultDTO.getErrcode()), resultDTO.getErrmsg());
            }
        }
    }
}
