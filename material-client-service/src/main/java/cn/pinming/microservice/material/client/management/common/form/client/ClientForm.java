package cn.pinming.microservice.material.client.management.common.form.client;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2023/9/13 10:42
 */
@Data
public class ClientForm {

    @ApiModelProperty("文件id")
    @NotBlank(message = "请上传文件")
    private String fileId;

    @ApiModelProperty("版本号 x.y.z")
    @NotBlank(message = "请设置文件版本号")
    private String version;

    @ApiModelProperty("类型 1 客户端 2 工具 3 文档")
    @NotNull(message = "请选择类型")
    private Byte type;

    @ApiModelProperty("备注")
//    @NotBlank(message = "请填写备注")
    private String remark;

}
