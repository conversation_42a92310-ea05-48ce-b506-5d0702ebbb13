package cn.pinming.microservice.material.client.management.common.query;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class MsgRobotPageQuery extends BasePageQuery {

    @ApiModelProperty("机器人名称")
    private String name;

    @ApiModelProperty("消息类型 1钉钉群消息机器人 2企微群消息机器人")
    private Integer type;

}
