package cn.pinming.microservice.material.client.management.common.form;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class DeliverySyncIdentityForm {
    /**
     * 同步目标的基石归属方code
     */
    @NotBlank(message = "同步基石归属方code不能为空")
    private String jsAttributionCode;

    /**
     *  基石运单类型 1 - 运单模式的发货单(运单);2 - OCR单据回收预生成单据
     */
    @NotBlank(message = "运单类型不能为空")
    private String jsDeliveryType;

    /**
     * 同步目标的基石租户id
     */
    @NotBlank(message = "同步基石租户id不能为空")
    private String jsTenantId;
}
