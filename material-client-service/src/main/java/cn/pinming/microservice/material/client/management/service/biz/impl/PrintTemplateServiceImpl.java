package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.pinming.microservice.material.client.management.common.enums.DeliveryParamEnum;
import cn.pinming.microservice.material.client.management.common.enums.DriverCheckParamEnum;
import cn.pinming.microservice.material.client.management.common.enums.OCRDriverCheckParamEnum;
import cn.pinming.microservice.material.client.management.common.enums.TemplateTypeEnum;
import cn.pinming.microservice.material.client.management.common.form.PrintTemplateConfigForm;
import cn.pinming.microservice.material.client.management.common.form.supplier.PrintTemplateContentForm;
import cn.pinming.microservice.material.client.management.common.form.supplier.PrintTemplateForm;
import cn.pinming.microservice.material.client.management.common.mapper.DeliveryMapper;
import cn.pinming.microservice.material.client.management.common.mapper.PrintTemplateMapper;
import cn.pinming.microservice.material.client.management.common.model.DeliveryDO;
import cn.pinming.microservice.material.client.management.common.model.PrintTemplateConfigDO;
import cn.pinming.microservice.material.client.management.common.model.PrintTemplateDO;
import cn.pinming.microservice.material.client.management.common.vo.TemplateParamVO;
import cn.pinming.microservice.material.client.management.common.vo.delivery.DeliveryItemDTO;
import cn.pinming.microservice.material.client.management.common.vo.print.PrintPreviewVO;
import cn.pinming.microservice.material.client.management.common.vo.print.PrintTemplateConfigVO;
import cn.pinming.microservice.material.client.management.common.vo.print.PrintTemplateContentVO;
import cn.pinming.microservice.material.client.management.common.vo.print.PrintTemplateVO;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizErrorException;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizExceptionMessageEnum;
import cn.pinming.microservice.material.client.management.infrastructure.util.UserIdUtil;
import cn.pinming.microservice.material.client.management.service.biz.IDeliveryService;
import cn.pinming.microservice.material.client.management.service.biz.IPrintTemplateConfigService;
import cn.pinming.microservice.material.client.management.service.biz.IPrintTemplateService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 打印模板设置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-20
 */
@Service
public class PrintTemplateServiceImpl extends ServiceImpl<PrintTemplateMapper, PrintTemplateDO> implements IPrintTemplateService {

    @Resource
    private UserIdUtil userIdUtil;
    @Resource
    private IPrintTemplateConfigService templateConfigService;
    @Resource
    private IDeliveryService deliveryService;
    @Resource
    private DeliveryMapper deliveryMapper;

    @Override
    public List<PrintTemplateVO> listPrintTemplate(Byte type) {
        List<PrintTemplateVO> list = getBaseMapper().selectTemplateConfigList(userIdUtil.getUId(), type, null, null);
        if (CollUtil.isNotEmpty(list)) {
            list.forEach(obj -> {
                String attributionIds = obj.getAttributionId();
                if (StrUtil.isNotBlank(attributionIds)) {
                    int count = StrUtil.split(attributionIds, StrUtil.COMMA).size();
                    obj.setAmount(count);
                }
            });
        }
        return list;
    }

    @Override
    public List<PrintTemplateVO> listPrintTemplate(Byte type, Byte style, Byte formType) {
        List<PrintTemplateVO> list = getBaseMapper().selectTemplateConfigList(userIdUtil.getUId(), type, style, formType);
        if (CollUtil.isNotEmpty(list)) {
            list.forEach(obj -> {
                String attributionIds = obj.getAttributionId();
                if (StrUtil.isNotBlank(attributionIds)) {
                    int count = StrUtil.split(attributionIds, StrUtil.COMMA).size();
                    obj.setAmount(count);
                }
            });
        }
        return list;
    }

    @Override
    public PrintTemplateConfigVO queryPrintTemplateConfigById(Long id) {
        PrintTemplateDO printTemplate = getById(id);
        if (printTemplate == null) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "打印模板不存在");
        }
        PrintTemplateConfigVO result = new PrintTemplateConfigVO();
        BeanUtils.copyProperties(printTemplate, result);

        PrintTemplateConfigDO configDO = templateConfigService.lambdaQuery().eq(PrintTemplateConfigDO::getTemplateId, id).eq(PrintTemplateConfigDO::getCreateId, userIdUtil.getUId()).one();

        if (configDO != null) {
            result.setPrintLimit(configDO.getPrintLimit());
            result.setDeviceIdList(StrUtil.split(configDO.getAttributionId(), StrUtil.COMMA).stream().filter(StrUtil::isNotBlank).mapToLong(Long::parseLong).boxed().collect(Collectors.toList()));
        }
        return result;
    }

    @Override
    public PrintPreviewVO preview(Long templateId, Long bizId) {
        PrintTemplateDO template = lambdaQuery().eq(PrintTemplateDO::getId, templateId).oneOpt().orElseThrow(() -> new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "打印模板不存在"));
        Map<String, Object> params = new HashMap<>();
        if (Objects.equals(template.getType(), TemplateTypeEnum.DELIVERY_ORDER.getVal())) {
            // 发货单
            DeliveryDO delivery = deliveryService.lambdaQuery().eq(DeliveryDO::getId, bizId).eq(DeliveryDO::getCreateId, userIdUtil.getUId())
                    .oneOpt().orElseThrow(() -> new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "发货单不存在"));

            buildMap(params, DeliveryParamEnum.NO.getVal(), delivery.getNo());
            buildMap(params, DeliveryParamEnum.PRINT_TIME.getVal(), LocalDateTimeUtil.format(delivery.getGmtCreate(), DatePattern.NORM_DATETIME_PATTERN));
            buildMap(params, DeliveryParamEnum.TRUCK_NO.getVal(), delivery.getTruckNo());
            buildMap(params, DeliveryParamEnum.DRIVER.getVal(), delivery.getDriver());
            buildMap(params, DeliveryParamEnum.DRIVER_MOBILE.getVal(), delivery.getDriverMobile());
            buildMap(params, DeliveryParamEnum.ADDRESS.getVal(), delivery.getAddress());
            buildMap(params, DeliveryParamEnum.RECEIVER.getVal(), delivery.getReceiver());
            buildMap(params, DeliveryParamEnum.RECEIVER_MOBILE.getVal(), delivery.getMobile());
            buildMap(params, DeliveryParamEnum.PROJECT.getVal(), delivery.getProject());
            buildMap(params, DeliveryParamEnum.RECEIVE_DATE.getVal(), delivery.getReceiveDate());
            buildMap(params, DeliveryParamEnum.REMARK.getVal(), delivery.getRemark());

            DeliveryDO previousDelivery = deliveryService.lambdaQuery().eq(DeliveryDO::getCreateId, userIdUtil.getUId()).orderByDesc(DeliveryDO::getGmtCreate).last("limit 1").one();
            if (previousDelivery != null) {
                buildMap(params, DeliveryParamEnum.PREVIOUS_TRUCK_NO.getVal(), previousDelivery.getTruckNo());
                buildMap(params, DeliveryParamEnum.PREVIOUS_TIME.getVal(), LocalDateTimeUtil.format(previousDelivery.getGmtCreate(), DatePattern.NORM_DATETIME_PATTERN));
            }

            // 明细列表
            List<DeliveryItemDTO> list = deliveryMapper.selectListByDeliveryId(bizId);
            if (CollUtil.isNotEmpty(list)) {
                List<Map<String, Object>> materialList = list.stream().map(obj -> {
                    Map<String, Object> map = new HashMap<>();
                    buildMap(map, DeliveryParamEnum.MATERIAL_NAME.getVal(), obj.getName());
                    buildMap(map, DeliveryParamEnum.MATERIAL_SPEC.getVal(), obj.getSpec());
                    buildMap(map, DeliveryParamEnum.MATERIAL_REMARK.getVal(), obj.getRemark());
                    buildMap(map, DeliveryParamEnum.MATERIAL_ARGUMENT.getVal(), obj.getArgument());
                    buildMap(map, DeliveryParamEnum.ORDER_AMOUNT.getVal(), obj.getOrderAmount());
                    buildMap(map, DeliveryParamEnum.UNIT.getVal(), obj.getUnit());
                    buildMap(map, DeliveryParamEnum.SEND_AMOUNT.getVal(), obj.getSendAmount());
                    buildMap(map, DeliveryParamEnum.TRUCK_AMOUNT.getVal(), obj.getTruckAmount());
                    buildMap(map, DeliveryParamEnum.AMOUNT.getVal(), obj.getCurrentAmount());
                    buildMap(map, DeliveryParamEnum.POSITION.getVal(), obj.getPosition());
                    return map;
                }).collect(Collectors.toList());
                params.put("list", materialList);
            }
        }
//        else if (Objects.equals(template.getType(), TemplateTypeEnum.DRIVER_CHECK_ORDER.getVal())) {
        // 司机确认单
//            params.put(DriverCheckParamEnum.TITLE.getVal(), null);
//            params.put(DriverCheckParamEnum.LOCAL_ID.getVal(), null);
//            params.put(DriverCheckParamEnum.PRINT_TIME.getVal(), null);
//            params.put(DriverCheckParamEnum.DEVICE_SN.getVal(), null);
//            params.put(DriverCheckParamEnum.SIGNATURE.getVal(), null);
//            params.put(DriverCheckParamEnum.SUPPLIER.getVal(), null);
//            params.put(DriverCheckParamEnum.ATTRITION.getVal(), null);
//            params.put(DriverCheckParamEnum.WEIGHT_GROSS.getVal(), null);
//            params.put(DriverCheckParamEnum.WEIGHT_TARE.getVal(), null);
//            params.put(DriverCheckParamEnum.WEIGHT_NET.getVal(), null);
//            params.put(DriverCheckParamEnum.WEIGHT_DEDUCT.getVal(), null);
//            params.put(DriverCheckParamEnum.MOISTURE_CONTENT.getVal(), null);
//            params.put(DriverCheckParamEnum.WEIGHT_ACTUAL.getVal(), null);
//            params.put(DriverCheckParamEnum.WEIGHT_UNIT.getVal(), null);
//            params.put(DriverCheckParamEnum.GROSS_TIME.getVal(), null);
//            params.put(DriverCheckParamEnum.TARE_TIME.getVal(), null);
//            params.put(DriverCheckParamEnum.CHECK_TIME.getVal(), null);
//            params.put(DriverCheckParamEnum.RATIO.getVal(), null);
//            params.put(DriverCheckParamEnum.ACTUAL_COUNT.getVal(), null);
//            params.put(DriverCheckParamEnum.DEVIATION_COUNT.getVal(), null);
//            params.put(DriverCheckParamEnum.MATERIAL_NAME.getVal(), null);
//            params.put(DriverCheckParamEnum.MATERIAL_SPEC.getVal(), null);
//        }

        PrintPreviewVO result = new PrintPreviewVO();
        result.setContent(JSONUtil.parseObj(template.getContent()));
        result.setParams(params);
        return result;
    }

    @Override
    public List<TemplateParamVO> templateParams(Byte type) {
        if (Objects.equals(type, TemplateTypeEnum.DELIVERY_ORDER.getVal())) {
            return DeliveryParamEnum.LIST;
        } else if (Objects.equals(type, TemplateTypeEnum.DRIVER_CHECK_ORDER.getVal())) {
            return DriverCheckParamEnum.LIST;
        } else if (Objects.equals(type, TemplateTypeEnum.DELIVERY_OCR_ORDER.getVal())) {
            return OCRDriverCheckParamEnum.LIST;
        }
        return Collections.emptyList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modifyTemplateConfig(PrintTemplateConfigForm form) {
        Long id = form.getId();
        PrintTemplateDO printTemplate = getById(id);
        if (printTemplate == null) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "打印模板不存在");
        }
        PrintTemplateConfigDO configDO = templateConfigService.lambdaQuery().eq(PrintTemplateConfigDO::getTemplateId, id).eq(PrintTemplateConfigDO::getCreateId, userIdUtil.getUId()).one();
        if (configDO == null) {
            configDO = new PrintTemplateConfigDO();
            configDO.setTemplateId(id);
            configDO.setCreateId(userIdUtil.getUId());
            configDO.setPrintLimit(form.getPrintLimit());
            configDO.setAttributionId(StrUtil.join(StrUtil.COMMA, form.getDeviceIdList()));
            templateConfigService.save(configDO);
        } else {
            templateConfigService.lambdaUpdate().eq(PrintTemplateConfigDO::getTemplateId, id).eq(PrintTemplateConfigDO::getCreateId, userIdUtil.getUId())
                    .set(PrintTemplateConfigDO::getPrintLimit, form.getPrintLimit()).set(PrintTemplateConfigDO::getAttributionId, StrUtil.join(StrUtil.COMMA, form.getDeviceIdList()))
                    .update(new PrintTemplateConfigDO());
        }
        if (CollUtil.isNotEmpty(form.getDeviceIdList())) {
            // 查询本租户下的其他模板
            List<PrintTemplateConfigDO> list = getBaseMapper().selectAttributionList(id, form.getDeviceIdList(), userIdUtil.getUId(), printTemplate.getType(), printTemplate.getStyle(), printTemplate.getFormType());
            if (CollUtil.isNotEmpty(list)) {
                List<PrintTemplateConfigDO> configList = list.stream().map(obj -> {
                    String attributionId = obj.getAttributionId();
                    List<Long> deviceIdList = StrUtil.split(attributionId, StrUtil.COMMA).stream().filter(StrUtil::isNotBlank).mapToLong(Long::parseLong).boxed().collect(Collectors.toList());
                    deviceIdList.removeAll(form.getDeviceIdList());
                    obj.setAttributionId(StrUtil.join(StrUtil.COMMA, deviceIdList));
                    obj.setId(obj.getId());
                    return obj;
                }).collect(Collectors.toList());
                templateConfigService.updateBatchById(configList);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(Long id) {
        PrintTemplateDO printTemplate = lambdaQuery().eq(PrintTemplateDO::getId, id).one();
        if (printTemplate != null && (StrUtil.isBlank(printTemplate.getCreateId()) || !Objects.equals(printTemplate.getCreateId(), userIdUtil.getUId()))) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "无法操作非本租户创建的打印模板");
        }
        removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveByForm(PrintTemplateForm form) {
        Long id = form.getId();
        PrintTemplateDO printTemplate = lambdaQuery().eq(PrintTemplateDO::getId, id).one();
        if (printTemplate != null && (StrUtil.isBlank(printTemplate.getCreateId()) || !Objects.equals(printTemplate.getCreateId(), userIdUtil.getUId()))) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "无法操作非本租户创建的打印模板");
        }

        PrintTemplateDO template = new PrintTemplateDO();
        BeanUtils.copyProperties(form, template);
        saveOrUpdate(template);

        PrintTemplateConfigDO configDO = templateConfigService.lambdaQuery().eq(PrintTemplateConfigDO::getTemplateId, id).eq(PrintTemplateConfigDO::getCreateId, userIdUtil.getUId()).one();
        if (configDO == null) {
            configDO = new PrintTemplateConfigDO();
            configDO.setTemplateId(template.getId());
            configDO.setCreateId(userIdUtil.getUId());
            templateConfigService.save(configDO);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveContentByForm(PrintTemplateContentForm form) {
        PrintTemplateDO printTemplate = lambdaQuery().eq(PrintTemplateDO::getId, form.getId()).oneOpt().orElseThrow(() -> new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "打印模板不存在"));
        if (StrUtil.isBlank(printTemplate.getCreateId()) || !Objects.equals(printTemplate.getCreateId(), userIdUtil.getUId())) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "无法操作非本租户创建的打印模板");
        }
        PrintTemplateDO template = new PrintTemplateDO();
        BeanUtils.copyProperties(form, template);
        updateById(template);
    }

    @Override
    public PrintTemplateContentVO queryPrintTemplateById(Long id) {
        PrintTemplateDO printTemplate = lambdaQuery().eq(PrintTemplateDO::getId, id).oneOpt().orElseThrow(() -> new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "打印模板不存在"));
        PrintTemplateContentVO result = new PrintTemplateContentVO();
        BeanUtils.copyProperties(printTemplate, result);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enableById(Long id) {
        PrintTemplateDO printTemplate = lambdaQuery().eq(PrintTemplateDO::getId, id).oneOpt().orElseThrow(() -> new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "打印模板不存在"));
        PrintTemplateConfigDO configDO = templateConfigService.lambdaQuery().eq(PrintTemplateConfigDO::getTemplateId, id).eq(PrintTemplateConfigDO::getCreateId, userIdUtil.getUId()).one();
        if (configDO == null) {
            configDO = new PrintTemplateConfigDO();
            configDO.setTemplateId(id);
            templateConfigService.save(configDO);
        } else {
            getBaseMapper().updateEnableStatusById(id, userIdUtil.getUId());
        }
    }

    private void buildMap(Map<String, Object> map, String key, Object value) {
        map.put(key, value == null ? "/" : value.toString());
    }

}
