package cn.pinming.microservice.material.client.management.common.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class CheckTruckVO implements Serializable {
    private static final long serialVersionUID = 6087216331485793996L;

    @ApiModelProperty(value = "车牌号码")
    private String truckNo;

    @ApiModelProperty(value = "车辆到场时间")
    private LocalDateTime truckTime;

    @ApiModelProperty(value = "车辆称重图片")
    private String truckPic;

    @ApiModelProperty(value = "货/铭牌图片")
    private String goodsPic;

    @ApiModelProperty(value = "送货单图片")
    private String sendPic;

    @ApiModelProperty(value = "车辆称重图片")
    private Map<String, String> truckPics;

    @ApiModelProperty(value = "货/铭牌图片")
    private Map<String, String> goodsPics;

    @ApiModelProperty(value = "送货单图片")
    private Map<String, String> sendPics;

    @ApiModelProperty(value = "外部代码")
    private String extNo;

    @ApiModelProperty(value = "收料地址详情")
    private String location;

    @ApiModelProperty(value = "验收结果 1 合格进场, 2 不合格进场")
    private Integer checkResult;

    @ApiModelProperty(value = "验收意见")
    private String checkRemark;
}
