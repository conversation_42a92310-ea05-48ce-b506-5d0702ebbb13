package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <p>
 * 资源每日消耗日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("s_app_resource_daily_log")
public class AppResourceDailyLogDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 统计日期 YYYY-mm-dd
     */
    private String date;

    /**
     * 已用订阅存储空间(G)
     */
    private BigDecimal usedSpace;

}
