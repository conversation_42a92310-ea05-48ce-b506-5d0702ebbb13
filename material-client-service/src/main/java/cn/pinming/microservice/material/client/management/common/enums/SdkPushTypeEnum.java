package cn.pinming.microservice.material.client.management.common.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
public enum SdkPushTypeEnum {
    DELIVERY(1L, "订单推送服务"),
    CONFIRM(2L, "确认单推送服务"),
    DATA(3L, "原始数据推送服务"),
    CURVE(4L, "称重曲线原始数据推送服务"),
    WEIGH_DATA_PIC(5L, "称重照片推送服务"),
    ;

    public static final Map<Long, String> KEY_MAP = Arrays.stream(values()).collect(Collectors.toMap(SdkPushTypeEnum::getVal, SdkPushTypeEnum::getDesc));

    public static final Map<String, Long> VAL_MAP = Arrays.stream(values()).collect(Collectors.toMap(SdkPushTypeEnum::getDesc, SdkPushTypeEnum::getVal));

    private Long   val;
    private String desc;

    SdkPushTypeEnum(Long val, String desc) {
        this.val = val;
        this.desc = desc;
    }


}
