package cn.pinming.microservice.material.client.management.proxy.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.pinming.microservice.material.client.management.proxy.MaterialServiceProxy;
import cn.pinming.microservice.material.client.management.common.BaseDO;
import cn.pinming.microservice.material.client.management.common.model.RebarMaterialDO;
import cn.pinming.microservice.material.client.management.service.biz.RebarMaterialService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class MaterialServiceProxyImpl implements MaterialServiceProxy {
    @Resource
    private RebarMaterialService rebarMaterialService;

    @Override
    public RebarMaterialDO getMaterialByMaterialId(Long materialId) {
        if (ObjectUtil.isNotNull(materialId)) {
            return rebarMaterialService.lambdaQuery()
                    .eq(BaseDO::getId, materialId)
                    .one();
        }
        return null;
    }

    @Override
    public List<RebarMaterialDO> getMaterialsByMaterialIds(List<Long> materialIds) {
        if (CollUtil.isNotEmpty(materialIds)) {
            return rebarMaterialService.lambdaQuery()
                    .in(BaseDO::getId, materialIds)
                    .list();
        }

        return null;
    }
}
