package cn.pinming.microservice.material.client.management.common.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class WeighDataVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("终端记录id")
    private String recordId;

    @ApiModelProperty("归属方id")
    private Integer attributionId;

    @ApiModelProperty("归属方名称")
    private String name;

    @ApiModelProperty("归属方code")
    private String code;

    @ApiModelProperty("设备机器码")
    private String deviceSn;

    @ApiModelProperty(value = "外部辅助码")
    private String auxiliaryCode;

    @ApiModelProperty("车牌号")
    private String truckNo;

    @ApiModelProperty("材料名称")
    private String material;

    @ApiModelProperty("重量")
    private BigDecimal weight;

    @ApiModelProperty("称重单位")
    private String unit;

    @ApiModelProperty("1 载车称重 2 净货称重")
    private Byte type;

    @ApiModelProperty("称重类型")
    private String typeStr;

    @ApiModelProperty("称重日期")
    private String weighDate;

    @ApiModelProperty("称重时间")
    private String weighTime;

    @ApiModelProperty("上传时间")
    private String gmtCreate;

    @ApiModelProperty("使用车牌识别事后checkout的车牌号")
    private String lprTruckNo;

    @ApiModelProperty("风险等级")
    private String riskGrade;

    @ApiModelProperty("推送状态 1 未推送 2 队列中 3 已推送")
    private Byte pushStatus;

    @ApiModelProperty("是否已组装")
    private String isUsed;
}
