package cn.pinming.microservice.material.client.management.controller.common;

import cn.pinming.microservice.material.client.management.infrastructure.exception.BizErrorException;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizExceptionMessageEnum;
import cn.pinming.microservice.material.client.management.infrastructure.response.SingleResponse;
import cn.pinming.microservice.material.client.management.service.biz.IShortLinkService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * 短链接服务
 */
@Api(value = "shortLink", tags = {"shortLink"})
@RestController
@RequestMapping("/api/s")
public class RedirectController {

    @Resource
    private IShortLinkService shortLinkService;

    @ApiOperation(value = "短连接跳转")
    @GetMapping("/{shortLink}")
    public SingleResponse<?> redirect(@PathVariable String shortLink, HttpServletResponse response) {
        String longLink = shortLinkService.getLongLink(shortLink);
        if (longLink != null) {
//            response.setStatus(HttpServletResponse.SC_MOVED_PERMANENTLY);
//            response.setHeader("Location", longLink);
            return SingleResponse.of(longLink);
        } else {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "短连接不存在或已失效");
        }
    }
}
