package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.pinming.microservice.material.client.management.infrastructure.util.UserIdUtil;
import cn.pinming.microservice.material.client.management.common.form.UserBusinessConfigForm;
import cn.pinming.microservice.material.client.management.common.mapper.UserBusinessConfigMapper;
import cn.pinming.microservice.material.client.management.common.model.UserBusinessConfigDO;
import cn.pinming.microservice.material.client.management.service.biz.UserBusinessConfigService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
public class UserBusinessConfigServiceImpl extends ServiceImpl<UserBusinessConfigMapper, UserBusinessConfigDO> implements UserBusinessConfigService {
    @Resource
    private UserIdUtil userIdUtil;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void appPushConfig(UserBusinessConfigForm form) {
        UserBusinessConfigDO one = this.lambdaQuery().eq(UserBusinessConfigDO::getUid, userIdUtil.getUId()).one();
        if (ObjectUtil.isNotNull(one)) {
            form.setId(one.getId());
        }

        UserBusinessConfigDO configDO = new UserBusinessConfigDO();
        BeanUtils.copyProperties(form, configDO);
        if (form.getPushType() == 2) {
            // 一般推送自动关闭推送开关
            configDO.setWeighDataPushStatus(1);
            configDO.setWeighPicPushStatus(1);
        }
        configDO.setUid(userIdUtil.getUId());
        this.saveOrUpdate(configDO);
    }

    @Override
    public UserBusinessConfigDO getByUid(String uid) {
        return this.lambdaQuery().eq(UserBusinessConfigDO::getUid, uid).one();
    }
}
