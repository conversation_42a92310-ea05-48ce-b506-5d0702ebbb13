package cn.pinming.microservice.material.client.management.common.mapper.ext;

import cn.pinming.microservice.material.client.management.common.dto.WeighConfirmPullDTO;
import cn.pinming.microservice.material.client.management.common.dto.push.PushConfirmDbDTO;
import cn.pinming.microservice.material.client.management.common.model.WeighDataConfirmDO;
import cn.pinming.microservice.material.client.management.common.query.ConfirmPullQuery;
import cn.pinming.microservice.material.client.management.common.query.WeighDataLocalQuery;
import cn.pinming.microservice.material.client.management.common.query.WeighDataPageQuery;
import cn.pinming.microservice.material.client.management.common.vo.WeighDataConfirmDetailDTO;
import cn.pinming.microservice.material.client.management.common.vo.WeighDataConfirmVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 现场称重确认单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-22
 */
public interface WeighDataConfirmExtMapper {

    IPage<WeighDataConfirmVO> col(@Param("query") WeighDataPageQuery query);

    List<PushConfirmDbDTO> getConfirmPushData(@Param("ids") List<Long> id);

    List<WeighDataConfirmDO> selectConfirmToPush(@Param("uid") String uid, @Param("attributionIdList") Set<Long> attributionIdList,@Param("supplierIdList") List<String> supplierList);

    List<WeighDataConfirmDO> selectConfirmToRecycle();

    IPage<WeighDataConfirmDetailDTO> confirmQuery(@Param("uid") String uid,@Param("query") WeighDataLocalQuery query);

    IPage<WeighConfirmPullDTO> getConfirm(@Param("query")ConfirmPullQuery query);
}
