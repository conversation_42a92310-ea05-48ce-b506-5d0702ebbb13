package cn.pinming.microservice.material.client.management.common.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ConsumerVO implements Serializable {
    private static final long serialVersionUID = 8496776548124710223L;

    @ApiModelProperty(value = "租户id")
    private String uid;

    @ApiModelProperty(value = "租户名称")
    private String userName;

    @ApiModelProperty(value = "用户id")
    private Long consumeId;

    @ApiModelProperty(value = "用户名称")
    private String consumeName;

    @ApiModelProperty(value = "关联归属方id")
    private String attributions;

    @ApiModelProperty(value = "appKey")
    private String appKey;

    @ApiModelProperty(value = "appSecretKey")
    private String appSecretKey;

    @ApiModelProperty(value = "归属方列表")
    private List<AttributionExtConfigVO> list;
}
