package cn.pinming.microservice.material.client.management.common.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2023/10/31 13:33
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AnprDTO {

//    @ApiModelProperty("车牌")
//    private String truckNo;

    @ApiModelProperty("得分")
    private BigDecimal score;

    @ApiModelProperty("识别的车牌")
    private String plateNo;


}
