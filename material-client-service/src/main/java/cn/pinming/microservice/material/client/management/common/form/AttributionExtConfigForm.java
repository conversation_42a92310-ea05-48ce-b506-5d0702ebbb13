package cn.pinming.microservice.material.client.management.common.form;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class AttributionExtConfigForm {
    @ApiModelProperty(value = "归属方id")
    private Long attributionId;

    @ApiModelProperty(value = "租户id")
    @NotNull
    private String uid;

    @ApiModelProperty(value = "订阅到期日")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "appId")
    @NotNull
    private Long appId;

    @ApiModelProperty(value = "是否允许手动输入 1 允许 2 不允许")
    private Byte isInputEnable;
}
