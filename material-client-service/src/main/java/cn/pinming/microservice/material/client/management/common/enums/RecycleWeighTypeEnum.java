package cn.pinming.microservice.material.client.management.common.enums;


import lombok.Getter;

import java.util.Arrays;

@Getter
public enum RecycleWeighTypeEnum {
    //过磅类型(1-收料、2-发料)
    RECEIVE(1, "收料"),
    MATERIAL(2, "发料"),
    ;

    private final Integer value;
    private final String desc;

    RecycleWeighTypeEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static RecycleWeighTypeEnum getByValue(Integer value) {
        return Arrays.stream(values()).filter(e -> e.getValue().equals(value)).findFirst().orElse(null);
    }

}
