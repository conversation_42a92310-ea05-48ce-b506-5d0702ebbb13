package cn.pinming.microservice.material.client.management.common.query;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class WeighDataLocalQuery extends BasePageQuery {
    /**
     * 记录ID集合(最大1000条)
     */
    private List<String> ids;

    /**
     * 称重开始时间
     */
    private LocalDateTime startTime;

    /**
     * 称重结束时间
     */
    private LocalDateTime endTime;

    /**
     * 数据归属方code
     */
    private String attributionCode;

    /**
     * 车牌号
     */
    private String truckNo;

    /**
     * 设备机器码
     */
    private String deviceSn;

    /**
     * 确认单类型 1 仅毛皮重
     */
    private Integer confirmType;

    /**
     * 收发料类型 1 收料 2 发料
     */
    private Integer weighType;

    /**
     * 车牌号列表(最大500条)
     */
    private List<String> truckNos;

}
