package cn.pinming.microservice.material.client.management.common.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ReceiptRecycleDetailsVO extends ReceiptRecycleVO implements Serializable {

    @ApiModelProperty("称重状态（2-回收成功、3-回收失败）")
    private Integer weighStatus;

    @ApiModelProperty("模板状态（2-回收成功、3-回收失败）")
    private Integer moduleStatus;

    @ApiModelProperty("称重数据")
    private List<ReceiptRecycleWeighVO> weighList;

    @ApiModelProperty("模板数据组")
    private List<ReceiptRecycleModuleVO> moduleList;

}
