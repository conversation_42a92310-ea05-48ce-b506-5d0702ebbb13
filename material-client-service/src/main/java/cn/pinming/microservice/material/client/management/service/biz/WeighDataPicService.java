package cn.pinming.microservice.material.client.management.service.biz;

import cn.pinming.microservice.material.client.management.common.model.WeighDataPicDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

public interface WeighDataPicService extends IService<WeighDataPicDO> {
    long queryTodayUsedSpaceByUid(String uid);

    /**
     * 得到图片地址
     *
     * @param isPre true 预览地址  false 真实地址
     */
    Map<String, List<String>> getPic(List<String> recordList, Boolean isPre);

    Map<String, List<String>> getUuid(List<String> recordList);
}
