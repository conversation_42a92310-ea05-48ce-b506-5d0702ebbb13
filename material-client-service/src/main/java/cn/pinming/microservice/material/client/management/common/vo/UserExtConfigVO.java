package cn.pinming.microservice.material.client.management.common.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class UserExtConfigVO implements Serializable {

    @ApiModelProperty("是否启用一二磅时间间隔（0-启用、1-禁用）")
    private Byte isTimeInterval;

    @ApiModelProperty(value = "单据回收-间隔时间 开始")
    private Integer recycleStart;

    @ApiModelProperty(value = "单据回收-间隔时间 结束")
    private Integer recycleEnd;

    @ApiModelProperty("是否启用忽略一二磅之间是否存在同车牌过磅记录（0-启用、1-禁用）")
    private Byte isIdenticalLicensePlate;

    @ApiModelProperty("是否启用忽略记录车牌号与系统识别车牌号是否不一致（0-启用、1-禁用）")
    private Byte isLicensePlateInconsistent;

    @ApiModelProperty("是否启用不检查待组装记录时间段内同车牌是否存在过磅记录（0-启用、1-禁用）")
    private Byte isCheckDataCombine;

    @ApiModelProperty("单据匹配方式 :  0 - 区域坐标匹配（旧） 1 - 索引匹配（新）")
    private Byte billMatchType;

    @ApiModelProperty("是否启用OCR识别（0-关闭、1-开启）")
    private Integer ocrEnable;

    @ApiModelProperty("百度AK")
    private String clientId;

    @ApiModelProperty("百度SK")
    private String clientSecret;

}
