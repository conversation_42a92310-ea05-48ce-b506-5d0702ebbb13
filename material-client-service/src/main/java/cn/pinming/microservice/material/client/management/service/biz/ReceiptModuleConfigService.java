package cn.pinming.microservice.material.client.management.service.biz;

import cn.pinming.microservice.material.client.management.common.form.ReceiptModuleConfigForm;
import cn.pinming.microservice.material.client.management.common.model.ReceiptModuleConfigDO;
import com.baomidou.mybatisplus.extension.service.IService;

public interface ReceiptModuleConfigService extends IService<ReceiptModuleConfigDO> {

    void receiptModuleConfigSave(ReceiptModuleConfigForm form);

    Integer getOcrType(Long moduleId);
}
