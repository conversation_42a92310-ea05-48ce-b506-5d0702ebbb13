package cn.pinming.microservice.material.client.management.common.vo.delivery;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2024/8/22 17:18
 */
@Data
public class DeliveryItemDTO {

    @ApiModelProperty(value = "订单明细ID")
    private Long id;

    @ApiModelProperty(value = "货物名称")
    private String name;

    @ApiModelProperty(value = "货物规格")
    private String spec;

    @ApiModelProperty(value = "其他参数")
    private String argument;

    @ApiModelProperty(value = "品牌")
    private String brand;

    @ApiModelProperty(value = "客户下单数量")
    private BigDecimal orderAmount;

    @ApiModelProperty(value = "累计发货数量/本车发货数量")
    private BigDecimal sendAmount;

    @ApiModelProperty(value = "累计发货车次")
    private Integer truckAmount;

    @ApiModelProperty(value = "本次发货数量")
    private BigDecimal currentAmount;

    @ApiModelProperty(value = "采购单位")
    private String unit;

    @ApiModelProperty(value = "计划使用部位")
    private String position;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty("运单明细 1 在途，2 待确认，3 自动确认中，4 已确认，5 已作废")
    private Integer status;
}
