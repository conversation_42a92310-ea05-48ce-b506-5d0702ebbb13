package cn.pinming.microservice.material.client.management.infrastructure.util;

import cn.hutool.core.util.StrUtil;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizErrorException;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizExceptionMessageEnum;
import org.springframework.stereotype.Component;

@Component
public class UserIdUtil {
    /**
     * 获取当前登录用户id
     *
     * @return
     */
    public String getUId() {
        String loginId = null;
        try {
            loginId = (String) StpKit.DEFAULT.getLoginId();
        } catch (Exception ignored) {
        }
        try {
            loginId = (String) StpKit.MANAGER.getLoginId();
        } catch (Exception ignored) {
        }

        if (StrUtil.isBlank(loginId)) {
            throw new BizErrorException(BizExceptionMessageEnum.NOT_LOGIN_ERROR);
        }
        return loginId;
    }
}
