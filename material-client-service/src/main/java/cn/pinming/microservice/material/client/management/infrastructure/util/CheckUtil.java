package cn.pinming.microservice.material.client.management.infrastructure.util;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.microservice.material.client.management.common.enums.DeveloperAppEnum;
import cn.pinming.microservice.material.client.management.common.enums.DeveloperTypeEnum;
import cn.pinming.microservice.material.client.management.common.enums.RiskGradeEnum;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizErrorException;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizExceptionMessageEnum;
import cn.pinming.microservice.material.client.management.common.mapper.ext.DeviceBindingExtMapper;
import cn.pinming.microservice.material.client.management.common.mapper.ext.DeviceExtMapper;
import cn.pinming.microservice.material.client.management.common.model.DeveloperDO;
import cn.pinming.microservice.material.client.management.common.model.DeviceBindingDO;
import cn.pinming.microservice.material.client.management.common.model.DeviceDO;
import cn.pinming.microservice.material.client.management.common.model.WeighDataDO;
import cn.pinming.microservice.material.client.management.service.biz.DeveloperService;
import cn.pinming.microservice.material.client.management.service.biz.DeviceBindingService;
import cn.pinming.microservice.material.client.management.service.biz.DeviceService;
import cn.pinming.microservice.material.client.management.service.biz.UserConfigService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

@Component
public class CheckUtil {
    @Resource
    private DeviceService deviceService;
    @Resource
    private DeveloperService developerService;
    @Resource
    private DeviceBindingExtMapper deviceBindingExtMapper;
    @Resource
    private DeviceExtMapper deviceExtMapper;
    @Resource
    private DeviceBindingService deviceBindingService;
    @Resource
    private UserConfigService userConfigService;

    /**
     * 基石服务校验-购买,停用,续费(已包含服务到期时间)
     *
     * @param uId     用户id
     * @param appEnum 服务
     */
    public void appCheck(String uId, DeveloperAppEnum appEnum) {
        DeveloperDO developerDO = developerService.lambdaQuery()
                .eq(DeveloperDO::getCreateId, uId)
                .eq(DeveloperDO::getAppId, appEnum.value())
                .one();
        if (ObjectUtil.isNull(developerDO)) {
            throw new BizErrorException(BizExceptionMessageEnum.SIGN_UP_APP);
        }
        if (developerDO.getType().equals(DeveloperTypeEnum.STOP.value())) {
            throw new BizErrorException(BizExceptionMessageEnum.SERVICE_DISABLED, appEnum.description());
        }
        if (userConfigService.getApiRemainingCount(uId) <= 0) {
            throw new BizErrorException(BizExceptionMessageEnum.APP_INVOKE_EMPTY);
        }

    }

    public void riskCheck(String uId, DeveloperAppEnum appEnum, List<WeighDataDO> weighDataList) {
        WeighDataDO first = weighDataList.get(0);
        WeighDataDO second = weighDataList.get(1);
        // 风险等级校验
        boolean riskGradeAssert = false;
        String highRisk = RiskGradeEnum.HIGH.name();
        String middleRisk = RiskGradeEnum.MIDDLE.name();
        DeveloperDO developerDO = developerService.lambdaQuery()
                .eq(DeveloperDO::getCreateId, uId)
                .eq(DeveloperDO::getAppId, appEnum.value())
                .one();
        // 默认高风险
        String riskGrade = ObjectUtil.isNotNull(developerDO) ? developerDO.getRiskGrade() : "";
        if (riskGrade.equals(highRisk)) {
            riskGradeAssert = weighDataList.stream().anyMatch(item -> Optional.ofNullable(item.getRiskGrade()).orElse("").equals(highRisk));
        } else if (riskGrade.equals(middleRisk)) {
            riskGradeAssert = weighDataList.stream().anyMatch(item -> Optional.ofNullable(item.getRiskGrade()).orElse("").equals(highRisk) ||
                    Optional.ofNullable(item.getRiskGrade()).orElse("").equals(middleRisk));
        }
        if (riskGradeAssert) {
            String firstRisk = first.getRiskGrade();
            String secondRisk = second.getRiskGrade();
            String firstRiskZh = firstRisk.equals(highRisk) ? "高" : (firstRisk.equals(middleRisk) ? "中" : "低");
            String secondRiskZh = secondRisk.equals(highRisk) ? "高" : (secondRisk.equals(middleRisk) ? "中" : "低");
            throw new BizErrorException("-2000", "记录一(" + first.getRecordId() + ")为" + firstRiskZh + "风险等级、记录二(" + second.getRecordId() +
                    ")为" + secondRiskZh + "风险等级，存在作弊风险，不能组装！");
        }
    }

    /**
     * 设备占有校验
     */
    public void deviceOccupiedCheck(String deviceSn, String deviceType) {
        // 是否被他人占有
        String userName = deviceExtMapper.occupiedCheck(deviceSn, deviceType);
        if (StrUtil.isNotBlank(userName)) {
            throw new BizErrorException("-20", "该设备正在被租户" + userName + "占有，需对方移除设备后才可添加。");
        }
    }

    /**
     * 设备归属校验
     *
     * @param deviceSn
     */
    public void deviceAttributionCheck(DeviceDO deviceSn) {
        String name = deviceBindingExtMapper.appAttributionCheck(deviceSn.getDeviceSn(), deviceSn.getDeviceType());
        if (StrUtil.isNotBlank(name)) {
            throw new BizErrorException("-21", "该设备已与归属方" + name + "绑定，请先解除绑定再操作。");
        }
    }

    /**
     * 设备占有校验
     */
    public Long deviceOccupiedByUid(Long deviceId, String uId) {
        DeviceBindingDO one = deviceBindingService.lambdaQuery()
                .eq(DeviceBindingDO::getDeviceId, deviceId)
                .one();
        if (ObjectUtil.isNotNull(one) && !one.getUid().equals(uId)) {
            throw new BizErrorException(BizExceptionMessageEnum.DEVICE_IS_OTHERS);
        }
        if (ObjectUtil.isNull(one)) {
            throw new BizErrorException(BizExceptionMessageEnum.DEVICE_HAS_NO_OCCUPIED);
        }
        return one.getId();
    }
}
