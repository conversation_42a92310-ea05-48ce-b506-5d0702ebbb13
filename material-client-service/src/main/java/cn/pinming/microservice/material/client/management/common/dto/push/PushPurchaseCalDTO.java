package cn.pinming.microservice.material.client.management.common.dto.push;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 基石转发订单数据体给第三方json结构体-称重计算参数
 */
@Data
public class PushPurchaseCalDTO {
    @ApiModelProperty(value = "扣水扣杂比例，取值范围为0-1，不能为空")
    private BigDecimal deductRatio;

    @ApiModelProperty(value = "该货物的称重换算系数，如2.334意思为1立方米 = 2.334吨，不能为空，数字类型")
    private BigDecimal scaleFactor;

    @ApiModelProperty(value = "用于设置该货物称重换算系数时，参照的重量单位：0 = 千克；1 = 吨，不能为空")
    private Integer weightUnitForReference;
}
