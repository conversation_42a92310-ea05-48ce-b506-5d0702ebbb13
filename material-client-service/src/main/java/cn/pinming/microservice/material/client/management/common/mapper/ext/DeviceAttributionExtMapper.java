package cn.pinming.microservice.material.client.management.common.mapper.ext;

import cn.pinming.microservice.material.client.management.common.model.DeviceAttributionDO;
import cn.pinming.microservice.material.client.management.common.vo.AttributionDeviceVO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface DeviceAttributionExtMapper {

    Long getDeviceAttributionIdByCode(@Param("uid") String uid, @Param("code") String code);

    List<DeviceAttributionDO> getDeviceAttributionsByIds(@Param("ids") Collection<Long> ids);

    List<AttributionDeviceVO> listAttributionDevice(String uid, String type, Long attributionId);

    DeviceAttributionDO getDeviceAttributionsByCode(String code, String uid);
}
