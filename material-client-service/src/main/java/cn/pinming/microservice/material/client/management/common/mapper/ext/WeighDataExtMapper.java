package cn.pinming.microservice.material.client.management.common.mapper.ext;

import cn.pinming.microservice.material.client.management.common.dto.ExpirePicFixDTO;
import cn.pinming.microservice.material.client.management.common.dto.WeighDataPullDTO;
import cn.pinming.microservice.material.client.management.common.dto.WeighPushDataDTO;
import cn.pinming.microservice.material.client.management.common.dto.WeighSimpleDTO;
import cn.pinming.microservice.material.client.management.common.dto.push.PushDTO;
import cn.pinming.microservice.material.client.management.common.dto.push.PushWeighDataDTO;
import cn.pinming.microservice.material.client.management.common.model.ext.WeighDataExtDO;
import cn.pinming.microservice.material.client.management.common.query.WeighDataLocalQuery;
import cn.pinming.microservice.material.client.management.common.query.WeighDataPageQuery;
import cn.pinming.microservice.material.client.management.common.query.WeighPullQuery;
import cn.pinming.microservice.material.client.management.common.vo.WeighDataConfirmDetailDTO;
import cn.pinming.microservice.material.client.management.common.vo.WeighDataDetailVO;
import cn.pinming.microservice.material.client.management.common.vo.WeighDataVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

public interface WeighDataExtMapper {

    List<WeighDataExtDO> list();

    WeighDataDetailVO getByRecordId(@Param("recordId") String recordId);

    List<WeighDataDetailVO> listByRecordId(@Param("recordIdList") List<String> recordIdList);

    IPage<WeighDataVO> select(@Param("uId") String uId,@Param("query") WeighDataPageQuery query);

    WeighDataVO card(@Param("weighDataId") String weighDataId);

    WeighPushDataDTO needPushWeighData(@Param("recordId") String recordId);

    List<WeighSimpleDTO> needPushWeighDataS(@Param("uid") String uid, @Param("attributionList") List<Long> attributionList);

    List<PushDTO> getPushDatas(@Param("attributionList") List<String> attributionIds);

    Long countByDeviceSn(@Param("deviceSn") String deviceSn, @Param("attributionId") Long attributionId);

    List<ExpirePicFixDTO> expirePicFix(@Param("list") List<String> records);

    IPage<WeighDataConfirmDetailDTO> dataQuery(@Param("uid")String uid,@Param("query") WeighDataLocalQuery query);

    IPage<WeighDataPullDTO> getDataPull(@Param("query") WeighPullQuery query);

    List<PushWeighDataDTO> selectDataToPush(@Param("uid")String uid, @Param("attributionList") Set<Long> attributionCodeList);
}
