package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 原始记录称重曲线
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("d_weigh_curve")
public class WeighCurveDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 用户id
     */
    private String uid;

    /**
     * 归属方主键id
     */
    private Long attributionId;

    /**
     * 设备机器码
     */
    private String deviceSn;

    /**
     * 终端记录id
     */
    private String recordId;

    /**
     * 终端称重曲线id
     */
    private String weighCurveId;

    /**
     * 称重曲线数据
     */
    private String weighCurveData;

    /**
     * 称重单位
     */
    private String weighUnit;

    /**
     * 终端称重曲线创建时间
     */
    private LocalDateTime createTime;

    /**
     * 推送状态
     * 1 未推送
     * 2 已推送
     * 3 推送失败
     */
    private Integer pushState;
}
