package cn.pinming.microservice.material.client.management.common.form.supplier;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class PrintTemplateContentForm {

    @ApiModelProperty(value = "模板ID")
    @NotNull(message = "模板ID为空")
    private Long id;

    @ApiModelProperty(value = "内容")
    @NotBlank(message = "模板内容为空")
    private String content;

}
