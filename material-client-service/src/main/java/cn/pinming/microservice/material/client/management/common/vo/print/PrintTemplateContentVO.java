package cn.pinming.microservice.material.client.management.common.vo.print;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PrintTemplateContentVO {

    @ApiModelProperty(value = "模板ID")
    private Long id;

    @ApiModelProperty(value = "模板名")
    private String name;

    @ApiModelProperty(value = "模板内容")
    private String content;

    @ApiModelProperty("打印业务类型")
    private Byte type;
}
