package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.hutool.core.util.ObjUtil;
import cn.pinming.microservice.material.client.management.common.enums.IsEnableEnum;
import cn.pinming.microservice.material.client.management.common.form.UserExtConfigForm;
import cn.pinming.microservice.material.client.management.common.mapper.UserExtConfigMapper;
import cn.pinming.microservice.material.client.management.common.model.UserExtConfigDO;
import cn.pinming.microservice.material.client.management.common.vo.UserExtConfigVO;
import cn.pinming.microservice.material.client.management.infrastructure.util.UserIdUtil;
import cn.pinming.microservice.material.client.management.service.biz.UserExtConfigService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class UserExtConfigServiceImpl extends ServiceImpl<UserExtConfigMapper, UserExtConfigDO> implements UserExtConfigService {
    @Resource
    private UserIdUtil userIdUtil;

    @Override
    @Transactional
    public void init(String uid) {
        UserExtConfigDO userExtConfigDO = this.lambdaQuery().eq(UserExtConfigDO::getUid, uid).one();
        if (ObjUtil.isNotNull(userExtConfigDO)) {
            return;
        }
        userExtConfigDO = new UserExtConfigDO();
        userExtConfigDO.setUid(uid);
        userExtConfigDO.setIsTimeInterval(IsEnableEnum.ENABLE.value());
        userExtConfigDO.setRecycleStart(15);
        userExtConfigDO.setRecycleEnd(60);
        userExtConfigDO.setIsIdenticalLicensePlate(IsEnableEnum.DISABLE.value());
        userExtConfigDO.setIsLicensePlateInconsistent(IsEnableEnum.DISABLE.value());
        userExtConfigDO.setIsCheckDataCombine(IsEnableEnum.DISABLE.value());
        this.save(userExtConfigDO);
    }

    @Override
    @Transactional
    public void updateUserExtConfig(UserExtConfigForm form) {
        UserExtConfigDO userExtConfigDO = this.lambdaQuery().eq(UserExtConfigDO::getUid, userIdUtil.getUId()).one();
        if (ObjUtil.isNull(userExtConfigDO)) {
            userExtConfigDO = new UserExtConfigDO();
            userExtConfigDO.setUid(userIdUtil.getUId());
            userExtConfigDO.setBillMatchType((byte) 0);
        }
        BeanUtils.copyProperties(form, userExtConfigDO);
        this.saveOrUpdate(userExtConfigDO);
    }

    @Override
    public UserExtConfigVO showUserExtConfig() {
        UserExtConfigDO one = this.lambdaQuery()
                .eq(UserExtConfigDO::getUid, userIdUtil.getUId())
                .one();
        if (ObjUtil.isNull(one)) {
            return null;
        }
        UserExtConfigVO vo = new UserExtConfigVO();
        BeanUtils.copyProperties(one, vo);
        return vo;
    }

    @Override
    public Byte getBillMatchType() {
        UserExtConfigDO userExtConfigDO = this.lambdaQuery().eq(UserExtConfigDO::getUid, userIdUtil.getUId()).one();
        if (ObjUtil.isNull(userExtConfigDO) || userExtConfigDO.getBillMatchType() == null) {
            return 0;
        } else {
            return userExtConfigDO.getBillMatchType();
        }
    }

}
