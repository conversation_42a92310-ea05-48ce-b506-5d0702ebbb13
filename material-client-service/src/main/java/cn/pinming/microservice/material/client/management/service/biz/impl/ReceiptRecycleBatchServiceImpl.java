package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizErrorException;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizExceptionMessageEnum;
import cn.pinming.microservice.material.client.management.infrastructure.util.UserIdUtil;
import cn.pinming.microservice.material.client.management.common.form.ReceiptRecycleBatchEditForm;
import cn.pinming.microservice.material.client.management.common.form.ReceiptRecycleBatchForm;
import cn.pinming.microservice.material.client.management.common.form.ReceiptRecycleDataBatchEditForm;
import cn.pinming.microservice.material.client.management.common.mapper.ReceiptRecycleBatchMapper;
import cn.pinming.microservice.material.client.management.common.model.ReceiptRecycleBatchDO;
import cn.pinming.microservice.material.client.management.common.model.ReceiptRecycleDO;
import cn.pinming.microservice.material.client.management.common.vo.ReceiptRecycleBatchVO;
import cn.pinming.microservice.material.client.management.service.biz.IReceiptRecycleBatchService;
import cn.pinming.microservice.material.client.management.service.biz.ReceiptRecycleService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 单据回收批次表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-11
 */
@Service
public class ReceiptRecycleBatchServiceImpl extends ServiceImpl<ReceiptRecycleBatchMapper, ReceiptRecycleBatchDO> implements IReceiptRecycleBatchService {

    @Resource
    private UserIdUtil userIdUtil;
    @Resource
    private ReceiptRecycleService receiptRecycleService;

    @Override
    public List<ReceiptRecycleBatchVO> listByQuery(String keyword) {
        List<ReceiptRecycleBatchVO> list = getBaseMapper().selectByQuery(userIdUtil.getUId(), keyword);
        if (CollUtil.isNotEmpty(list)) {
            list.forEach(item -> {
                if (StrUtil.isNotBlank(item.getDeviceId())) {
                    item.setDeviceIdList(StrUtil.split(item.getDeviceId(), ',', -1, true, Long::parseLong));
                }
            });
        }
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(ReceiptRecycleBatchForm form) {
        //查询是否重复
        int count = lambdaQuery().eq(ReceiptRecycleBatchDO::getName, form.getName())
                .eq(ReceiptRecycleBatchDO::getCreateId, userIdUtil.getUId()).count();
        if (count > 0) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "批次名称已存在");
        }
        ReceiptRecycleBatchDO entity = new ReceiptRecycleBatchDO();
        BeanUtils.copyProperties(form, entity);
        save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(Long id) {
        // 校验 回收数据量为0
        Integer count = receiptRecycleService.lambdaQuery().eq(ReceiptRecycleDO::getBatchId, id)
                .eq(ReceiptRecycleDO::getUid, userIdUtil.getUId()).count();
        if (count > 0) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "该批次下已存在回收数据,不能删除");
        }
        removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void archiveById(Long id) {
        ReceiptRecycleBatchDO recycleBatch = getById(id);
        if (recycleBatch == null) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "批次不存在");
        }
        Integer count = receiptRecycleService.lambdaQuery().eq(ReceiptRecycleDO::getBatchId, id)
                .eq(ReceiptRecycleDO::getUid, userIdUtil.getUId()).count();
        if (count == 0) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "该批次下回收数据量为0,无法归档");
        }
        if (recycleBatch.getStatus() != 0) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "该批次已归档");
        }
        lambdaUpdate().set(ReceiptRecycleBatchDO::getStatus, 1).set(ReceiptRecycleBatchDO::getDeviceId, StrUtil.EMPTY)
                .eq(ReceiptRecycleBatchDO::getId, id).update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void extCodeEdit(ReceiptRecycleBatchEditForm form) {
        Long id = form.getId();
        String extCode = form.getExtCode();

        ReceiptRecycleBatchDO recycleBatch = getById(id);
        if (recycleBatch == null) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "批次不存在");
        }
        if (recycleBatch.getStatus() != 0) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "该批次已归档");
        }
        lambdaUpdate().set(ReceiptRecycleBatchDO::getExtCode, extCode)
                .eq(ReceiptRecycleBatchDO::getId, id).update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deviceBindEdit(ReceiptRecycleBatchEditForm form) {
        Long id = form.getId();
        List<Long> deviceIdList = form.getDeviceIdList();
        ReceiptRecycleBatchDO recycleBatch = getById(id);
        if (recycleBatch == null) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "批次不存在");
        }
        if (recycleBatch.getStatus() != 0) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "该批次已归档");
        }
        // 更新绑定的设备id
        lambdaUpdate().set(ReceiptRecycleBatchDO::getDeviceId, StrUtil.join(StrUtil.COMMA, deviceIdList))
                .eq(ReceiptRecycleBatchDO::getId, id).update();

        // 查询其他批次是否绑定该设备
        if (CollUtil.isNotEmpty(form.getDeviceIdList())) {
            List<ReceiptRecycleBatchDO> list = getBaseMapper().selectDeviceIdListByBatchId(id, form.getDeviceIdList(), userIdUtil.getUId());
            if (CollUtil.isNotEmpty(list)) {
                List<ReceiptRecycleBatchDO> configList = list.stream().map(obj -> {
                    String deviceId = obj.getDeviceId();
                    List<Long> existIdList = StrUtil.split(deviceId, StrUtil.COMMA).stream().filter(StrUtil::isNotBlank).mapToLong(Long::parseLong).boxed().collect(Collectors.toList());
                    existIdList.removeAll(deviceIdList);
                    obj.setId(obj.getId());
                    obj.setDeviceId(StrUtil.join(StrUtil.COMMA, existIdList));
                    return obj;
                }).collect(Collectors.toList());
                updateBatchById(configList);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void dataBatchEdit(ReceiptRecycleDataBatchEditForm form) {
        Long id = form.getId();
        Long batchId = form.getBatchId();
        ReceiptRecycleDO receiptRecycle = receiptRecycleService.getById(id);
        if (receiptRecycle == null) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "回收日志不存在");
        }

        if (batchId != null) {
            ReceiptRecycleBatchDO recycleBatchDO = getById(batchId);
            if (recycleBatchDO == null) {
                throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "批次不存在");
            }
            if (recycleBatchDO.getStatus() != 0) {
                throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "该批次已归档");
            }
        }

        // 更新回收批次
        receiptRecycleService.lambdaUpdate().set(ReceiptRecycleDO::getBatchId, batchId)
                .eq(ReceiptRecycleDO::getId, id).update();
    }

    @Override
    public ReceiptRecycleBatchDO checkDeviceId(Long deviceId, String uid) {
        return getBaseMapper().selectBatchByDeviceId(deviceId, uid);
    }
}
