package cn.pinming.microservice.material.client.management.common.model.ext;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class WeighDataTareExtDO {
    @ApiModelProperty(value = "基石称重记录id",required = true)
    private String originalId;

    @ApiModelProperty(value = "磅序 1-一磅；2-二磅",required = true)
    private Integer sort;

    @ApiModelProperty(value = "重量值",required = true)
    private BigDecimal weightValue;

    @ApiModelProperty(value = "过磅时间",required = true)
    private String weighTime;

    @ApiModelProperty(value = "过磅照片",required = true)
    private List<String> photoUrls;
}
