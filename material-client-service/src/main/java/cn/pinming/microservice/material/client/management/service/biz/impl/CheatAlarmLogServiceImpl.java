package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.pinming.microservice.material.client.management.common.dto.WeighDataCheckDTO;
import cn.pinming.microservice.material.client.management.common.enums.DeveloperAppEnum;
import cn.pinming.microservice.material.client.management.common.enums.DeviceTypeEnum;
import cn.pinming.microservice.material.client.management.common.form.AlarmFrameForm;
import cn.pinming.microservice.material.client.management.common.mapper.CheatAlarmLogMapper;
import cn.pinming.microservice.material.client.management.common.model.CheatAlarmLogDO;
import cn.pinming.microservice.material.client.management.service.biz.ICheatAlarmLogService;
import cn.pinming.microservice.material.client.management.service.biz.WeighDataService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.Set;

/**
 * <p>
 * 地磅作弊报警日志表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-08
 */
@Slf4j
@Service
public class CheatAlarmLogServiceImpl extends ServiceImpl<CheatAlarmLogMapper, CheatAlarmLogDO> implements ICheatAlarmLogService {

    @Resource
    private WeighDataService weighDataService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveAlarmFrame(AlarmFrameForm form) {
        String deviceSn = form.getDeviceSn();
        String deviceType = form.getDeviceType();
        log.error("AlarmFrameForm:{}", JSONUtil.toJsonStr(form));
        WeighDataCheckDTO dto = weighDataService.check(deviceSn, DeveloperAppEnum.UPLOAD.value(),
                false, StrUtil.isNotBlank(deviceType) ? deviceType : DeviceTypeEnum.WEIGH.name());

        CheatAlarmLogDO cheatAlarmLog = getById(form.getId());
        if (cheatAlarmLog == null) {
            cheatAlarmLog = new CheatAlarmLogDO();
        }
        BeanUtils.copyProperties(form, cheatAlarmLog, "fileId");

        String fileId = form.getFileId();
        if (StrUtil.isNotBlank(fileId)) {
            String alarmLogFileId = cheatAlarmLog.getFileId();
            if (StrUtil.isNotBlank(alarmLogFileId)) {
                Set<String> fileIdSet = new HashSet<>(StrUtil.split(alarmLogFileId, StrUtil.COMMA));
                fileIdSet.add(fileId);
                cheatAlarmLog.setFileId(StrUtil.join(StrUtil.COMMA, fileIdSet));
            } else {
                cheatAlarmLog.setFileId(fileId);
            }
        }
        cheatAlarmLog.setAttributionId(dto.getAttributionId());
        cheatAlarmLog.setUid(dto.getUid());
        saveOrUpdate(cheatAlarmLog);
    }
}
