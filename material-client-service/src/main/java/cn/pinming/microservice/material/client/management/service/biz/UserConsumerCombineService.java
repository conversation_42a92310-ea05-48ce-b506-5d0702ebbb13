package cn.pinming.microservice.material.client.management.service.biz;

import cn.pinming.microservice.material.client.management.common.model.UserConsumerCombineDO;
import cn.pinming.microservice.material.client.management.common.vo.ConsumerVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface UserConsumerCombineService extends IService<UserConsumerCombineDO> {
    List<ConsumerVO> getInfoByPhoneSn(String phoneSn);

    String loginByPhoneSn(String phoneSn, String uId, Long attributionId);
}
