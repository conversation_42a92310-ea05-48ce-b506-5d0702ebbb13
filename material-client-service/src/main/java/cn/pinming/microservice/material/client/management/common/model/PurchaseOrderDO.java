package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 采购单(订单)主表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("d_purchase_order")
public class PurchaseOrderDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 归属方id
     */
    private Long attributionId;

    /**
     * 业务系统订单id
     */
    private String orderExtId;

    /**
     * 外部系统供应商id
     */
    private String supplierExtId;

    /**
     * 供应商ID(租户id)
     */
    private String supplierId;

    /**
     * 计划使用部位
     */
    private String position;

    /**
     * 收货项目
     */
    private String project;

    /**
     * 收货地址
     */
    private String address;

    /**
     * 收货人姓名
     */
    private String receiver;

    /**
     * 收货人电话
     */
    private String mobile;

    /**
     * 要货日期
     */
    private LocalDate receiveDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 采购单状态 1 启用 2 弃用
     */
    private Integer status;

    /**
     * 推送状态  1 未推送 2 已推送
     */
    private Integer pushStatus;

    /**
     * 订单来源 品茗拌合站 品茗采购单 品茗合同
     */
    private String source;

    /**
     * 发货状态 1 待发货 2 发货中 3 发货完毕
     */
    private Integer deliveryStatus;

    /**
     * 同步时间
     */
    private LocalDateTime syncTime;

}
