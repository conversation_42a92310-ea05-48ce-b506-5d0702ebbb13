package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.pinming.microservice.material.client.management.common.mapper.PrintTemplateConfigMapper;
import cn.pinming.microservice.material.client.management.common.model.PrintTemplateConfigDO;
import cn.pinming.microservice.material.client.management.service.biz.IPrintTemplateConfigService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 打印模板设置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-29
 */
@Service
public class PrintTemplateConfigServiceImpl extends ServiceImpl<PrintTemplateConfigMapper, PrintTemplateConfigDO> implements IPrintTemplateConfigService {

}
