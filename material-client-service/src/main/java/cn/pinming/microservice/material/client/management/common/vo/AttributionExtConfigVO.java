package cn.pinming.microservice.material.client.management.common.vo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class AttributionExtConfigVO implements Serializable {
    @ApiModelProperty(value = "归属方id")
    private Long attributionId;

    @ApiModelProperty(value = "数据归属方名称")
    private String name;

    @ApiModelProperty(value = "数据归属方code")
    private String code;

    @ApiModelProperty(value = "订阅到期日")
    private LocalDateTime endTime;
}
