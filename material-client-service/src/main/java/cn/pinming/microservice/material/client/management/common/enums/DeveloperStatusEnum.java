package cn.pinming.microservice.material.client.management.common.enums;

public enum DeveloperStatusEnum {
    NORMAL((byte) 1, "正常使用"),
    STOP((byte) 2, "请续费");

    private byte type;
    private String description;

    DeveloperStatusEnum(byte type, String description) {
        this.type = type;
        this.description = description;
    }

    public byte value() {
        return type;
    }

    public String description() {
        return description;
    }

    public static String descByType(Byte type) {
        for (DeveloperStatusEnum value : DeveloperStatusEnum.values()) {
            if (value.type == type) {
                return value.description;
            }
        }
        return "";
    }
}
