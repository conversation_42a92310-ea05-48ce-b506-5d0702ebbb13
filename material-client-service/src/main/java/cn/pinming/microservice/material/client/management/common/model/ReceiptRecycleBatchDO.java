package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 单据回收批次表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("d_receipt_recycle_batch")
public class ReceiptRecycleBatchDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 批次名称
     */
    private String name;

    /**
     * 归属方id
     */
    private Long attributionId;

    /**
     * 设备id
     */
    private String deviceId;

    /**
     * 扩展编码
     */
    private String extCode;

    /**
     * 状态 0 未归档 1 已归档
     */
    private Byte status;

}
