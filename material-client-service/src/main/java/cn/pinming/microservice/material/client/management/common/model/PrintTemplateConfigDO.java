package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 打印模板设置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("s_print_template_config")
public class PrintTemplateConfigDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 打印模版id
     */
    private Long templateId;

    /**
     * 设备id 以逗号分割
     */
    private String attributionId;

    /**
     * 状态 0 启用 1 禁用
     */
    private Integer enabled;

    /**
     * 打印次数 
     */
    private Integer printLimit;


}
