package cn.pinming.microservice.material.client.management.service.business;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.material.v2.model.WeighData;
import cn.pinming.material.v2.model.WeighDataAssemble;
import cn.pinming.microservice.material.client.management.common.model.*;
import cn.pinming.microservice.material.client.management.common.enums.DeveloperAppEnum;
import cn.pinming.microservice.material.client.management.common.enums.IsEnableEnum;
import cn.pinming.microservice.material.client.management.common.enums.WeighDataTypeEnum;
import cn.pinming.microservice.material.client.management.common.enums.WeighDataUnitEnum;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizErrorException;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizExceptionMessageEnum;
import cn.pinming.microservice.material.client.management.infrastructure.util.CheckUtil;
import cn.pinming.microservice.material.client.management.infrastructure.util.DateUtil;
import cn.pinming.microservice.material.client.management.service.biz.*;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CombineService {

    @Resource
    private CheckUtil checkUtil;
    @Resource
    private AppInvocationDailyLogService appInvocationDailyLogService;
    @Resource
    private WeighDataService weighDataService;
    @Resource
    private DeviceAttributionService deviceAttributionService;
    @Resource
    private WeighDataCombineService weighDataCombineService;
    @Resource
    private WeighDataPicService weighDataPicService;
    @Resource
    private UserExtConfigService userExtConfigService;

    /**
     * 数据组装
     */
    public WeighDataAssemble weighDataAssemble(List<String> recordList, String attributionCode, String uid) {
        Result verifyResult = getVerifyResult(recordList, uid, attributionCode, null);
        // 称重照片
        Map<String, List<String>> preMap = weighDataPicService.getPic(verifyResult.recordList, true);
        Map<String, List<String>> realMap = weighDataPicService.getPic(verifyResult.recordList, false);
        Map<String, List<String>> uuidMap = weighDataPicService.getUuid(verifyResult.recordList);
        // 是否重组
        List<WeighDataCombineDO> combineDOList = weighDataCombineService.lambdaQuery()
                .eq(WeighDataCombineDO::getCreateId, uid)
                .eq(WeighDataCombineDO::getAttributionId, verifyResult.deviceAttributionId)
                .in(WeighDataCombineDO::getWeighDataId, verifyResult.recordList)
                .groupBy(WeighDataCombineDO::getWeighDataId)
                .list();
        List<String> combineList = new ArrayList<>();
        if (CollUtil.isNotEmpty(combineDOList)) {
            combineList.addAll(combineDOList.stream().map(WeighDataCombineDO::getWeighDataId).collect(Collectors.toList()));
        }
        // 组装数据
        List<WeighData> collect = verifyResult.weighDataList.stream().map(e -> {
            WeighData data = new WeighData();
            BeanUtils.copyProperties(e, data);
            data.setWeighTime(DateUtil.parseLocalDateTime(e.getWeighTime()));
            data.setGmtCreate(DateUtil.parseLocalDateTime(e.getGmtCreate()));
            data.setTypeStr(WeighDataTypeEnum.desc(e.getType()));
            data.setRiskGrade(e.getRiskGrade());
            if (CollUtil.isNotEmpty(preMap)) {
                data.setPic(preMap.get(e.getRecordId()));
            }
            if (CollUtil.isNotEmpty(realMap)) {
                data.setUrls(realMap.get(e.getRecordId()));
            }
            if (CollUtil.isNotEmpty(uuidMap)) {
                data.setUuids(uuidMap.get(e.getRecordId()));
            }
            if (CollUtil.isNotEmpty(combineList) && combineList.contains(e.getRecordId())) {
                data.setCombine(true);
            }
            return data;
        }).collect(Collectors.toList());
        WeighDataAssemble result = new WeighDataAssemble();
        result.setUid(uid);
        result.setWeighData(collect);

        // 记录调用次数
        appInvocationDailyLogService.saveInvokeLog(DeveloperAppEnum.ASSEMBLE.value(), uid, -1L, 1);

        return result;
    }

    /**
     * 不带额外校验的重组
     *
     * @param recordList
     * @param uid
     * @return
     */
    public WeighDataAssemble weighDataAssembleWithoutExtCheck(List<String> recordList, String uid, Long attributionId) {
        Result verifyResult = getVerifyResultWithoutExtCheck(recordList, uid, attributionId);
        // 称重照片
        Map<String, List<String>> preMap = weighDataPicService.getPic(verifyResult.recordList, true);
        Map<String, List<String>> realMap = weighDataPicService.getPic(verifyResult.recordList, false);
        Map<String, List<String>> uuidMap = weighDataPicService.getUuid(verifyResult.recordList);
        // 是否重组
        List<WeighDataCombineDO> combineDOList = weighDataCombineService.lambdaQuery()
                .eq(WeighDataCombineDO::getCreateId, uid)
                .eq(WeighDataCombineDO::getAttributionId, verifyResult.deviceAttributionId)
                .in(WeighDataCombineDO::getWeighDataId, verifyResult.recordList)
                .groupBy(WeighDataCombineDO::getWeighDataId)
                .list();
        List<String> combineList = new ArrayList<>();
        if (CollUtil.isNotEmpty(combineDOList)) {
            combineList.addAll(combineDOList.stream().map(WeighDataCombineDO::getWeighDataId).collect(Collectors.toList()));
        }
        // 组装数据
        List<WeighData> collect = verifyResult.weighDataList.stream().map(e -> {
            WeighData data = new WeighData();
            BeanUtils.copyProperties(e, data);
            data.setWeighTime(DateUtil.parseLocalDateTime(e.getWeighTime()));
            data.setGmtCreate(DateUtil.parseLocalDateTime(e.getGmtCreate()));
            data.setTypeStr(WeighDataTypeEnum.desc(e.getType()));
            data.setRiskGrade(e.getRiskGrade());
            if (CollUtil.isNotEmpty(preMap)) {
                data.setPic(preMap.get(e.getRecordId()));
            }
            if (CollUtil.isNotEmpty(realMap)) {
                data.setUrls(realMap.get(e.getRecordId()));
            }
            if (CollUtil.isNotEmpty(uuidMap)) {
                data.setUuids(uuidMap.get(e.getRecordId()));
            }
            if (CollUtil.isNotEmpty(combineList) && combineList.contains(e.getRecordId())) {
                data.setCombine(true);
            }
            return data;
        }).collect(Collectors.toList());
        WeighDataAssemble result = new WeighDataAssemble();
        result.setUid(uid);
        result.setWeighData(collect);

        return result;
    }


    /**
     * 确认数据重组
     */
    public boolean confirmAssemble(List<String> recordList, String attributionCode, Long deviceAttributionId, String uid, String appKey) {
        Result verifyResult = getVerifyResult(recordList, uid, attributionCode, deviceAttributionId);
        List<WeighDataCombineDO> result = verifyResult.recordList.stream().map(e -> {
            WeighDataCombineDO weighDataCombineDO = new WeighDataCombineDO();
            weighDataCombineDO.setWeighDataId(e);
            weighDataCombineDO.setAppKey(appKey);
            weighDataCombineDO.setCreateId(uid);
            weighDataCombineDO.setAttributionId(verifyResult.deviceAttributionId);
            return weighDataCombineDO;
        }).collect(Collectors.toList());
        List<WeighDataCombineDO> collect = result.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            weighDataCombineService.saveOrUpdateBatch(collect);
            log.info("{}确认数据重组完成", verifyResult.recordList);
        }
        return true;
    }

    /**
     * 单据回收重组
     */
    public List<WeighDataDO> receiptRecycleAssemble(List<String> recordIdList, ReceiptRecycleDO receiptRecycleDO) {
        try {
            Result verifyResult = getVerifyResult(recordIdList, receiptRecycleDO.getUid(), null, receiptRecycleDO.getAttributionId());
            //称重时间是否在指定间隔内
            WeighDataDO weighDataDO1 = verifyResult.weighDataList.get(0);
            WeighDataDO weighDataDO2 = verifyResult.weighDataList.get(1);
            //相同重量校验
            if (weighDataDO1.getUnit().equals(weighDataDO2.getUnit())) {
                if (weighDataDO1.getWeight().equals(weighDataDO2.getWeight())) {
                    log.info("称重重量相同receiptRecycleId:{}, recordIdList:{}", receiptRecycleDO.getId(), recordIdList);
                    return null;
                }
            } else {
                if (WeighDataUnitEnum.convertTon(weighDataDO1.getWeight(), weighDataDO1.getUnit()).equals(WeighDataUnitEnum.convertTon(weighDataDO2.getWeight(), weighDataDO2.getUnit()))) {
                    log.info("转换单位后，称重重量相同receiptRecycleId:{}, recordIdList:{}", receiptRecycleDO.getId(), recordIdList);
                    return null;
                }
            }
            UserExtConfigDO userExtConfigDO = userExtConfigService.lambdaQuery().eq(UserExtConfigDO::getUid, receiptRecycleDO.getUid()).one();
            if (userExtConfigDO != null) {
                //是否不检查待组装记录时间段内同车牌存在过磅记录
                if (userExtConfigDO.getIsCheckDataCombine() == null || userExtConfigDO.getIsCheckDataCombine() == IsEnableEnum.DISABLE.value()) {
                    //是否已重组数据
                    List<WeighDataCombineDO> combineDOList = weighDataCombineService.lambdaQuery()
                            .eq(WeighDataCombineDO::getCreateId, receiptRecycleDO.getUid())
                            .eq(WeighDataCombineDO::getAttributionId, receiptRecycleDO.getAttributionId())
                            .in(WeighDataCombineDO::getWeighDataId, recordIdList)
                            .list();
                    if (CollUtil.isNotEmpty(combineDOList)) {
                        log.info("已重组数据receiptRecycleId:{}, recordIdList:{}", receiptRecycleDO.getId(), recordIdList);
                        return null;
                    }
                }
                //记录车牌号与系统识别车牌号是否不一致
                if (userExtConfigDO.getIsLicensePlateInconsistent() == null || userExtConfigDO.getIsLicensePlateInconsistent() == IsEnableEnum.DISABLE.value()) {
                    String[] truckNo1 = {weighDataDO1.getTruckNo(), weighDataDO1.getLprTruckNo()};
                    String[] truckNo2 = {weighDataDO2.getTruckNo(), weighDataDO2.getLprTruckNo()};
                    if (!Arrays.equals(truckNo1, truckNo2) || !weighDataDO1.getTruckNo().equals(weighDataDO1.getLprTruckNo())) {
                        log.info("记录车牌号与系统识别车牌号不一致receiptRecycleId:{}, recordIdList:{}", receiptRecycleDO.getId(), recordIdList);
                        return null;
                    }
                }
                //是否在时间间隔内
                if (userExtConfigDO.getIsTimeInterval() == IsEnableEnum.ENABLE.value() && userExtConfigDO.getRecycleStart() != null && userExtConfigDO.getRecycleEnd() != null) {
                    long betweenMinutes = Math.abs(LocalDateTimeUtil.between(weighDataDO1.getWeighTime(), weighDataDO2.getWeighTime(), ChronoUnit.MINUTES));
                    if (betweenMinutes < userExtConfigDO.getRecycleStart() || betweenMinutes > userExtConfigDO.getRecycleEnd()) {
                        log.info("称重时间不在指定间隔内receiptRecycleId:{}, recordIdList:{}", receiptRecycleDO.getId(), recordIdList);
                        return null;
                    }
                }
                //一二磅之间是否存在同车牌过磅记录
                if (userExtConfigDO.getIsIdenticalLicensePlate() == null || userExtConfigDO.getIsIdenticalLicensePlate() == IsEnableEnum.DISABLE.value()) {
                    List<WeighDataDO> otherRecordList = weighDataService.lambdaQuery().eq(WeighDataDO::getUid, receiptRecycleDO.getUid())
                            .eq(WeighDataDO::getAttributionId, receiptRecycleDO.getAttributionId())
                            .eq(WeighDataDO::getTruckNo, weighDataDO1.getTruckNo())
                            .between(WeighDataDO::getWeighTime, weighDataDO1.getWeighTime(), weighDataDO2.getWeighTime())
                            .notIn(WeighDataDO::getRecordId, recordIdList)
                            .list();
                    if (CollUtil.isNotEmpty(otherRecordList)) {
                        log.info("一二磅之间存在同车牌过磅记录receiptRecycleId:{}, recordIdList:{}", receiptRecycleDO.getId(), recordIdList);
                        return null;
                    }
                }
            }
            // 记录调用次数
            appInvocationDailyLogService.saveInvokeLog(DeveloperAppEnum.ASSEMBLE.value(), receiptRecycleDO.getUid(), -1L, 1);
            return verifyResult.weighDataList;
        } catch (Exception e) {
            //异常不往外抛，直接回收失败
            log.error("单据回收重组异常receiptRecycleId:{}, recordIdList:{}", receiptRecycleDO.getId(), recordIdList, e);
        }
        return null;
    }

    @NotNull
    private Result getVerifyResult(List<String> recordList, String uid, String attributionCode, Long attributionId) {
        if (CollUtil.isEmpty(recordList)) {
            throw new BizErrorException(BizExceptionMessageEnum.WEIDATE_IS_NO_EXIST);
        }
        //称重数据重复
        if (recordList.stream().distinct().count() != recordList.size()) {
            throw new BizErrorException(BizExceptionMessageEnum.RECORDID_REPEAT);
        }
        // 服务校验
        checkUtil.appCheck(uid, DeveloperAppEnum.ASSEMBLE);
        DeviceAttributionDO deviceAttributionDO = null;
        // 归属方校验
        if (StrUtil.isNotBlank(attributionCode) && attributionCode.contains(",")) {
            deviceAttributionDO = deviceAttributionService.lambdaQuery()
                    .eq(DeviceAttributionDO::getUid, uid)
                    .eq(DeviceAttributionDO::getCode, attributionCode)
                    .eq(attributionId != null, DeviceAttributionDO::getId, attributionId)
                    .one();
        }
        if (StrUtil.isNotBlank(attributionCode) && !attributionCode.contains(",")) {
            deviceAttributionDO = deviceAttributionService.lambdaQuery()
                    .eq(DeviceAttributionDO::getUid, uid)
                    .apply("find_in_set({0},code)", attributionCode)
                    .eq(attributionId != null, DeviceAttributionDO::getId, attributionId)
                    .one();
        }
        if (ObjectUtil.isNotNull(attributionId)) {
            deviceAttributionDO = deviceAttributionService.lambdaQuery()
                    .eq(DeviceAttributionDO::getUid, uid)
                    .eq(DeviceAttributionDO::getId, attributionId)
                    .one();
        }

        if (deviceAttributionDO == null) {
            throw new BizErrorException(BizExceptionMessageEnum.ATTRIBUTION_IS_EMPTY);
        }
        //校验数据是否存在
        List<WeighDataDO> weighDataList = weighDataService.lambdaQuery()
                .eq(WeighDataDO::getAttributionId, deviceAttributionDO.getId())
                .eq(WeighDataDO::getUid, uid)
                .in(WeighDataDO::getRecordId, recordList)
                .orderByAsc(WeighDataDO::getWeighTime)
                .list();
        if (CollUtil.isEmpty(weighDataList)) {
            throw new BizErrorException(BizExceptionMessageEnum.WEIDATE_IS_NO_EXIST);
        }
        List<String> dbIds = weighDataList.stream().map(WeighDataDO::getRecordId).collect(Collectors.toList());
        List<String> exclude = recordList.stream().filter(e -> !dbIds.contains(e)).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(exclude)) {
            throw new BizErrorException("-20", String.join(",", exclude) + "不存在");
        }
        // 终端类型校验
        if (weighDataList.stream().anyMatch(e -> e.getType().equals(WeighDataTypeEnum.SECOND.value()))) {
            throw new BizErrorException(BizExceptionMessageEnum.WEIDATE_IS_NO_EXIST);
        }
        // 风险等级校验
        checkUtil.riskCheck(uid, DeveloperAppEnum.ASSEMBLE, weighDataList);
        return new Result(deviceAttributionDO.getId(), recordList, weighDataList);
    }

    @NotNull
    private Result getVerifyResultWithoutExtCheck(List<String> recordList, String uid, Long attributionId) {
        if (CollUtil.isEmpty(recordList)) {
            throw new BizErrorException(BizExceptionMessageEnum.WEIDATE_IS_NO_EXIST);
        }
        //称重数据重复
        if (recordList.stream().distinct().count() != recordList.size()) {
            throw new BizErrorException(BizExceptionMessageEnum.RECORDID_REPEAT);
        }

        //校验数据是否存在
        List<WeighDataDO> weighDataList = weighDataService.lambdaQuery()
                .eq(WeighDataDO::getAttributionId, attributionId)
                .eq(WeighDataDO::getUid, uid)
                .in(WeighDataDO::getRecordId, recordList)
                .orderByAsc(WeighDataDO::getWeighTime)
                .list();
        if (CollUtil.isEmpty(weighDataList)) {
            throw new BizErrorException(BizExceptionMessageEnum.WEIDATE_IS_NO_EXIST);
        }
        List<String> dbIds = weighDataList.stream().map(WeighDataDO::getRecordId).collect(Collectors.toList());
        List<String> exclude = recordList.stream().filter(e -> !dbIds.contains(e)).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(exclude)) {
            throw new BizErrorException("-20", String.join(",", exclude) + "不存在");
        }

        return new Result(attributionId, recordList, weighDataList);
    }

    private static class Result {
        public final Long deviceAttributionId;
        public final List<String> recordList;
        public final List<WeighDataDO> weighDataList;

        public Result(Long deviceAttributionId, List<String> recordList, List<WeighDataDO> weighDataList) {
            this.deviceAttributionId = deviceAttributionId;
            this.recordList = recordList;
            this.weighDataList = weighDataList;
        }
    }
}
