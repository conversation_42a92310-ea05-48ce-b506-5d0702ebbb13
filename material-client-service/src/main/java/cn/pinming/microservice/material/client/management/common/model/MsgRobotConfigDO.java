package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;


@Data
@EqualsAndHashCode(callSuper = true)
@TableName("s_msg_robot_config")
public class MsgRobotConfigDO extends BaseDO {

    /**
     * 消息类型 1钉钉群消息机器人 2企微群消息机器人
     */
    private Integer type;

    /**
     * 机器人名称
     */
    private String name;

    /**
     * 钉钉 access_token=xxxx  企业微信 key=xxxx
     */
    private String token;

    /**
     * 钉钉 加签
     */
    private String secret;

    /**
     * 范围
     */
    private String scope;

}
