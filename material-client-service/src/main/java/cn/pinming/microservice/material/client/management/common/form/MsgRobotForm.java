package cn.pinming.microservice.material.client.management.common.form;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class MsgRobotForm {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("机器人名称")
    @NotBlank(message = "机器人名称为空")
    private String name;

    @ApiModelProperty("消息类型 1钉钉群消息机器人 2企微群消息机器人")
    @NotNull(message = "消息类型为空")
    private Integer type;

    @ApiModelProperty("钉钉access_token=xxxx  企业微信key=xxxx")
    @NotBlank(message = "access_token为空")
    private String token;

    @ApiModelProperty("钉钉 加签")
    private String secret;

    @ApiModelProperty("使用范围 多选")
    @NotBlank(message = "使用范围为空")
    private String scope;

}
