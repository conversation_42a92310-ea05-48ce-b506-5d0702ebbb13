package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 开发者可选服务表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("s_developer_app")
public class DeveloperAppDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * app分类
     */
    private String name;

    /**
     * appService
     */
    private String appService;


}
