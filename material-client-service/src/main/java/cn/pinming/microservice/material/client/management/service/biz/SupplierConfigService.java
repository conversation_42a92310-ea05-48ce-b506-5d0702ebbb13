package cn.pinming.microservice.material.client.management.service.biz;

import cn.pinming.microservice.material.client.management.common.form.supplier.SupplierConfigForm;
import cn.pinming.microservice.material.client.management.common.form.supplier.SupplierConfigPushForm;
import cn.pinming.microservice.material.client.management.common.model.SupplierConfigDO;
import cn.pinming.microservice.material.client.management.common.vo.SupplierConfigVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface SupplierConfigService extends IService<SupplierConfigDO> {

    List<SupplierConfigVO> listByUID();

    void saveConfig(SupplierConfigForm form);

    void deleteById(Long id);

    void savePushConfig(SupplierConfigPushForm form);
}
