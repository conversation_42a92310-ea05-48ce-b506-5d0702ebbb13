package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.pinming.microservice.material.client.management.common.mapper.DeliveryDetailMapper;
import cn.pinming.microservice.material.client.management.common.model.DeliveryDetailDO;
import cn.pinming.microservice.material.client.management.service.biz.IDeliveryDetailService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 发货单(运单)明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-22
 */
@Service
public class DeliveryDetailServiceImpl extends ServiceImpl<DeliveryDetailMapper, DeliveryDetailDO> implements IDeliveryDetailService {

}
