package cn.pinming.microservice.material.client.management.common.mapper;

import cn.pinming.microservice.material.client.management.common.dto.AppDailyLogDTO;
import cn.pinming.microservice.material.client.management.common.model.AppResourceDailyLogDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * <p>
 * 资源每日消耗日志表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-08
 */
public interface AppResourceDailyLogMapper extends BaseMapper<AppResourceDailyLogDO> {

    List<AppDailyLogDTO> selectUserUsedSpaceList();
}
