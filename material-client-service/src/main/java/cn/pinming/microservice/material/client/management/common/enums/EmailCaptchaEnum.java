package cn.pinming.microservice.material.client.management.common.enums;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @date 2023/6/13
 */
public enum EmailCaptchaEnum {
    @ApiModelProperty(value = "注册")
    REGISTER,
    @ApiModelProperty(value = "登录")
    LOGIN,
    @ApiModelProperty(value = "重新生成appKey")
    GENERATE,
    @ApiModelProperty(value = "删除数据归属方")
    DELETE_ATTRIBUTION,
    @ApiModelProperty(value = "单机版授权文件绑定数据归属方")
    BIND_ATTRIBUTION,
    @ApiModelProperty(value = "租户删除模版")
    DELETE_MODULE,
    @ApiModelProperty(value = "修改密码")
    UPDATE_PASSWORD,
    @ApiModelProperty(value = "修改邮箱")
    UPDATE_EMAIL,
}
