package cn.pinming.microservice.material.client.management.infrastructure.config;

import com.github.xiaoymin.knife4j.spring.annotations.EnableKnife4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2WebMvc;

@Configuration
@EnableSwagger2WebMvc
@EnableKnife4j
public class SwaggerConfig {

    @Bean
    public Docket userApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("user")
                .select()
                .apis(RequestHandlerSelectors.basePackage("cn.pinming.microservice.material.client.management.controller.user"))
                .paths(PathSelectors.any())
                .build()
                .apiInfo(apiInfo());
    }

    @Bean
    public Docket openApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("openapi")
                .select()
                .apis(RequestHandlerSelectors.basePackage("cn.pinming.microservice.material.client.management.controller.openapi"))
                .paths(PathSelectors.any())
                .build()
                .apiInfo(apiInfo());
    }

    @Bean
    public Docket app() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("app")
                .select()
                .apis(RequestHandlerSelectors.basePackage("cn.pinming.microservice.material.client.management.controller.developer"))
                .paths(PathSelectors.any())
                .build()
                .apiInfo(apiInfo());
    }

    @Bean
    public Docket deviceBinding() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("deviceBinding")
                .select()
                .apis(RequestHandlerSelectors.basePackage("cn.pinming.microservice.material.client.management.controller.devicebinding"))
                .paths(PathSelectors.any())
                .build()
                .apiInfo(apiInfo());
    }

    @Bean
    public Docket deviceAttribution() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("deviceAttribution")
                .select()
                .apis(RequestHandlerSelectors.basePackage("cn.pinming.microservice.material.client.management.controller.deviceattribution"))
                .paths(PathSelectors.any())
                .build()
                .apiInfo(apiInfo());
    }

    @Bean
    public Docket weighData() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("weighData")
                .select()
                .apis(RequestHandlerSelectors.basePackage("cn.pinming.microservice.material.client.management.controller.weighdata"))
                .paths(PathSelectors.any())
                .build()
                .apiInfo(apiInfo());
    }

    @Bean
    public Docket weighDataPic() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("weighDataPic")
                .select()
                .apis(RequestHandlerSelectors.basePackage("cn.pinming.microservice.material.client.management.controller.weighdatapic"))
                .paths(PathSelectors.any())
                .build()
                .apiInfo(apiInfo());
    }

    @Bean
    public Docket common() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("common")
                .select()
                .apis(RequestHandlerSelectors.basePackage("cn.pinming.microservice.material.client.management.controller.common"))
                .paths(PathSelectors.any())
                .build()
                .apiInfo(apiInfo());
    }

    @Bean
    public Docket curve() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("curve")
                .select()
                .apis(RequestHandlerSelectors.basePackage("cn.pinming.microservice.material.client.management.controller.weighCurve"))
                .paths(PathSelectors.any())
                .build()
                .apiInfo(apiInfo());
    }

    @Bean
    public Docket curveConfig() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("curveConfig")
                .select()
                .apis(RequestHandlerSelectors.basePackage("cn.pinming.microservice.material.client.management.controller.weighCurveConfig"))
                .paths(PathSelectors.any())
                .build()
                .apiInfo(apiInfo());
    }

    @Bean
    public Docket device() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("device")
                .select()
                .apis(RequestHandlerSelectors.basePackage("cn.pinming.microservice.material.client.management.controller.devices"))
                .paths(PathSelectors.any())
                .build()
                .apiInfo(apiInfo());
    }


    @Bean
    public Docket subscribeApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("subscribe")
                .select()
                .apis(RequestHandlerSelectors.basePackage("cn.pinming.microservice.material.client.management.controller.subscribe"))
                .paths(PathSelectors.any())
                .build()
                .apiInfo(apiInfo());
    }


    @Bean
    public Docket clientApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("client")
                .select()
                .apis(RequestHandlerSelectors.basePackage("cn.pinming.microservice.material.client.management.controller.client"))
                .paths(PathSelectors.any())
                .build()
                .apiInfo(apiInfo());
    }

    @Bean
    public Docket oss() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("oss")
                .select()
                .apis(RequestHandlerSelectors.basePackage("cn.pinming.microservice.material.client.management.controller.oss"))
                .paths(PathSelectors.any())
                .build()
                .apiInfo(apiInfo());
    }

    @Bean
    public Docket document() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("document")
                .select()
                .apis(RequestHandlerSelectors.basePackage("cn.pinming.microservice.material.client.management.controller.document"))
                .paths(PathSelectors.any())
                .build()
                .apiInfo(apiInfo());
    }

    @Bean
    public Docket plate() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("plate")
                .select()
                .apis(RequestHandlerSelectors.basePackage("cn.pinming.microservice.material.client.management.controller.plate"))
                .paths(PathSelectors.any())
                .build()
                .apiInfo(apiInfo());
    }

    @Bean
    public Docket ocr() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("ocr")
                .select()
                .apis(RequestHandlerSelectors.basePackage("cn.pinming.microservice.material.client.management.controller.ocr"))
                .paths(PathSelectors.any())
                .build()
                .apiInfo(apiInfo());
    }

    @Bean
    public Docket receiptRecycle() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("receiptRecycle")
                .select()
                .apis(RequestHandlerSelectors.basePackage("cn.pinming.microservice.material.client.management.controller.receiptrecycle"))
                .paths(PathSelectors.any())
                .build()
                .apiInfo(apiInfo());
    }

    @Bean
    public Docket push() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("push")
                .select()
                .apis(RequestHandlerSelectors.basePackage("cn.pinming.microservice.material.client.management.controller.pu`sh"))
                .paths(PathSelectors.any())
                .build()
                .apiInfo(apiInfo());
    }

    @Bean
    public Docket rebarCheck() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("rebarCheck")
                .select()
                .apis(RequestHandlerSelectors.basePackage("cn.pinming.microservice.material.client.management.controller.rebarCheck"))
                .paths(PathSelectors.any())
                .build()
                .apiInfo(apiInfo());
    }

    @Bean
    public Docket userConsumer() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("userConsumer")
                .select()
                .apis(RequestHandlerSelectors.basePackage("cn.pinming.microservice.material.client.management.controller.consumer"))
                .paths(PathSelectors.any())
                .build()
                .apiInfo(apiInfo());
    }

    /**
     * 订单
     *
     * @return
     */
    @Bean
    public Docket purchase() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("purchase")
                .select()
                .apis(RequestHandlerSelectors.basePackage("cn.pinming.microservice.material.client.management.controller.purchase"))
                .paths(PathSelectors.any())
                .build()
                .apiInfo(apiInfo());
    }

    /**
     * 自助二维码
     *
     * @return
     */
    @Bean
    public Docket selfQrcode() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("self-qrcode")
                .select()
                .apis(RequestHandlerSelectors.basePackage("cn.pinming.microservice.material.client.management.controller.qrcode"))
                .paths(PathSelectors.any())
                .build()
                .apiInfo(apiInfo());
    }

    /**
     * 供应商
     *
     * @return
     */
    @Bean
    public Docket supplier() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("supplier")
                .select()
                .apis(RequestHandlerSelectors.basePackage("cn.pinming.microservice.material.client.management.controller.supplier"))
                .paths(PathSelectors.any())
                .build()
                .apiInfo(apiInfo());
    }

    /**
     * 发货单
     */
    @Bean
    public Docket delivery() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("delivery")
                .select()
                .apis(RequestHandlerSelectors.basePackage("cn.pinming.microservice.material.client.management.controller.delivery"))
                .paths(PathSelectors.any())
                .build()
                .apiInfo(apiInfo());
    }

    /**
     * 打印模板
     */
    @Bean
    public Docket printTemplate() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("print-template")
                .select()
                .apis(RequestHandlerSelectors.basePackage("cn.pinming.microservice.material.client.management.controller.print"))
                .paths(PathSelectors.any())
                .build()
                .apiInfo(apiInfo());
    }

    @Bean
    public Docket selfCheck() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("self-check")
                .select()
                .apis(RequestHandlerSelectors.basePackage("cn.pinming.microservice.material.client.management.controller.selfcheck"))
                .paths(PathSelectors.any())
                .build()
                .apiInfo(apiInfo());
    }

    @Bean
    public Docket weighDataConfirm() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("weighDataConfirm")
                .select()
                .apis(RequestHandlerSelectors.basePackage("cn.pinming.microservice.material.client.management.controller.weighDataConfirm"))
                .paths(PathSelectors.any())
                .build()
                .apiInfo(apiInfo());
    }

    @Bean
    public Docket pushConfig() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("sdk-push-config")
                .select()
                .apis(RequestHandlerSelectors.basePackage("cn.pinming.microservice.material.client.management.controller.push"))
                .paths(PathSelectors.any())
                .build()
                .apiInfo(apiInfo());
    }

    @Bean
    public Docket msgRobot() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("msg-robot")
                .select()
                .apis(RequestHandlerSelectors.basePackage("cn.pinming.microservice.material.client.management.controller.msg"))
                .paths(PathSelectors.any())
                .build()
                .apiInfo(apiInfo());
    }


    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("物料管理客户端接口文档")
                .description("物料管理客户端")
                .version("1.0.0")
                .build();
    }
}


