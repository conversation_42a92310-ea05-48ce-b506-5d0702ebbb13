package cn.pinming.microservice.material.client.management.common.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;

/**
 * 修改邮箱
 */
@Data
public class UpdateEmailForm {

    @ApiModelProperty("新邮箱")
    @NotBlank(message = "新邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    private String email;

    @ApiModelProperty("邮箱验证码")
    @NotBlank(message = "邮箱验证码不能为空")
    private String emailCaptcha;

}
