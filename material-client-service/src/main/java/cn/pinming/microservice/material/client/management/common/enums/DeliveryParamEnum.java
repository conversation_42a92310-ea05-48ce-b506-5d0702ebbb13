package cn.pinming.microservice.material.client.management.common.enums;

import cn.pinming.microservice.material.client.management.common.vo.TemplateParamVO;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
public enum DeliveryParamEnum {

    NO("no", "运单编号条码"),
    PRINT_TIME("printTime", "打印时间"),
    TRUCK_NO("truckNo", "车牌号"),
    DRIVER("driver", "司机"),
    DRIVER_MOBILE("mobile", "司机手机号"),
    ADDRESS("address", "送货地址"),
    RECEIVER("receiver", "收货人"),
    RECEIVER_MOBILE("receiverMobile", "收货人电话"),
    PROJECT("project", "收货项目"),
    RECEIVE_DATE("receiveDate", "要货时间"),
    REMARK("remark", "要货备注"),
    PREVIOUS_TRUCK_NO("previousTruckNo", "上次发货车次"),
    PREVIOUS_TIME("previousTime", "上次发货时间"),

    // 物资明细
    MATERIAL_NAME("materialName", "货物名称"),
    MATERIAL_SPEC("materialSpec", "货物规格"),
    MATERIAL_REMARK("materialRemark", "货物备注"),
    MATERIAL_ARGUMENT("materialArgument", "其他参数"),
    ORDER_AMOUNT("orderAmount", "下单数量"),
    UNIT("unit", "采购单位"),
    SEND_AMOUNT("sendAmount", "累计发货数量"),
    TRUCK_AMOUNT("truckAmount", "累计发货车次"),
    AMOUNT("amount", "本车发货数量"),
    POSITION("position", "使用部位"),

    WEIGH_MODE("weighMode", "称重类型"),
    WEIGH_VALUE("weighValue", "重量"),
    WEIGH_TIME("weighTime", "称重时间"),

    ;

    public static final Map<String, String> KEY_MAP = Arrays.stream(values()).collect(Collectors.toMap(DeliveryParamEnum::getVal, DeliveryParamEnum::getDesc));

    public static final Map<String, String> VAL_MAP = Arrays.stream(values()).collect(Collectors.toMap(DeliveryParamEnum::getDesc, DeliveryParamEnum::getVal));

    public static final List<TemplateParamVO> LIST = Arrays.stream(values()).map(e -> new TemplateParamVO(e.getVal(), e.getDesc())).collect(Collectors.toList());

    private final String val;

    private final String desc;

    DeliveryParamEnum(String val, String desc) {
        this.val = val;
        this.desc = desc;
    }

}
