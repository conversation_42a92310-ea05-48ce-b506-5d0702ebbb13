package cn.pinming.microservice.material.client.management.common.model.ext;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class WeighDataConfirmOriginExtDO {

    @ApiModelProperty(value = "打印张数")
    private Integer actualPrintCounts;

    @ApiModelProperty(value = "终端设备SN")
    private String terminalSn;

    @ApiModelProperty(value = "终端设备类型")
    private String deviceType;

    @ApiModelProperty(value = "确认数据")
    private WeighDataConfirmExtDO terminalConfirmData;

    @ApiModelProperty(value = "是否确认 0：未确认 1：已确认")
    private Integer isConfirmed;

    @ApiModelProperty(value = "打印模板列表")
    private List<PrintTemplateExtDO> printTemplateList;

    @ApiModelProperty(value = "是否超时 true：超时 false：未超时")
    private boolean isTimeout;

    @ApiModelProperty(value = "允许超时时长（分钟）如果为0则表示允许超时 ")
    private Integer timeoutMinutes;

    @ApiModelProperty(value = "设备归属方")
    private String deviceAttribution;

    @ApiModelProperty(value = "是否开启签名 0 否 1 是")
    private Integer signature;

    @ApiModelProperty(value = "是否跳过扫码 0 否 1 是")
    private Integer skipScanCode;

    @ApiModelProperty(value = "是否自动称重 0 否 1 是")
    private Integer autoWeight;

}
