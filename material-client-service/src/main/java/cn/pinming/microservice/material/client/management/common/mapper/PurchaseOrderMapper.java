package cn.pinming.microservice.material.client.management.common.mapper;

import cn.pinming.microservice.material.client.management.common.dto.push.PurchaseSyncDTO;
import cn.pinming.microservice.material.client.management.common.model.PurchaseOrderDO;
import cn.pinming.microservice.material.client.management.common.query.PurchasePageQuery;
import cn.pinming.microservice.material.client.management.common.vo.h5.SimplePurchaseVO;
import cn.pinming.microservice.material.client.management.common.vo.purchase.PurchaseDetailVO;
import cn.pinming.microservice.material.client.management.common.vo.purchase.PurchaseItemVO;
import cn.pinming.microservice.material.client.management.common.vo.purchase.PurchaseVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 采购单(订单)主表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-21
 */
public interface PurchaseOrderMapper extends BaseMapper<PurchaseOrderDO> {

    Page<PurchaseVO> pageByQuery(PurchasePageQuery query);

    PurchaseDetailVO selectDetailById(Long id);

    List<PurchaseItemVO> selectDetailListById(Long id, String uid);

    List<SimplePurchaseVO> selectH5PurchaseOrderList(@Param("supplierExtId") String supplierExtId, @Param("attributionId") Long attributionId);

    SimplePurchaseVO selectH5PurchaseOrder(@Param("purchaseId") Long purchaseId, @Param("attributionId") Long attributionId);

    List<PurchaseSyncDTO> selectPurchaseSync(@Param("uid")String uid, @Param("list") List<String> list);
}
