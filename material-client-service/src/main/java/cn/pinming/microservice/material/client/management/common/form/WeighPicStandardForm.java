package cn.pinming.microservice.material.client.management.common.form;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class WeighPicStandardForm {
    /**
     * 称重记录id
     */
    @NotNull(message = "称重记录id不能为空")
    private String weighDataId;

    /**
     * 本地文件路径
     */
    private String filePath;

    /**
     * oss 文件id
     */
    private String fileId;

    /**
     * 1 过磅照片 2 磅房、操作棚照片 3 操作人照片
     */
    @NotNull(message = "照片类型不能为空")
    private Integer type;

    /**
     * 设备机器码
     */
    @NotBlank(message = "设备机器码不能为空")
    private String deviceSn;

    /**
     * 照片本地创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime localCTime;

    /**
     * 照片本地id
     */
    private String localId;

    /**
     * 照片大小(kb)
     */
    private BigDecimal size;
}
