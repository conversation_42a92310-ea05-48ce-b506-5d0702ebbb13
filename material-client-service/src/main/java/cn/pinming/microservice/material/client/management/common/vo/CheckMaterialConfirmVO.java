package cn.pinming.microservice.material.client.management.common.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class CheckMaterialConfirmVO {
    @ApiModelProperty(value = "明细id")
    private Long id;

    @ApiModelProperty(value = "材料id")
    private Long materialId;

    @ApiModelProperty(value = "单根长度(米)")
    private BigDecimal length;

    @ApiModelProperty(value = "材料名称")
    private String materialName;

    @ApiModelProperty(value = "材料规格")
    private String materialSpec;

    @ApiModelProperty(value = "送货单根数")
    private BigDecimal sendWeight;

    @ApiModelProperty(value = "送货单重量")
    private BigDecimal sendAmount;

    @ApiModelProperty(value = "复核根数")
    private BigDecimal theoryAmount;

    @ApiModelProperty(value = "复核重量")
    private BigDecimal theoryWeight;

    @ApiModelProperty(value = "复核根数")
    private BigDecimal reverseTheoryAmount;

    @ApiModelProperty(value = "复核重量")
    private BigDecimal reverseTheoryWeight;

    @ApiModelProperty(value = "确认根数")
    private BigDecimal confirmAmount;

    @ApiModelProperty(value = "确认重量")
    private BigDecimal confirmWeight;

    @ApiModelProperty(value = "实点根数")
    private Long actualAmount;

    @ApiModelProperty(value = "钢筋类型 1 直螺纹,2 盘螺")
    private Integer type;
}
