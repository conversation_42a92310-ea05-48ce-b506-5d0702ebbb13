package cn.pinming.microservice.material.client.management.controller.oss;

import cn.pinming.microservice.material.client.management.common.enums.DeveloperAppEnum;
import cn.pinming.microservice.material.client.management.common.form.oss.FileConfirmForm;
import cn.pinming.microservice.material.client.management.common.form.oss.FileStsForm;
import cn.pinming.microservice.material.client.management.common.form.oss.MultiFileStsForm;
import cn.pinming.microservice.material.client.management.common.vo.ClientVO;
import cn.pinming.microservice.material.client.management.common.vo.oss.FileOssConfigVO;
import cn.pinming.microservice.material.client.management.common.vo.oss.StsResponseVO;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizErrorException;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizExceptionMessageEnum;
import cn.pinming.microservice.material.client.management.infrastructure.response.SingleResponse;
import cn.pinming.microservice.material.client.management.proxy.FileServiceProxy;
import cn.pinming.microservice.material.client.management.service.biz.ClientService;
import cn.pinming.microservice.material.client.management.service.biz.FileOssService;
import cn.pinming.microservice.material.client.management.service.biz.WeighDataService;
import cn.pinming.zhjz.cxptz.microservice.filecenter.api.model.StorageProviderConfigDto;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2023/6/15 13:45
 */
@Api(value = "oss", tags = {"oss"})
@RestController
@RequestMapping("/api/oss")
@Slf4j
public class OssController {

    @Resource
    private FileOssService fileOssService;
    @Resource
    private ClientService clientService;
    @Resource
    private FileServiceProxy fileServiceProxy;
    @Resource
    private WeighDataService weighDataService;

    // python客户端
    @ApiOperation(value = "获取oss临时sts和callback配置", responseReference = "SingleResponse«FileOssConfigVO»", nickname = "ossConfig")
    @PostMapping("/config")
    public SingleResponse<FileOssConfigVO> config(@Validated @RequestBody MultiFileStsForm form) {
//        log.info("oss getConfig form:{}", JSONUtil.toJsonStr(form));
        FileOssConfigVO result = fileOssService.getConfig(form);
//        log.info("oss getConfig result:{}", JSONUtil.toJsonStr(result));
        return SingleResponse.of(result);
    }

    // python客户端
    @ApiOperation(value = "确认", responseReference = "SingleResponse«Boolean»", nickname = "confirm")
    @PostMapping("/confirm")
    public SingleResponse<Boolean> confirm(@Validated @RequestBody FileConfirmForm form) {
//        log.info("oss confirm form:{}", JSONUtil.toJsonStr(form));
        fileOssService.confirm(form);
        return SingleResponse.of(true);
    }

    // python客户端 私有化部署用
    @ApiOperation(value = "文件上传", responseReference = "SingleResponse«String»", nickname = "upload")
    @PostMapping("/upload/{sn}")
    public SingleResponse<String> upload(MultipartFile file, @PathVariable String sn) {
        if (file == null || file.isEmpty()) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "文件为空");
        }
        weighDataService.check(sn, DeveloperAppEnum.UPLOAD.value(), true, null);
        String fileId = fileServiceProxy.uploadFileForUuid(file);
        return SingleResponse.of(fileId);
    }

    // 私有化部署用
//    @ApiOperation(value = "文件预览地址", responseReference = "SingleResponse«String»", nickname = "preview")
//    @GetMapping("/preview/{fileId}")
//    public SingleResponse<String> preview(@PathVariable String fileId) {
//        fileServiceProxy.confirm(Collections.singletonList(fileId));
//        String url = fileOssService.getUrlByUuid(fileId);
//        return SingleResponse.of(url);
//    }

    @ApiOperation(value = "客户端列表", responseReference = "SingleResponse«IPage<ClientVO>»", nickname = "clientList")
    @GetMapping("/list")
    public SingleResponse<?> list() {
        Page query = new Page();
        query.setSize(Integer.MAX_VALUE);
        IPage<ClientVO> page = clientService.clientPageList(query);
        return SingleResponse.of(page);
    }

    @ApiOperation(value = "最新客户端", responseReference = "SingleResponse«ClientVO»", nickname = "clientLatest")
    @GetMapping("/{type}/latest")
    public SingleResponse<?> latest(@PathVariable Byte type) {
        ClientVO result = clientService.clientLatest(type);
        return SingleResponse.of(result);
    }


    // web端 私有化部署无用
    @ApiOperation(value = "获取oss callback配置", responseReference = "SingleResponse«StorageProviderConfigDto»", nickname = "uploadFileConfig")
    @PostMapping("/uploadFileConfig")
    public SingleResponse<?> uploadFileConfig(@RequestParam Byte subSystem) {
        StorageProviderConfigDto config = fileOssService.getConfig(subSystem);
        return SingleResponse.of(config);
    }

    // web端 私有化部署无用
    @ApiOperation(value = "获取oss临时sts", responseReference = "SingleResponse«StsResponseVO»", nickname = "fileSts")
    @PostMapping("/fileSts")
    public SingleResponse<?> fileSts(FileStsForm form) {
        StsResponseVO result = fileOssService.getFileSts(form);
        return SingleResponse.of(result);
    }


}
