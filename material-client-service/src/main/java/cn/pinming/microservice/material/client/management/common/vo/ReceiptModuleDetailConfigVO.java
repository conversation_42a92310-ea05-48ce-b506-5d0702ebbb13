package cn.pinming.microservice.material.client.management.common.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class ReceiptModuleDetailConfigVO implements Serializable {

    private static final long serialVersionUID = 4752198648725663275L;

    @ApiModelProperty(value = "模版明细id")
    @NotNull(message = "模版明细id不能为空")
    private Long moduleDetailId;

    @ApiModelProperty(value = "数据组顺序")
    private Integer groupOrder;

    @ApiModelProperty(value = "键值顺序")
    private Integer keyOrder;

    @ApiModelProperty(value = "数据组名称")
    private String groupName;

    @ApiModelProperty(value = "数据键名称")
    private String keyName;

    @ApiModelProperty(value = "业务类型 1 单据匹配显示设置 2 单据回收必须键值")
    private Byte recycleDetailType;

    @ApiModelProperty(value = "锚点类型  1 主锚 2 副锚 3 业务锚点")
    private Byte type;

    @ApiModelProperty(value = "数据组id")
    private String groupId;
}
