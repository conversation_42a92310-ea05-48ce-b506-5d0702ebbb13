package cn.pinming.microservice.material.client.management.common.vo.ocr;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class OCRTemplateVO implements Serializable {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "第三方id")
    private String extId;

    @ApiModelProperty(value = "业务结构")
    private List<ItemAreaVO> param;
}
