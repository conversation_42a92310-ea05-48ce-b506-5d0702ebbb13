package cn.pinming.microservice.material.client.management.common.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ExpirePicFixDTO {
    @ApiModelProperty("终端记录id")
    private String recordId;


    @ApiModelProperty("称重时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime weighTime;

    /**
     * oss 文件id
     */
    private String fileId;
}
