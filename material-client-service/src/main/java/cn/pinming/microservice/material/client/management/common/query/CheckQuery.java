package cn.pinming.microservice.material.client.management.common.query;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class CheckQuery extends Page {
    @ApiModelProperty(value = "是否归档 1 否, 2 是")
    private Integer isVerify;

    @ApiModelProperty(value = "推送状态 1 未推, 2 已推")
    private Integer pushStatus;

    @ApiModelProperty(value = "租户id")
    @NotBlank(message = "请选择租户")
    private String uid;

    @ApiModelProperty(value = "归属方主键id")
    private Long attributionId;

    @ApiModelProperty(value = "用户id")
    private Long consumeId;

    @ApiModelProperty(value = "验收编号")
    private String no;

    @ApiModelProperty(value = "钢筋类型 1 直螺纹,2 盘螺")
    private Integer type;

    @ApiModelProperty(value = "设备sn")
    private String phoneSn;

    @ApiModelProperty(value = "归属方code")
    private String attributionCode;

    @ApiModelProperty(value = "归属方名称")
    private String attributionName;

    @ApiModelProperty(value = "用户名称")
    private String consumeName;
}
