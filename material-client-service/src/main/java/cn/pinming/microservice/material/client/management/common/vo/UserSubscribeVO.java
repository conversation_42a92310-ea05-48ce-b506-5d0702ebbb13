package cn.pinming.microservice.material.client.management.common.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2023/8/7 17:02
 */
@Data
public class UserSubscribeVO {

    /**
     * 用户配置表id
     */
    private Long id;

    /**
     * 用户配置表uid
     */
    private String uid;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 租户名称
     */
    private String userName;

    /**
     * 购买空间， 单位GB
     */
    private BigDecimal spaceSize;

    /**
     * 使用空间， 单位GB
     */
    private BigDecimal spaceUseSize;

    /**
     * 已购买服务调用总次数
     */
    private Long apiTotal;

    /**
     * 实际服务调用次数
     */
    private Long apiUseTotal;

    /**
     * 剩余称重数据服务调用api
     */
    private Long remainingApi;

    /**
     * 剩余单据服务调用api
     */
    private Long remainingReceiptApi;

}
