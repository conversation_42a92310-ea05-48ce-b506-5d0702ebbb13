package cn.pinming.microservice.material.client.management.infrastructure.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.pinming.microservice.material.client.management.common.dto.AppDailyLogDTO;
import cn.pinming.microservice.material.client.management.common.mapper.AppResourceDailyLogMapper;
import cn.pinming.microservice.material.client.management.common.model.AppResourceDailyLogDO;
import cn.pinming.microservice.material.client.management.common.model.UserConfigDO;
import cn.pinming.microservice.material.client.management.service.biz.AppResourceDailyLogService;
import cn.pinming.microservice.material.client.management.service.biz.UserConfigService;
import cn.pinming.microservice.material.client.management.service.biz.WeighDataPicService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2023/8/8 14:59
 */
@Component
@Slf4j
public class UserSpaceStatisticsTask {

    @Resource
    private UserConfigService userConfigService;
    @Resource
    private AppResourceDailyLogService appResourceDailyLogService;
    @Resource
    private AppResourceDailyLogMapper appResourceDailyLogExtMapper;
    @Resource
    private WeighDataPicService weighDataPicService;
    @Value("${rocketmq.isEnable:false}")
    private boolean isEnable;

    /**
     * 整点开始 每15分钟执行一次 统计各个租户的空间占用情况
     */
    @Scheduled(cron = "0 0/5 * * * ? ")
    @Transactional(rollbackFor = Exception.class)
    public void run() {
        if (!isEnable) {
            return;
        }
        List<UserConfigDO> userList = userConfigService.lambdaQuery().select(UserConfigDO::getUid, UserConfigDO::getId).list();
        log.error("userList:{}", userList.size());
        if (CollUtil.isNotEmpty(userList)) {
            //统计当日的空间占用情况
            String now = LocalDate.now().toString();
            List<AppResourceDailyLogDO> dailyLogList = new ArrayList<>();

            for (UserConfigDO userConfig : userList) {
                String uid = userConfig.getUid();
                AppResourceDailyLogDO dailyLogDO = appResourceDailyLogService.lambdaQuery().eq(AppResourceDailyLogDO::getDate, now).eq(AppResourceDailyLogDO::getCreateId, uid).one();
                if (dailyLogDO == null) {
                    dailyLogDO = new AppResourceDailyLogDO();
                    dailyLogDO.setDate(now);
                    dailyLogDO.setCreateId(uid);
                    dailyLogDO.setModifyId(uid);
                }

                long space = weighDataPicService.queryTodayUsedSpaceByUid(uid);
                if (space > 0) {
                    BigDecimal todayUsedSpace = NumberUtil.div(BigDecimal.valueOf(space), 1024 * 1024, 4);
                    dailyLogDO.setUsedSpace(todayUsedSpace);
                }
                dailyLogList.add(dailyLogDO);
            }
            log.error("dailyLogList:{}", dailyLogList.size());
            appResourceDailyLogService.saveOrUpdateBatch(dailyLogList);

            List<AppDailyLogDTO> list = appResourceDailyLogExtMapper.selectUserUsedSpaceList();
            if (CollUtil.isNotEmpty(list)) {
                List<UserConfigDO> userConfigList = list.stream().map(obj -> {
                    UserConfigDO userConfigDO = new UserConfigDO();
                    userConfigDO.setId(obj.getId());
                    userConfigDO.setSpaceUseSize(obj.getUsedSpace());
//                    userConfigDO.setApiUseTotal(obj.getUsedApiTotal());
                    userConfigDO.setModifyId(obj.getCreateId());
                    return userConfigDO;
                }).collect(Collectors.toList());
                userConfigService.saveOrUpdateBatch(userConfigList);
            }
        }
    }
}
