package cn.pinming.microservice.material.client.management.infrastructure.util.pwd;

import cn.pinming.microservice.material.client.management.infrastructure.exception.BizErrorException;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizExceptionMessageEnum;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2023/6/12
 */
public class DynamicSalt {

    private static int[] SALT_INDEX = new int[]{14, 25, 27, 16, 7, 10, 0, 5, 9, 21, 11, 26, 23, 1, 18, 15, 29, 19, 12, 4, 24, 6, 17, 20, 30, 31, 8, 2, 28, 3, 13, 22};

    public static String dynamicSaltExtract(String salt) {
        if (StringUtils.isBlank(salt) || salt.length() != 32 || salt.contains(" ")) {
            throw new BizErrorException(BizExceptionMessageEnum.SALT_ERROR);
        }
        String dynamicSalt = "";
        // 提取
        String[] saltArr = salt.split("");
        for (int saltIndex : SALT_INDEX) {
            dynamicSalt += saltArr[saltIndex];
        }

        return dynamicSalt;
    }
}
