package cn.pinming.microservice.material.client.management.common.enums;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;

/**
 * 模板键值类型（1-字符串 2-数字 3-日期）
 */
@Getter
public enum OcrKeyTypeEnum {

    STRING((byte) 1, "字符串"),
    NUMBER((byte) 2, "数字"),
    DATE((byte) 3, "日期");

    private final Byte value;
    private final String desc;

    OcrKeyTypeEnum(Byte value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static boolean validate(Byte value, String content) {
        if (value == null || content == null || content.equals("")) {
            return false;
        }
        if (value.equals(NUMBER.value)) {
            return StrUtil.isNumeric(content);
        }
        return true;
    }
}
