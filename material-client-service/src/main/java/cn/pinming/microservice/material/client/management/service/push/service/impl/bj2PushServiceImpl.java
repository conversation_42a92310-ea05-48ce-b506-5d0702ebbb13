package cn.pinming.microservice.material.client.management.service.push.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.microservice.material.client.management.common.enums.DeveloperAppEnum;
import cn.pinming.microservice.material.client.management.common.dto.push.PushDTO;
import cn.pinming.microservice.material.client.management.common.model.PushConfigurationDO;
import cn.pinming.microservice.material.client.management.service.biz.AppInvocationDailyLogService;
import cn.pinming.microservice.material.client.management.service.push.dto.bj2.bj2PushConvertDTO;
import cn.pinming.microservice.material.client.management.service.push.dto.bj2.bj2PushDTO;
import cn.pinming.microservice.material.client.management.service.push.dto.bj2.bj2PushFileDTO;
import com.alibaba.fastjson.JSONArray;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class bj2PushServiceImpl extends AbstractPushServiceImpl {
    @Resource
    private AppInvocationDailyLogService appInvocationDailyLogService;

    private static final String SERVICE_NAME = "八局二一体化地磅数据推送接口";
    private static final String URL = "https://yth.dev.cscec82.com:9999/api/material/app/weighing/batchCreate";
    private static final String APPID = "eLhx+I/RJRky1fLUZWOy";

    @Override
    public void pushData() {
        List<PushDTO> pushDatas = getPushDatas();
        if (CollUtil.isNotEmpty(pushDatas)) {
            bj2PushConvertDTO convert = convert(pushDatas);
            push(convert);
        }
    }

    /**
     * 获取推送数据
     */
    private List<PushDTO> getPushDatas() {
        // 获取配置
        PushConfigurationDO configuration = getConfiguration(SERVICE_NAME);
        List<PushDTO> weighDatas = new ArrayList<>();
        if (ObjUtil.isNotNull(configuration) && StrUtil.isNotBlank(configuration.getAttributionIds())) {
            // 校验
            List<String> attributionIds = check(configuration);
            if (CollUtil.isNotEmpty(attributionIds)) {
                // 获取数据
                weighDatas = getWeighDatas(attributionIds);
                if (CollUtil.isNotEmpty(weighDatas)) {
                    // 获取照片
                    getWeighPics(weighDatas);
                }
            }
        }

        return weighDatas;
    }

    /**
     * 转换数据
     */
    private bj2PushConvertDTO convert(List<PushDTO> list) {
        bj2PushConvertDTO resultDTO = new bj2PushConvertDTO();

        List<bj2PushDTO> result = list.stream().map(e -> {
            bj2PushDTO dto = new bj2PushDTO();
            dto.setProjectSysNo(e.getAttributionCode());
            dto.setPlateNumber(e.getTruckNo());
            dto.setPlateNumberConfidenceRatio(judgePlateNumberConfidenceRatio(e));
            dto.setAppWeighingId(e.getRecordId());
            dto.setTime(new DateTime(e.getWeighTime().toLocalTime()));
            dto.setRemark(null);
            dto.setWeight(e.getWeight());
            dto.setWeighingType(2);
            dto.setRegisterWeighingId(null);

            if (CollUtil.isNotEmpty(e.getList())) {
                List<bj2PushFileDTO> fileDTOS = e.getList().stream().map(item -> {
                    bj2PushFileDTO fileDTO = new bj2PushFileDTO();
                    fileDTO.setUrl(item.getDownloadUrl());
                    fileDTO.setType(100);
                    fileDTO.setName(item.getPicType() == 1 ? "过磅照片" : (item.getPicType() == 2 ? "磅房照片" : "操作人照片") + e.getWeighTime().toInstant(ZoneOffset.ofHours(8)).toEpochMilli());
                    return fileDTO;
                }).collect(Collectors.toList());
                dto.setAttachments(fileDTOS);
            }

            return dto;
        }).collect(Collectors.toList());

        // 租户推送次数
        Map<String, Long> collect = list.stream().collect(Collectors.groupingBy(PushDTO::getUid, Collectors.counting()));

        resultDTO.setPushDTOList(result);
        resultDTO.setUserMap(collect);
        return resultDTO;
    }

    /**
     * 推送
     */
    private void push(bj2PushConvertDTO convertDTO) {
        List<bj2PushDTO> list = convertDTO.getPushDTOList();
        Map<String, Long> userMap = convertDTO.getUserMap();

        CloseableHttpClient httpClient;
        CloseableHttpResponse httpResponse = null;
        String result = "";
        // 创建httpClient实例
        httpClient = HttpClients.createDefault();
        // 创建httpPost远程连接实例
        HttpPost httpPost = new HttpPost(URL);
        // 配置请求参数实例
        // 设置连接主机服务超时时间
        RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(35000)
                // 设置连接请求超时时间
                .setConnectionRequestTimeout(35000)
                // 设置读取数据连接超时时间
                .setSocketTimeout(60000)
                .build();
        // 为httpPost实例设置配置
        httpPost.setConfig(requestConfig);
        // 设置请求头
        httpPost.addHeader("Content-Type", "application/json");
        httpPost.addHeader("x-client-app-id",APPID);
        // 为httpPost设置封装好的请求参数
        String string = JSONArray.toJSONString(list);
        httpPost.setEntity(new StringEntity(string, "UTF-8"));

        try {
            // httpClient对象执行post请求,并返回响应参数对象
            httpResponse = httpClient.execute(httpPost);
            // 从响应对象中获取响应内容
            HttpEntity entity = httpResponse.getEntity();
            result = EntityUtils.toString(entity);
        } catch (IOException e) {
            log.error("八局二推送出错:{}",e.getMessage());
        } finally {
            log.info("八局二推送result:{}", result);
            // 关闭资源
            if (null != httpResponse) {
                try {
                    httpResponse.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (null != httpClient) {
                try {
                    httpClient.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

        // 扣减服务次数
        userMap.forEach((k,v) -> appInvocationDailyLogService.saveInvokeLog(DeveloperAppEnum.PUSH.value(), k,-1L, v));
    }

    private BigDecimal judgePlateNumberConfidenceRatio(PushDTO dto) {
        BigDecimal ratio = BigDecimal.valueOf(10);
        Random random = new Random();
        if ("LOW".equals(dto.getRiskGrade())) {
            // 低风险
            if (StrUtil.isNotBlank(dto.getLprTruckNo())) {
                // 95-99
                ratio = new BigDecimal(random.nextInt(4) + 95);
            }else {
                // 90-94
                ratio = new BigDecimal(random.nextInt(4) + 90);
            }
        }else if ("MIDDLE".equals(dto.getRiskGrade())) {
            // 中风险
            if (StrUtil.isNotBlank(dto.getLprTruckNo())) {
                // 85-89
                ratio = new BigDecimal(random.nextInt(4) + 85);
            }else {
                // 80-84
                ratio = new BigDecimal(random.nextInt(4) + 80);
            }
        }else if ("HIGH".equals(dto.getRiskGrade())) {
            // 高风险
            if (StrUtil.isNotBlank(dto.getLprTruckNo())) {
                // 75-79
                ratio = new BigDecimal(random.nextInt(4) + 75);
            }else {
                // 70-74
                ratio = new BigDecimal(random.nextInt(4) + 70);
            }
        }
        return ratio;
    }
}
