package cn.pinming.microservice.material.client.management.common.dto.ocr;


import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2024/9/25 15:47
 */
@Data
public class SelfDeviceOcrDTO {

    /**
     * 设备sn
     */
    private String deviceSn;

    /**
     * 确认单本地id
     */
    private String localId;

    /**
     * 照片
     */
    private List<String> recyclePics;

    /**
     * 过磅类型(1-收料、2-发料)
     */
    private Integer weighType;

    /**
     * 车牌号
     */
    private String truckNo;

    /**
     * 毛重
     */
    private BigDecimal weightGross;

    /**
     * 毛重时间
     */
    private LocalDateTime weightGrossTime;

    /**
     * 皮重
     */
    private BigDecimal weightTare;

    /**
     * 皮重时间
     */
    private LocalDateTime weightTareTime;

    /**
     * 净重
     */
    private BigDecimal weightNet;

    /**
     * 称重单位（吨）
     */
    private String unit;

    /**
     * 回收时间
     */
    private LocalDateTime recycleTime;

    /**
     * 批次id
     */
    private Long batchId;
}
