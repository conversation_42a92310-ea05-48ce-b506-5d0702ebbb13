package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <p>
 * 原始记录称重曲线配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("d_weigh_curve_config")
public class WeighCurveConfigDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 用户id
     */
    private String uid;

    /**
     * 归属方主键id
     */
    private Long attributionId;

    /**
     * 设备Id
     */
    private Long deviceId;

    private Long deviceBindingId;

    /**
     * 平台期时长 秒
     */
    private Integer platformDuration;

    /**
     * 平台期个数
     */
    private Integer platformCount;

    /**
     * 持续时长 秒
     */
    private Integer sustainDuration;

    /**
     * 称重最大值 kg
     */
    private BigDecimal weight;

    /**
     * 防控仪监控周期 秒
     */
    private Integer alarmTime;

    /**
     * 告警项
     */
    private String alarmItem;

}
