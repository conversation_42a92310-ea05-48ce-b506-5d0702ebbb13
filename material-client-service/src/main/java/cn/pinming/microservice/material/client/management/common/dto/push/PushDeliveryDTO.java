package cn.pinming.microservice.material.client.management.common.dto.push;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class PushDeliveryDTO {
    @ApiModelProperty(value = "发货单编号")
    private String jsDeliveryNo;

    @ApiModelProperty(value = "车牌号")
    private String truckNo;

    @ApiModelProperty(value = "司机名称")
    private String driverName;

    @ApiModelProperty(value = "司机电话")
    private String driverPhoneNo;

    @ApiModelProperty(value = "发货时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime deliveryTime;

    @ApiModelProperty(value = "发货单物料明细")
    private List<PushDeliveryMaterialDTO> cargoList;
}
