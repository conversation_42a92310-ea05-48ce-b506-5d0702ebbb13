package cn.pinming.microservice.material.client.management.service.biz;

import cn.pinming.microservice.material.client.management.common.model.ReceiptRecycleDO;
import cn.pinming.microservice.material.client.management.common.model.ReceiptRecycleModuleDO;
import cn.pinming.microservice.material.client.management.common.vo.ReceiptRecycleModuleVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 单据回收模板表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-03
 */
public interface ReceiptRecycleModuleService extends IService<ReceiptRecycleModuleDO> {

    boolean verifyAndCorrectionModule(ReceiptRecycleDO receiptRecycleDO, Long moduleId);

    List<ReceiptRecycleModuleVO> queryReceiptRecycleModuleVO(ReceiptRecycleDO receiptRecycleDO);
}
