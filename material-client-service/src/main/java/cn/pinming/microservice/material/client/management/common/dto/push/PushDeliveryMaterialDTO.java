package cn.pinming.microservice.material.client.management.common.dto.push;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class PushDeliveryMaterialDTO {
    @ApiModelProperty(value = "基石平台订单明细id")
    private String jsCargoId;

    @ApiModelProperty(value = "业务系统订单明细id")
    private String cargoId;

    @ApiModelProperty(value = "货物名称")
    private String cargoName;

    @ApiModelProperty(value = "货物规格")
    private String cargoModel;

    @ApiModelProperty(value = "货物其他参数")
    private String cargoParameter;

    @ApiModelProperty(value = "货物品牌")
    private String cargoBrand;

    @ApiModelProperty(value = "运单数量")
    private BigDecimal waybillCounts;

    @ApiModelProperty(value = "运单数量单位")
    private String waybillUnit;

    @ApiModelProperty(value = "使用部位id 如WBS的id")
    private String cargoUsageId;

    @ApiModelProperty(value = "使用部位id ")
    private String cargoUsage;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "称重计算参数")
    private PushDeliveryParaDTO calParameters;
}
