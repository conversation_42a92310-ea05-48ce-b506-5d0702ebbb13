package cn.pinming.microservice.material.client.management.service.biz;

import cn.pinming.microservice.material.client.management.common.form.GenerateForm;
import cn.pinming.microservice.material.client.management.common.model.DeveloperDO;
import cn.pinming.microservice.material.client.management.common.model.ext.DeveloperExtDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface DeveloperService extends IService<DeveloperDO> {

    List<DeveloperExtDO> selectAppList(String uId);

    void updateDeveloperType(Long id, Byte type);

    void generate(String uId, GenerateForm form);

    void init();
}
