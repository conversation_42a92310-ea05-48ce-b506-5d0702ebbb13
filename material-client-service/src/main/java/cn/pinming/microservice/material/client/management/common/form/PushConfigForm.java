package cn.pinming.microservice.material.client.management.common.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class PushConfigForm {

    @ApiModelProperty(value = "接口id")
    @NotNull(message = "接口id为空")
    private Long id;

    @ApiModelProperty(value = "排除的数据归属方id列表")
    private List<Long> attributionIdList;

//    @ApiModelProperty(value = "服务器地址", required = true)
//    @NotBlank(message = "服务器地址为空")
//    @Pattern(regexp = "^(https?://)(?!localhost|127\\.0\\.0\\.1|192\\.168\\.\\d{1,3}\\.\\d{1,3})(?!.*\\.local(?:domain)?\\.com)[a-zA-Z0-9-]+(\\.[a-zA-Z0-9-]+)*(\\.[a-zA-Z]{2,})?$",message = "服务器地址格式不正确")
//    private String serverHost;


}
