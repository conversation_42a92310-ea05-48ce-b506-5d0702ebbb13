package cn.pinming.microservice.material.client.management.common.form.supplier;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class SelfQrcodeForm {

    @ApiModelProperty(value = "名称")
    @NotBlank(message = "入口名称为空")
    private String name;

    @ApiModelProperty(value = "归属方id")
    @NotNull(message = "归属方id为空")
    private Long attributionId;

    @ApiModelProperty(value = "类型  1 运单  2 ocr")
    @NotNull(message = "类型为空")
    private Integer type;

}
