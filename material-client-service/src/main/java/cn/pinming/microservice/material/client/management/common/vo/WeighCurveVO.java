package cn.pinming.microservice.material.client.management.common.vo;

import cn.pinming.microservice.material.client.management.common.dto.WeighDataSavedDTO;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class WeighCurveVO implements Serializable {
    private List<Double> weights;
    private List<LocalDateTime> times;
    private List<WeighDataSavedDTO> weighPoints;
    private List<LocalDateTime> logTimes;
}
