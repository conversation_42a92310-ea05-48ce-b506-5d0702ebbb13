package cn.pinming.microservice.material.client.management.common.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
public enum TemplateTypeEnum {
    DRIVER_CHECK_ORDER((byte) 0, "司机确认单"),
    DELIVERY_ORDER((byte) 1, "发货单"),
    DELIVERY_OCR_ORDER((byte) 2, "司机确认单(OCR)")
    ;

    public static final Map<Byte, String> KEY_MAP = Arrays.stream(values()).collect(Collectors.toMap(TemplateTypeEnum::getVal, TemplateTypeEnum::getDesc));

    public static final Map<String, Byte> VAL_MAP = Arrays.stream(values()).collect(Collectors.toMap(TemplateTypeEnum::getDesc, TemplateTypeEnum::getVal));
    /**
     * 状态值
     */
    private final Byte val;
    /**
     * 状态的描述
     */
    private final String desc;

    TemplateTypeEnum(Byte val, String desc) {
        this.val = val;
        this.desc = desc;
    }

}
