package cn.pinming.microservice.material.client.management.common.enums;

/**
 * 是否启用枚举、通用枚举
 */
public enum IsEnableEnum {
    ENABLE((byte) 0, "启用"),
    DISABLE((byte) 1, "禁用");

    private final byte type;

    private final String description;

    IsEnableEnum(byte type, String description) {
        this.type = type;
        this.description = description;
    }

    public byte value() {
        return type;
    }

    public String description() {
        return description;
    }
}
