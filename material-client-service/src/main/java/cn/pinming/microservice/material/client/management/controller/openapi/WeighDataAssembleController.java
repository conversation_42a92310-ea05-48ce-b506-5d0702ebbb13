package cn.pinming.microservice.material.client.management.controller.openapi;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.BetweenFormatter;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.material.v2.model.PageList;
import cn.pinming.material.v2.model.QueryPage;
import cn.pinming.material.v2.model.WeighDataAssemble;
import cn.pinming.material.v2.model.dto.*;
import cn.pinming.material.v2.model.form.WeighDataAssembleForm;
import cn.pinming.material.v2.model.query.*;
import cn.pinming.material.v2.model.vo.*;
import cn.pinming.material.v2.signature.SignatureConstant;
import cn.pinming.microservice.material.client.management.common.dto.WeighPushPicDTO;
import cn.pinming.microservice.material.client.management.common.dto.push.PushDataPicDTO;
import cn.pinming.microservice.material.client.management.common.form.*;
import cn.pinming.microservice.material.client.management.common.mapper.ext.DeviceBindingExtMapper;
import cn.pinming.microservice.material.client.management.common.mapper.ext.WeighDataExtMapper;
import cn.pinming.microservice.material.client.management.common.model.*;
import cn.pinming.microservice.material.client.management.common.vo.WeighDataConfirmDetailDTO;
import cn.pinming.microservice.material.client.management.infrastructure.response.SingleResponse;
import cn.pinming.microservice.material.client.management.infrastructure.util.DateUtil;
import cn.pinming.microservice.material.client.management.infrastructure.util.StpKit;
import cn.pinming.microservice.material.client.management.service.biz.*;
import cn.pinming.microservice.material.client.management.service.openapi.WeighDataAssembleService;
import cn.pinming.zhjz.cxptz.microservice.filecenter.api.FileCenterService;
import cn.pinming.zhjz.cxptz.microservice.filecenter.api.model.DownloadUrlOptionsDto;
import cn.pinming.zhjz.cxptz.microservice.filecenter.api.model.FileIdentityDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 */
@Slf4j
@Api(value = "OpenAPI", tags = {"openapi"})
@RestController
@RequestMapping("/api/openapi")
public class WeighDataAssembleController {
    @Resource
    private WeighDataExtMapper weighDataExtMapper;
    @Resource
    private WeighDataAssembleService weighDataAssembleService;
    @Resource
    private WeighDataService weighDataService;
    @Resource
    private IPurchaseOrderService purchaseOrderService;
    @Resource
    private IWeighDataConfirmService weighDataConfirmService;
    @Resource
    private ReceiptRecycleService receiptRecycleService;
    @Resource
    private IDeliveryService deliveryService;
    @Resource
    private UserService userService;
    @Resource
    private IWeighCurveService weighCurveService;
    @Resource
    private IWeighCurveAlarmService weighCurveAlarmService;
    @Resource
    private ICheatAlarmLogService cheatAlarmLogService;
    @DubboReference
    private FileCenterService fileCenterService;
    @Resource
    private OCRModuleService ocrModuleService;
    @Resource
    private WeighDataPicService weighDataPicService;
    @Resource
    private DeviceBindingExtMapper deviceBindingExtMapper;

    @ApiOperation(value = "组装数据", nickname = "package")
    @PostMapping("/weigh/data/assemble")
    public SingleResponse<WeighDataAssemble> toPackage(HttpServletRequest request,
                                                       @RequestBody WeighDataAssembleForm assembleForm) {
        String appKeyHeader = request.getHeader(SignatureConstant.APP_KEY_PARAM_HEADER_NAME);
        WeighDataAssemble weighDataAssemble = weighDataAssembleService.weighDataAssemble(appKeyHeader, assembleForm);
        return SingleResponse.of(weighDataAssemble);
    }

    @ApiOperation(value = "确认数据被组装", nickname = "confirm")
    @PostMapping("/weigh/data/confirm")
    public SingleResponse<Boolean> confirm(HttpServletRequest request,
                                           @RequestBody WeighDataAssembleForm assembleForm) {
        String appKeyHeader = request.getHeader(SignatureConstant.APP_KEY_PARAM_HEADER_NAME);
        boolean flag = weighDataAssembleService.confirm(appKeyHeader, assembleForm);
        return SingleResponse.of(flag);
    }

    @ApiOperation(value = "根据条件查询数据", nickname = "pageQuery")
    @PostMapping("/weigh/data")
    public SingleResponse<PageList<WeighDataDTO>> pageQuery(HttpServletRequest request,
                                                            @RequestBody QueryPage<WeighDataQuery> queryPage) {
        String appKeyHeader = request.getHeader(SignatureConstant.APP_KEY_PARAM_HEADER_NAME);
        PageList<WeighDataDTO> pageList = weighDataAssembleService.pageQuery(appKeyHeader, queryPage);
        return SingleResponse.of(pageList);
    }

    @ApiOperation(value = "warning4 错配或偷重", nickname = "warning4")
    @PostMapping("/weigh/data/warning4")
    public SingleResponse<WeighDataAssemble> query(HttpServletRequest request,
                                                   @RequestBody AssembleForm assembleForm) {
        String appKeyHeader = request.getHeader(SignatureConstant.APP_KEY_PARAM_HEADER_NAME);
        WeighDataAssembleForm weighDataAssembleForm = new WeighDataAssembleForm();
        weighDataAssembleForm.setUid(assembleForm.getUid());
        weighDataAssembleForm.setFirst(assembleForm.getFirst());
        weighDataAssembleForm.setSecond(assembleForm.getSecond());
        weighDataAssembleForm.setAttributionCode(assembleForm.getAttributionCode());
        weighDataAssembleForm.setTruckNo(assembleForm.getTruckNo());
        weighDataAssembleForm.setStartTime(DateUtil.parseLocalDateTime(assembleForm.getStartTime()));
        weighDataAssembleForm.setEndTime(DateUtil.parseLocalDateTime(assembleForm.getEndTime()));
        WeighDataAssemble weighDataAssemble = weighDataAssembleService.weighDataQuery(appKeyHeader, weighDataAssembleForm);
        return SingleResponse.of(weighDataAssemble);
    }

    @ApiOperation(value = "获取推送数据", nickname = "dataPushGet")
    @GetMapping("/weigh/data/get")
    public SingleResponse<?> dataPushGet(@RequestParam String recordId) {
        return SingleResponse.of(weighDataAssembleService.getPushData(recordId));
    }

    @ApiOperation(value = "获取推送照片", nickname = "picPushGet")
    @GetMapping("/weigh/pic/get")
    public SingleResponse<?> picPushGet(@RequestParam Long id) {
        WeighPushPicDTO pushPic = weighDataAssembleService.getPushPic(id);
        return SingleResponse.of(pushPic);
    }

    @ApiOperation(value = "确认推送数据被接收", nickname = "dataPushConfirm")
    @GetMapping("/weigh/data/pushConfirm")
    public SingleResponse<?> PushDataConfirm(@RequestParam String recordId) {
        boolean flag = weighDataAssembleService.PushDataConfirm(recordId);
        return SingleResponse.of(flag);
    }

    @ApiOperation(value = "确认推送照片被接收", nickname = "picPushConfirm")
    @GetMapping("/weigh/pic/pushConfirm")
    public SingleResponse<?> PushPicConfirm(@RequestParam Long id) {
        boolean flag = weighDataAssembleService.PushPicConfirm(id);
        return SingleResponse.of(flag);
    }

    @ApiOperation(value = "OCR识别", nickname = "matchModule")
    @PostMapping("/ocr")
    public SingleResponse<String> ocr(HttpServletRequest request, PicForm form) {
//        String appKeyHeader = request.getHeader(SignatureConstant.APP_KEY_PARAM_HEADER_NAME);
        String res = weighDataAssembleService.ocr(form);
        return SingleResponse.of(res);
    }

    @ApiOperation(value = "模版匹配", nickname = "matchModule")
    @PostMapping("/match/module")
    public SingleResponse<String> matchModule(HttpServletRequest request,
                                              @RequestBody MatchForm form) {
        String appKeyHeader = request.getHeader(SignatureConstant.APP_KEY_PARAM_HEADER_NAME);
        String res = weighDataAssembleService.matchModule(appKeyHeader, form);
        return SingleResponse.of(res);
    }

    @ApiOperation(value = "确认推送单据回收数据被接收", nickname = "pushReceiptRecycleConfirm")
    @GetMapping("/receiptRecycle/pushConfirm")
    public SingleResponse<?> pushReceiptRecycleConfirm(@RequestParam Long id) {
        boolean flag = weighDataAssembleService.pushReceiptRecycleConfirm(id);
        return SingleResponse.of(flag);
    }

    @ApiOperation(value = "获取图片预览/下载地址", nickname = "getPicUrl")
    @PostMapping("/getPicUrl")
    public SingleResponse<List<String>> getPicUrl(@RequestBody PicForm form) {
        List<String> uuids = weighDataAssembleService.getPicUrl(form);
        return SingleResponse.of(uuids);
    }

    @ApiOperation(value = "订单新增(采购单)", nickname = "purchaseAdd")
    @PostMapping("/purchase/add")
    public SingleResponse<?> purchaseAdd(@Validated @RequestBody PurchaseForm form, HttpServletRequest request) {
        String appKeyHeader = request.getHeader(SignatureConstant.APP_KEY_PARAM_HEADER_NAME);
        PurchaseResDTO dto = purchaseOrderService.add(appKeyHeader, form);
        return SingleResponse.of(dto);
    }

    @ApiOperation(value = "订单作废(采购单)", nickname = "purchaseCancel")
    @GetMapping("/purchase/cancel")
    public SingleResponse<?> purchaseCancel(@RequestParam String orderExtId, HttpServletRequest request) {
        String appKeyHeader = request.getHeader(SignatureConstant.APP_KEY_PARAM_HEADER_NAME);
        purchaseOrderService.cancel(appKeyHeader, orderExtId);
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "订单收货完毕(采购单)", nickname = "purchaseFinish")
    @GetMapping("/purchase/finish")
    public SingleResponse<?> purchaseFinish(@RequestParam String orderExtId, HttpServletRequest request) {
        String appKeyHeader = request.getHeader(SignatureConstant.APP_KEY_PARAM_HEADER_NAME);
        purchaseOrderService.finish(appKeyHeader, orderExtId);
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "确认单详情", nickname = "confirmDetail")
    @GetMapping("/confirmDetail")
    public SingleResponse<WeighDataConfirmDetailDTO> confirmDetail(@RequestParam String id) {
        WeighDataConfirmDetailDTO detail = weighDataConfirmService.detail(id, null);
        return SingleResponse.of(detail);
    }

    @ApiOperation(value = "确认单分页查询", nickname = "confirmQuery")
    @PostMapping("/weigh/confirm/query")
    public SingleResponse<PageList<WeighDataConfirmDetailDTO>> confirmQuery(@RequestBody WeighDataQuery query) {
        PageList<WeighDataConfirmDetailDTO> page = weighDataConfirmService.confirmQuery(query);
        return SingleResponse.of(page);
    }

    @ApiOperation(value = "原始记录分页查询", nickname = "confirmQuery")
    @PostMapping("/weigh/data/query")
    public SingleResponse<PageList<WeighDataConfirmDetailDTO>> dataQuery(@RequestBody WeighDataQuery query) {
        PageList<WeighDataConfirmDetailDTO> page = weighDataService.dataQuery(query);
        return SingleResponse.of(page);
    }

    @ApiOperation(value = "回收数据列表", nickname = "recycleSimpleList")
    @PostMapping("/recycle/list")
    public SingleResponse<?> recycleSimpleList(HttpServletRequest request, @RequestBody QueryPage<RecycleQuery> query) {
        String appKeyHeader = request.getHeader(SignatureConstant.APP_KEY_PARAM_HEADER_NAME);
        PageList<ReceiptRecycleDTO> page = receiptRecycleService.recycleSimpleList(appKeyHeader, query);
        return SingleResponse.of(page);
    }

    @ApiOperation(value = "回收数据详情", nickname = "recycleDetail")
    @GetMapping("/recycle/detail")
    public SingleResponse<?> recycleDetail(HttpServletRequest request, @RequestParam Long id) {
        String appKeyHeader = request.getHeader(SignatureConstant.APP_KEY_PARAM_HEADER_NAME);
        ReceiptRecyclePushDTO result = receiptRecycleService.recycleDetail(appKeyHeader, id);
        return SingleResponse.of(result);
    }

    @ApiOperation(value = "运单新增", nickname = "deliveryAdd")
    @PostMapping("/delivery/add")
    public SingleResponse<?> deliveryAdd(HttpServletRequest request, @Validated @RequestBody DeliverySyncForm form) {
        String appKeyHeader = request.getHeader(SignatureConstant.APP_KEY_PARAM_HEADER_NAME);
        String deliveryNo = deliveryService.deliveryAdd(form, appKeyHeader);
        return SingleResponse.of(deliveryNo);
    }

    @ApiOperation(value = "iframe获取token", nickname = "iframeGetToken")
    @PostMapping("/token")
    public SingleResponse<?> token(HttpServletRequest request) {
        String appKeyHeader = request.getHeader(SignatureConstant.APP_KEY_PARAM_HEADER_NAME);
        UserDO userDO = userService.getUserByAppKey(appKeyHeader);
        String uid = userDO.getUid();
        // 检查是否有token，没有则生成
        String token = StpUtil.getTokenValueByLoginId(uid, "IFRAME");
//        if (StrUtil.isBlank(token)) {
        StpKit.DEFAULT.login(uid, "IFRAME");
        token = StpKit.DEFAULT.getTokenValue();
//        }
        return SingleResponse.of(token);
    }

    @ApiOperation(value = "确认订单被接受", nickname = "pushPurchaseConfirm")
    @GetMapping("/purchase/pushConfirm")
    public SingleResponse<?> pushPurchaseConfirm(HttpServletRequest request, @RequestParam String purchaseId) {
        String appKeyHeader = request.getHeader(SignatureConstant.APP_KEY_PARAM_HEADER_NAME);
        boolean flag = purchaseOrderService.pushPurchaseConfirm(Long.valueOf(purchaseId), appKeyHeader);
        return SingleResponse.of(flag);
    }

    @ApiOperation(value = "称重曲线详情", nickname = "curveDetail")
    @GetMapping("/weigh/curve/get")
    public SingleResponse<WeighCurveVO> curveDetail(@RequestParam String id) {
        WeighCurveVO vo = new WeighCurveVO();
        cn.pinming.microservice.material.client.management.common.vo.WeighCurveVO detail = weighCurveService.detail(id);
        if (ObjectUtil.isNotNull(detail)) {
            vo.setTimes(detail.getTimes());
            vo.setWeights(detail.getWeights());
            if (CollUtil.isNotEmpty(detail.getWeighPoints())) {
                List<WeighDataSavedDTO> collect = detail.getWeighPoints().stream().map(e -> {
                    WeighDataSavedDTO weighDataSavedDTO = new WeighDataSavedDTO();
                    BeanUtils.copyProperties(e, weighDataSavedDTO);
                    return weighDataSavedDTO;
                }).collect(Collectors.toList());
                vo.setWeighPoints(collect);
            }
        }
        return SingleResponse.of(vo);
    }

    @ApiOperation(value = "获取终端设备作弊日志", nickname = "cheatAlarmLog")
    @PostMapping("/cheat/alarmLog")
    public SingleResponse<?> cheatAlarmLog(HttpServletRequest request, @RequestBody CheatAlarmLogQuery query) {
        String appKeyHeader = request.getHeader(SignatureConstant.APP_KEY_PARAM_HEADER_NAME);
        UserDO userDO = userService.getUserByAppKey(appKeyHeader);
        List<CheatAlarmLogVO> result = new ArrayList<>();
        if (ObjectUtil.isNull(query.getStartTime()) || ObjectUtil.isNull(query.getEndTime())) {
            return SingleResponse.buildSuccess();
        }
        List<CheatAlarmLogDO> list = cheatAlarmLogService.lambdaQuery()
                .eq(CheatAlarmLogDO::getDeviceSn, query.getDeviceSn())
                .eq(CheatAlarmLogDO::getUid, userDO.getUid())
                .between(CheatAlarmLogDO::getCreateTime, query.getStartTime(), query.getEndTime())
                .list();
        if (CollUtil.isNotEmpty(list)) {
            List<String> fileIdList = list.stream().filter(e -> StrUtil.isNotBlank(e.getFileId())).flatMap(e -> StrUtil.split(e.getFileId(), ",").stream()).collect(Collectors.toList());
            Map<String, String> downloadMap = new HashMap<>();
            if (CollUtil.isNotEmpty(fileIdList)) {
                DownloadUrlOptionsDto options = new DownloadUrlOptionsDto();
                options.setTime(Date.from(LocalDate.now().plusYears(10).atStartOfDay(ZoneId.systemDefault()).toInstant()));
                List<FileIdentityDto> fileIdentities = fileIdList.stream().map(e -> {
                    FileIdentityDto dto = new FileIdentityDto();
                    dto.setFileUuid(e);
                    return dto;
                }).collect(Collectors.toList());
                Map<FileIdentityDto, String> downloadDtoMap = fileCenterService.acquireFileDownloadUrls(fileIdentities, options);
                if (CollUtil.isNotEmpty(downloadDtoMap)) {
                    downloadDtoMap.forEach((k, v) -> downloadMap.put(k.getFileUuid(), v));
                }
            }
            result = list.stream().map(e -> {
                CheatAlarmLogVO cheatAlarmLogVO = new CheatAlarmLogVO();
                BeanUtils.copyProperties(e, cheatAlarmLogVO);
                if (CollUtil.isNotEmpty(downloadMap) && StrUtil.isNotBlank(e.getFileId())) {
                    String pic = StrUtil.split(e.getFileId(), ",").stream().map(downloadMap::get).collect(Collectors.joining(","));
                    cheatAlarmLogVO.setFileUrl(pic);
                }
                return cheatAlarmLogVO;
            }).collect(Collectors.toList());
        }
        return SingleResponse.of(result);
    }

    @ApiOperation(value = "获取原始称重记录风险结果", nickname = "weighCurveAlarm")
    @GetMapping("/weigh/curve/alarm")
    public SingleResponse<?> weighCurveAlarm(@RequestParam String recordId) {
        WeighCurveAlarmVO result = new WeighCurveAlarmVO();

//        WeighCurveDO curveDO = weighCurveService.lambdaQuery().eq(WeighCurveDO::getRecordId, recordId).one();
        WeighCurveAlarmDO one = weighCurveAlarmService.lambdaQuery().eq(WeighCurveAlarmDO::getRecordId, recordId).one();
        if (ObjectUtil.isNotNull(one)) {
            BeanUtils.copyProperties(one, result);

            // 组装指标项列表
            List<String> itemList = Arrays.asList("平台期", "曲线总时长", "称重点最值差", "防控仪监控", "是否跟车");
            List<WeighCurveAlarmItemVO> alarmItemList = itemList.stream().map(item -> {
                WeighCurveAlarmItemVO alarmItem = new WeighCurveAlarmItemVO();
                alarmItem.setItem(item);
                String scope;
                String value;
                if ("平台期".equals(item)) {
                    List<String> split = StrUtil.split(result.getPlatformData(), "/");
                    value = StrUtil.format("{}个平台期,依次为:\r\n{}", split.size(), result.getPlatformData());
                    alarmItem.setValue(value);
                    scope = StrUtil.format("1~{}个平台期,平台期时长{}秒", result.getPlatformCount(), result.getPlatformDuration());
                    alarmItem.setScope(scope);
                } else if ("曲线总时长".equals(item)) {
                    long between = LocalDateTimeUtil.between(result.getStartTime(), result.getEndTime(), ChronoUnit.MILLIS);
                    value = cn.hutool.core.date.DateUtil.formatBetween(between, BetweenFormatter.Level.SECOND);
                    alarmItem.setValue(value);
                    scope = StrUtil.format("< {}秒", result.getSustainDuration());
                    alarmItem.setScope(scope);
                } else if ("称重点最值差".equals(item)) {
                    value = StrUtil.format("{}千克", result.getWeightDiff());
                    alarmItem.setValue(value);
                    scope = StrUtil.format("-{}千克 ~ +{}千克", result.getWeight(), result.getWeight());
                    alarmItem.setScope(scope);
                } else if ("防控仪监控".equals(item)) {
                    Integer alarmPeriod = result.getAlarmPeriod();
                    LocalDateTime lastAlarmTime = result.getLastAlarmTime();
                    String lastAlarmTimeStr = "";
                    if (lastAlarmTime != null) {
                        // 距离曲线开始时间最近的一次作弊日志：2025-04-27 15:38:13， 相距35秒
                        lastAlarmTimeStr = StrUtil.format("距离曲线开始时间最近的一次作弊日志：{}, 相距{}秒",
                                DateUtil.parseLocalDateTime(lastAlarmTime),
                                LocalDateTimeUtil.between(lastAlarmTime, result.getStartTime(), ChronoUnit.SECONDS));
                    }

                    value = StrUtil.format("曲线起止时间内: {}\r\n{}", alarmPeriod > 0 ? "发生" : "未发生", lastAlarmTimeStr);
                    alarmItem.setValue(value);
                    scope = StrUtil.format("曲线时间内：不可发生; 距离曲线开始前{}秒内不可发生", result.getAlarmTime());
                    alarmItem.setScope(scope);
                } else if ("是否跟车".equals(item)) {
                    value = result.getFollowTruck() == 1 ? "是" : "否";
                    alarmItem.setValue(value);
                    alarmItem.setScope("不可发生");
                }
                return alarmItem;
            }).collect(Collectors.toList());
            result.setAlarmItemList(alarmItemList);
        }
        return SingleResponse.of(result);
    }

    @ApiOperation(value = "获取ocr单据模板列表", nickname = "ocrTemplateList")
    @PostMapping("/ocr/template/list")
    public SingleResponse<?> ocrTemplateList(HttpServletRequest request, @RequestBody OcrTemplateQuery query) {
        String appKeyHeader = request.getHeader(SignatureConstant.APP_KEY_PARAM_HEADER_NAME);
        UserDO userDO = userService.getUserByAppKey(appKeyHeader);
        String uid = userDO.getUid();

        List<String> contractIds = query.getContractIds();
        List<OcrTemplateVO> result = new ArrayList<>();
        if (CollUtil.isNotEmpty(contractIds)){
            List<OcrModuleDO> list = ocrModuleService.lambdaQuery().eq(OcrModuleDO::getUid, uid)
                    .in(OcrModuleDO::getExtId, contractIds)
                    .list();
            if (CollUtil.isNotEmpty(list)) {
                result = BeanUtil.copyToList(list, OcrTemplateVO.class);
            }
        }
        return SingleResponse.of(result);
    }

    @ApiOperation(value = "获取ocr单据匹配列表", nickname = "ocrRecycleList")
    @PostMapping("/ocr/recycle/list")
    public SingleResponse<?> ocrRecycleList(HttpServletRequest request, @RequestBody ReceiptRecycleQuery query) {
        String appKeyHeader = request.getHeader(SignatureConstant.APP_KEY_PARAM_HEADER_NAME);
        UserDO userDO = userService.getUserByAppKey(appKeyHeader);
        String uid = userDO.getUid();
        ReceiptRecycleDataVO result = receiptRecycleService.ocrRecycleList(uid, query);
        return SingleResponse.of(result);
    }

    @ApiOperation(value = "ocr单据图片", nickname = "ocrRecyclePicList")
    @PostMapping("/ocr/recycle/pic")
    public SingleResponse<?> ocrRecyclePicList(@RequestParam Long recycleId) {
        ReceiptRecyclePicVO result = receiptRecycleService.ocrRecyclePic(recycleId);
        return SingleResponse.of(result);
    }

    @ApiOperation(value = "称重记录图片", nickname = "recordPicList")
    @PostMapping("/record/pic/list")
    public SingleResponse<?> recordPicList(@RequestBody List<String> recordIds) {
        List<RecordPicDTO> result = new ArrayList<>();
        if (CollUtil.isNotEmpty(recordIds)) {
            List<WeighDataPicDO> list = weighDataPicService.lambdaQuery().in(WeighDataPicDO::getRecordId, recordIds)
                    .select(WeighDataPicDO::getRecordId, WeighDataPicDO::getFilePath, WeighDataPicDO::getFileId)
                    .list();
            if (CollUtil.isNotEmpty(list)) {
                Map<String, List<PushDataPicDTO>> weighDataIdPicMap = new HashMap<>();
                Map<String, String> recordPicUuidList = list.stream().collect(Collectors.toMap(WeighDataPicDO::getFileId, WeighDataPicDO::getFilePath));
                if (CollUtil.isNotEmpty(recordPicUuidList)) {
                    DownloadUrlOptionsDto options = new DownloadUrlOptionsDto();
                    options.setTime(Date.from(LocalDate.now().plusYears(10).atStartOfDay(ZoneId.systemDefault()).toInstant()));
                    List<FileIdentityDto> fileIdentities = recordPicUuidList.keySet().stream().map(e -> {
                        FileIdentityDto dto = new FileIdentityDto();
                        dto.setFileUuid(e);
                        return dto;
                    }).collect(Collectors.toList());
                    Map<FileIdentityDto, String> downloadDtoMap = fileCenterService.acquireFileDownloadUrls(fileIdentities, options);
                    if (CollUtil.isNotEmpty(downloadDtoMap)) {
                        Map<String, String> downloadMap = new HashMap<>();
                        downloadDtoMap.forEach((k, url) -> downloadMap.put(k.getFileUuid(), url));
                        Map<String, List<WeighDataPicDO>> collect = list.stream().collect(Collectors.groupingBy(WeighDataPicDO::getRecordId));
                        collect.forEach((recordId, v) -> {
                            List<PushDataPicDTO> picDTOList = new ArrayList<>();
                            if (CollUtil.isNotEmpty(v)) {
                                v.forEach(t->{
                                    String fileId = t.getFileId();
                                    if (downloadMap.containsKey(fileId)) {
                                        PushDataPicDTO picDTO = new PushDataPicDTO();
                                        picDTO.setPic(downloadMap.get(fileId));
                                        String[] split = t.getFilePath().split("/");
                                        picDTO.setCamNo(split[2]);
                                        picDTOList.add(picDTO);
                                    }
                                });
                            }
                            weighDataIdPicMap.put(recordId, picDTOList);
                        });
                    }
                }

                if (!weighDataIdPicMap.isEmpty()) {
                    weighDataIdPicMap.forEach((k, v) -> {
                        RecordPicDTO recordPicVO = new RecordPicDTO();
                        recordPicVO.setRecordId(k);
                        if (weighDataIdPicMap.containsKey(k)) {
                            List<PushDataPicDTO> picList = weighDataIdPicMap.get(k);
                            List<PicDTO> picFormList = BeanUtil.copyToList(picList, PicDTO.class);
                            recordPicVO.setPicList(picFormList);
                        }
                        result.add(recordPicVO);
                    });
                }
            }
        }
        return SingleResponse.of(result);
    }

    @ApiOperation(value = "称重主机列表", nickname = "deviceBindingList")
    @GetMapping("/device/binding/list")
    public SingleResponse<?> deviceBindingList(@RequestParam Integer projectId) {
        List<DeviceBindingDTO> result = deviceBindingExtMapper.getDeviceBindingList(projectId);
        return SingleResponse.of(result);
    }

}
