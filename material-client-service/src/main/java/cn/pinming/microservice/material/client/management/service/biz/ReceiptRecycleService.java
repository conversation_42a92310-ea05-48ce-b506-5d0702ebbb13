package cn.pinming.microservice.material.client.management.service.biz;

import cn.pinming.material.v2.model.PageList;
import cn.pinming.material.v2.model.QueryPage;
import cn.pinming.material.v2.model.WeighDataAssemble;
import cn.pinming.material.v2.model.dto.ReceiptRecycleDTO;
import cn.pinming.material.v2.model.dto.ReceiptRecyclePushDTO;
import cn.pinming.material.v2.model.form.WeighDataAssembleForm;
import cn.pinming.material.v2.model.query.RecycleQuery;
import cn.pinming.material.v2.model.vo.ReceiptRecycleDataVO;
import cn.pinming.material.v2.model.vo.ReceiptRecyclePicVO;
import cn.pinming.microservice.material.client.management.common.dto.ocr.SelfDeviceOcrDTO;
import cn.pinming.microservice.material.client.management.common.form.ReceiptRecycleExportForm;
import cn.pinming.microservice.material.client.management.common.form.ReceiptRecycleForm;
import cn.pinming.microservice.material.client.management.common.form.ReceiptRecycleUpdateForm;
import cn.pinming.microservice.material.client.management.common.model.DeviceAttributionDO;
import cn.pinming.microservice.material.client.management.common.model.ReceiptRecycleDO;
import cn.pinming.microservice.material.client.management.common.query.ReceiptRecycleQuery;
import cn.pinming.microservice.material.client.management.common.query.ReceiptRecycleResultQuery;
import cn.pinming.microservice.material.client.management.common.vo.ReceiptRecycleDetailsVO;
import cn.pinming.microservice.material.client.management.common.vo.ReceiptRecycleResultVO;
import cn.pinming.microservice.material.client.management.common.vo.ReceiptRecycleVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * <p>
 * 单据回收表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-03
 */
public interface ReceiptRecycleService extends IService<ReceiptRecycleDO> {

    IPage<ReceiptRecycleVO> pageList(ReceiptRecycleQuery query);

    ReceiptRecycleDetailsVO details(Long id);

    IPage<ReceiptRecycleResultVO> resultList(ReceiptRecycleResultQuery query);

    DeviceAttributionDO history();

    void cameraAdd(ReceiptRecycleForm form);

    void terminalRecycle(String deviceSn, MultipartFile[] files);

    WeighDataAssemble assembleWeigh(WeighDataAssembleForm assembleForm);

    void correctionWeigh(ReceiptRecycleUpdateForm form);

    void assembleModule(Long id);

    void correctionModule(ReceiptRecycleUpdateForm form);

    void chooseModule(ReceiptRecycleUpdateForm form);

    void successRecycle(Long id);

    void invalidRecycle(Long id);

    void resultExport(HttpServletResponse response, ReceiptRecycleExportForm form);

    void selfDeviceRecycle(SelfDeviceOcrDTO dto);

    PageList<ReceiptRecycleDTO> recycleSimpleList(String appKeyHeader, QueryPage<RecycleQuery> query);

    ReceiptRecyclePushDTO recycleDetail(String appKeyHeader, Long id);

    ReceiptRecycleDataVO ocrRecycleList(String uid, cn.pinming.material.v2.model.query.ReceiptRecycleQuery query);

    ReceiptRecyclePicVO ocrRecyclePic(Long recycleId);
}
