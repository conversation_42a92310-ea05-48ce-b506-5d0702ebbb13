package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 单据模板配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("d_receipt_module_config")
public class ReceiptModuleConfigDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 模版id
     */
    private Long moduleId;

    /**
     * 单据识别类型(1-自助称重类单据、2-非称重类单据)
     */
    private Integer ocrType;


}
