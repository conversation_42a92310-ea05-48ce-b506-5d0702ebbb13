package cn.pinming.microservice.material.client.management.common.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2023/8/9 14:28
 */
@Data
public class AppDailyLogDTO {

    private Long id;

    private String createId;

    /**
     * 已用订阅存储空间(G)
     */
    private BigDecimal usedSpace;

    /**
     * 已调用次数
     */
    private Long usedApiTotal;

    /**
     * 购买空间， 单位GB
     */
    private BigDecimal spaceSize;

    /**
     * 购买空间到期时间
     */
    private LocalDateTime spaceExpire;

    /**
     * 已购买服务调用总次数
     */
    private Long apiTotal;
}
