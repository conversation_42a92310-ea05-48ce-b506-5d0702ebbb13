package cn.pinming.microservice.material.client.management.common.mapper;

import cn.pinming.microservice.material.client.management.common.model.CheckInfoDO;
import cn.pinming.microservice.material.client.management.common.vo.CheckMaterialReverseVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface CheckInfoMapper extends BaseMapper<CheckInfoDO> {

    CheckMaterialReverseVO totalCheckVO(@Param("id") Long id);
}
