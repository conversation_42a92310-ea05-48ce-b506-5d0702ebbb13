package cn.pinming.microservice.material.client.management.common.form;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 *
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2023/8/7 17:36
 */
@Data
public class UserInvocationForm {

    @NotNull(message = "用户id")
    private String uid;

    @NotNull(message = "修改的服务调用次数")
    private Long modifyApiTotal;

    @NotNull(message = "修改类型(2-称重数据服务 3-单据匹配服务)，默认2")
    private Integer modifyType = 2;
}
