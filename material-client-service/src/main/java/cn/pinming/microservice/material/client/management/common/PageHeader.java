package cn.pinming.microservice.material.client.management.common;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

public class PageHeader<T> extends Page<T> {

    private List<Column> headers;

    private Integer ocrType;

    public Integer getOcrType() {
        return ocrType;
    }
    public void setOcrType(Integer ocrType) {
        this.ocrType = ocrType;
    }

    public List<Column> getHeaders() {
        return headers;
    }
    public void setHeaders(List<Column> headers) {
        this.headers = headers;
    }

    public PageHeader(long current, long size, long total, List<Column> headers, Integer ocrType) {
        super(current, size, total);
        this.headers = headers;
        this.ocrType = ocrType;
    }

    public PageHeader(long current, long size, List<Column> headers, Integer ocrType) {
        super(current, size);
        this.headers = headers;
        this.ocrType = ocrType;
    }

    public static class Column {

        private String columnName;

        private String columnKey;

        public Column(String columnName, String columnKey) {
            this.columnName = columnName;
            this.columnKey = columnKey;
        }

        public String getColumnName() {
            return columnName;
        }
        public void setColumnName(String columnName) {
            this.columnName = columnName;
        }
        public String getColumnKey() {
            return columnKey;
        }
        public void setColumnKey(String columnKey) {
            this.columnKey = columnKey;
        }
    }
}
