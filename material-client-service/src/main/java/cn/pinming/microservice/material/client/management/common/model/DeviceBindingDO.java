package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 设备管理-设备数据绑定表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("s_device_binding")
public class DeviceBindingDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 设备数据归属方表主键ID
     */
    private Long attributionId;

    /**
     * 设备id
     */
    private Long deviceId;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 用户id
     */
    private String uid;

    /**
     * 绑定时间
     */
    private LocalDateTime bindingTime;

    /**
     * 终端密码
     */
    private String password;

    /**
     * 外部辅助码
     */
    private String auxiliaryCode;

    /**
     * 0 开始接收 1 暂停接收
     */
    private Byte receive;

    /**
     * 司机自助确认设备模式 1 运单模式  2 单据回收模式 3 双模式（1,2）(仅适用于独立小设备) 4 仅扫码模式（仅适用于独立小设备） 5 仅毛皮重模式（仅适用于新型一体机终端） 6 简单称重模式（仅适用于新型一体机终端）
     */
    private Integer selfCheckMode;

    /**
     * 组装超时时间 单位 分
     */
    private Integer timeout;

    private Integer sameTruckMinDuration;

    /**
     * 是否开启签名 0 关闭 1 开启
     */
    private Integer signature;

    /**
     * 是否跳过扫码 0 关闭 1 开启
     */
    private Integer skipScanCode;

    /**
     * 是否自动称重 0 关闭 1 开启 (仅毛皮重模式)
     */
    private Integer autoWeight;

    /**
     * 消息机器人id 逗号分割
     */
    private String msgRobot;

    /**
     * 消息接收人手机号 逗号分割
     */
    private String msgReceiver;

    /**
     * 本地ip
     */
    private String localIp;
}
