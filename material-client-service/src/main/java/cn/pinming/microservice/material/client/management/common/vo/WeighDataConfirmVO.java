package cn.pinming.microservice.material.client.management.common.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class WeighDataConfirmVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("确认单单号")
    private String confirmNo;

    @ApiModelProperty("归属方名称")
    private String attributionName;

    @ApiModelProperty("设备机器码")
    private String deviceSn;

    @ApiModelProperty("车牌号")
    private String truckNo;

    @ApiModelProperty("供应商名称")
    private String supplierName;

    @ApiModelProperty("司机")
    private String driver;

    @ApiModelProperty("材料名称")
    private String material;

    @ApiModelProperty("实重")
    private BigDecimal weightActual;

    @ApiModelProperty("实际数量")
    private BigDecimal actualCount;

    @ApiModelProperty("结算单位")
    private String weightUnit;

    @ApiModelProperty("称重类型 1 收料过磅 2 发料过磅")
    private Byte weighType;

    @ApiModelProperty("创建时间")
    private String gmtCreate;

    @ApiModelProperty("订单明细")
    private String deliveryDetailIds;

    @ApiModelProperty("毛重")
    private BigDecimal weightGross;

    @ApiModelProperty("皮重")
    private BigDecimal weightTare;

    @ApiModelProperty("扣重")
    private BigDecimal weightDeduct;

    @ApiModelProperty("含水率")
    private BigDecimal moistureContent;

    @ApiModelProperty("换算系数")
    private BigDecimal ratio;

    @ApiModelProperty("面单应收量：发货数量")
    private BigDecimal weightSend;

    @ApiModelProperty("偏差量")
    private BigDecimal deviationCount;

    @ApiModelProperty("偏差率")
    private BigDecimal deviationRate;

    @ApiModelProperty("进场时间")
    private LocalDateTime enterTime;

    @ApiModelProperty("出场时间")
    private LocalDateTime leaveTime;

    @ApiModelProperty("推送状态 1 未推送 2 已推送 3 推送失败")
    private Integer pushState;

    @ApiModelProperty("发货单id")
    private Long deliveryId;

}
