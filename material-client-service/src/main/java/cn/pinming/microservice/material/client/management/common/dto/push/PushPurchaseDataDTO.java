package cn.pinming.microservice.material.client.management.common.dto.push;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 基石转发订单数据体给第三方json结构体-订单信息
 */
@Data
public class PushPurchaseDataDTO {
    @ApiModelProperty(value = "收货人姓名，可为空，建议设置")
    private String cargoReceiver;

    @ApiModelProperty(value = "订单拥有者的地址，一般指项目收货地址，可为空，但建议设置")
    private String orderAddress;

    @ApiModelProperty(value = "订单拥有者的名称，一般指项目名称，不能为空")
    private String orderName;

    @ApiModelProperty(value = "来源业务系统中此订单的唯一ID，不能为空")
    private String purchaseId;

    @ApiModelProperty(value = "收货人电话，可以为空，但建议设置")
    private String receiverTelNumber;

    @ApiModelProperty(value = "订单备注，可为空")
    private String remark;

    @ApiModelProperty(value = "要货日期，不能为空")
    private String requireDate;

    @ApiModelProperty(value = "供应商id，不能为空")
    private String supplierId;

    @ApiModelProperty(value = "供应商名称，不能为空")
    private String supplierName;

    @ApiModelProperty(value = "订单明细列表")
    private List<PushPurchaseMaterialDTO> cargoList;
}
