package cn.pinming.microservice.material.client.management.common.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 原始记录称重曲线报警日志
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
@Data
@TableName("d_weigh_curve_alarm")
public class WeighCurveAlarmDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 终端称重曲线id
     */
    private String weighCurveId;

    /**
     * 称重曲线id
     */
    private String curveId;

    /**
     * 终端记录id
     */
    private String recordId;

    /**
     * 终端称重曲线开始时间
     */
    private LocalDateTime startTime;

    /**
     * 终端称重曲线结束时间
     */
    private LocalDateTime endTime;

    /**
     * 上一次报警日志时间
     */
    private LocalDateTime lastAlarmTime;

    /**
     * 称重曲线期间是否有报警日志  0 无 1 有
     */
    private Integer alarmPeriod;

    /**
     * 平台期时长 秒
     */
    private Integer platformDuration;

    /**
     * 平台期个数
     */
    private Integer platformCount;

    /**
     * 平台期数据
     */
    private String platformData;

    /**
     * 称重点最值差
     */
    private BigDecimal weightDiff;

    private BigDecimal weightMax;

    /**
     * 持续时长 （秒）
     */
    private Integer sustainDuration;

    /**
     * 称重点最值差设定值
     */
    private BigDecimal weight;

    /**
     * 防控仪监控时间
     */
    private Integer alarmTime;

    /**
     * 是否跟车 0 否 1 是
     */
    private Integer followTruck;

    /**
     * 告警项快照
     */
    private String alarmItem;

    /**
     * 告警消息原因
     */
    private String alarmReason;

    /**
     * 告警等级 1 告警 2 提示
     */
    private Integer alarmLevel;

    @TableLogic
    private Boolean deleted;

}
