package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.StrUtil;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizErrorException;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizExceptionMessageEnum;
import cn.pinming.microservice.material.client.management.infrastructure.util.UserIdUtil;
import cn.pinming.microservice.material.client.management.common.form.GatewayForm;
import cn.pinming.microservice.material.client.management.common.form.PushConfigForm;
import cn.pinming.microservice.material.client.management.common.mapper.PushUserConfigMapper;
import cn.pinming.microservice.material.client.management.common.model.PushUserConfigDO;
import cn.pinming.microservice.material.client.management.common.vo.push.PushRouteConfigVO;
import cn.pinming.microservice.material.client.management.service.biz.IPushUserConfigService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 租户推送配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-11
 */
@Service
public class PushUserConfigServiceImpl extends ServiceImpl<PushUserConfigMapper, PushUserConfigDO> implements IPushUserConfigService {

    @Resource
    private UserIdUtil userIdUtil;

//    private static final String SERVER_HOST_REGEX = "^(https?://)(?!localhost|127\\.0\\.0\\.1|192\\.168\\.\\d{1,3}\\.\\d{1,3})(?!.*\\.local(?:domain)?\\.com)[a-zA-Z0-9-]+(\\.[a-zA-Z0-9-]+)*(\\.[a-zA-Z]{2,})(/.*)?$";
//
//    private static final Pattern HOST_PATTERN = Pattern.compile(SERVER_HOST_REGEX);

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savePushConfig(PushConfigForm form) {
        String uid = userIdUtil.getUId();
        Long routeId = form.getId();
        List<Long> attributionIdList = form.getAttributionIdList();

        String excludeId = "";
        if (CollUtil.isNotEmpty(attributionIdList)) {
            excludeId = CollUtil.join(attributionIdList, StrUtil.COMMA);
        }

        PushUserConfigDO userConfigDO = lambdaQuery().eq(PushUserConfigDO::getCreateId, uid).eq(PushUserConfigDO::getRouteConfigId, routeId).one();
        if (userConfigDO == null) {
            userConfigDO = new PushUserConfigDO();
            userConfigDO.setRouteConfigId(routeId);
        }
        userConfigDO.setExcludeId(excludeId);
        saveOrUpdate(userConfigDO);
    }

    @Override
    public List<PushRouteConfigVO> selectOpenedRouteConfigList(Long routeConfigId) {
        return getBaseMapper().selectOpenedRouteConfigList(routeConfigId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveGateway(GatewayForm form) {
        String uid = userIdUtil.getUId();
        // 校验gateway格式
//        Matcher matcher = HOST_PATTERN.matcher(form.getGateway());
//        if (!matcher.matches()) {
//            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "推送网关格式不正确");
//        }
        if (!Validator.isUrl(form.getGateway())) {
            throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "推送网关格式不正确");
        }

        PushUserConfigDO userConfigDO = lambdaQuery().eq(PushUserConfigDO::getCreateId, uid).eq(PushUserConfigDO::getRouteConfigId, form.getRouteId()).one();
        if (userConfigDO == null) {
            userConfigDO = new PushUserConfigDO();
            userConfigDO.setRouteConfigId(form.getRouteId());
        }
        userConfigDO.setGateway(form.getGateway());
        saveOrUpdate(userConfigDO);
    }
}
