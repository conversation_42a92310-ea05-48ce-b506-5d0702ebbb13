package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.pinming.material.v2.model.*;
import cn.pinming.material.v2.model.query.WeighDataQuery;
import cn.pinming.microservice.material.client.management.common.BaseDO;
import cn.pinming.microservice.material.client.management.common.dto.*;
import cn.pinming.microservice.material.client.management.common.dto.ocr.SelfDeviceOcrDTO;
import cn.pinming.microservice.material.client.management.common.dto.push.*;
import cn.pinming.microservice.material.client.management.common.enums.DeliveryDetailStatusEnum;
import cn.pinming.microservice.material.client.management.common.enums.DeveloperAppEnum;
import cn.pinming.microservice.material.client.management.common.enums.DeveloperTypeEnum;
import cn.pinming.microservice.material.client.management.common.enums.DeviceTypeEnum;
import cn.pinming.microservice.material.client.management.common.form.ConfirmPicForm;
import cn.pinming.microservice.material.client.management.common.mapper.DeliveryDetailMapper;
import cn.pinming.microservice.material.client.management.common.mapper.WeighDataConfirmMapper;
import cn.pinming.microservice.material.client.management.common.mapper.ext.DeviceAttributionExtMapper;
import cn.pinming.microservice.material.client.management.common.mapper.ext.WeighDataConfirmExtMapper;
import cn.pinming.microservice.material.client.management.common.model.*;
import cn.pinming.microservice.material.client.management.common.model.ext.*;
import cn.pinming.microservice.material.client.management.common.query.ConfirmPullQuery;
import cn.pinming.microservice.material.client.management.common.query.WeighDataLocalQuery;
import cn.pinming.microservice.material.client.management.common.query.WeighDataPageQuery;
import cn.pinming.microservice.material.client.management.common.vo.*;
import cn.pinming.microservice.material.client.management.common.vo.delivery.DeliveryDetailDTO;
import cn.pinming.microservice.material.client.management.common.vo.delivery.DeliveryItemDTO;
import cn.pinming.microservice.material.client.management.infrastructure.constant.SdkQueryConstant;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizErrorException;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizExceptionMessageEnum;
import cn.pinming.microservice.material.client.management.infrastructure.util.DeliveryStatusUpdateUtil;
import cn.pinming.microservice.material.client.management.infrastructure.util.NoUtil;
import cn.pinming.microservice.material.client.management.infrastructure.util.PurchaseDeliveryStatusUpdateUtil;
import cn.pinming.microservice.material.client.management.proxy.FileServiceProxy;
import cn.pinming.microservice.material.client.management.service.biz.*;
import cn.pinming.microservice.material.client.management.service.business.CombineService;
import cn.pinming.zhjz.cxptz.microservice.filecenter.api.FileCenterService;
import cn.pinming.zhjz.cxptz.microservice.filecenter.api.model.DownloadUrlOptionsDto;
import cn.pinming.zhjz.cxptz.microservice.filecenter.api.model.FileIdentityDto;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 现场称重确认单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-22
 */
@Service
@Slf4j
public class WeighDataConfirmServiceImpl extends ServiceImpl<WeighDataConfirmMapper, WeighDataConfirmDO> implements IWeighDataConfirmService {
    @Resource
    private FileServiceProxy fileServiceProxy;
    @Resource
    private NoUtil noUtil;
    @Resource
    private IDeliveryService deliveryService;
    @Resource
    private IDeliveryDetailService deliveryDetailService;
    @Resource
    private DeliveryStatusUpdateUtil deliveryStatusUpdateUtil;
    @Resource
    private WeighDataConfirmExtMapper weighDataConfirmExtMapper;
    @Resource
    private DeviceService deviceService;
    @Resource
    private DeviceBindingService deviceBindingService;
    @Resource
    private DeliveryDetailMapper deliveryDetailMapper;
    @Resource
    private WeighDataService weighDataService;
    @Resource
    private FileOssService fileOssService;
    @Resource
    private DeveloperService developerService;
    @Resource
    private AppInvocationDailyLogService appInvocationDailyLogService;
    @Resource
    private PurchaseDeliveryStatusUpdateUtil purchaseDeliveryStatusUpdateUtil;
    @Resource
    private CombineService combineService;
    @Resource
    private DeviceAttributionService deviceAttributionService;
    @Resource
    private WeighDataPicService weighDataPicService;
    @Resource
    private WeighDataCombineService weighDataCombineService;
    @Resource
    private UserService userService;
    @Resource
    private ReceiptRecycleService receiptRecycleService;
    @Resource
    private IReceiptRecycleBatchService receiptRecycleBatchService;
    @Resource
    private DeviceAttributionExtMapper deviceAttributionExtMapper;
    @Resource
    private IWeighDataConfirmService weighDataConfirmService;
    @DubboReference
    private FileCenterService fileCenterService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(WeighDataConfirmOriginExtDO form) {
        WeighDataConfirmExtDO confirmData = form.getTerminalConfirmData();
        WeighDataCheckDTO checkDTO = weighDataService.check(form.getTerminalSn(), DeveloperAppEnum.QUERY.value(), false, DeviceTypeEnum.SELF_CHECK.name());
        DeliveryDO deliveryDO = deliveryService.lambdaQuery()
                .eq(DeliveryDO::getNo, confirmData.getJsDeliveryNo())
                .eq(DeliveryDO::getAttributionId, checkDTO.getAttributionId())
                .one();

        DeviceDO deviceDO = deviceService.lambdaQuery()
                .eq(DeviceDO::getDeviceSn, form.getTerminalSn()).eq(DeviceDO::getDeviceType, form.getDeviceType())
                .one();
        if (ObjectUtil.isNull(deviceDO)) {
            throw new BizErrorException(BizExceptionMessageEnum.DECEIVE_IS_DELETED);
        }
        if (deviceDO.getIsUsed() == 2) {
            throw new BizErrorException(BizExceptionMessageEnum.DEVICE_IS_USED);
        }
        DeviceBindingDO deviceBindingDO = deviceBindingService.lambdaQuery()
                .eq(DeviceBindingDO::getDeviceId, deviceDO.getId())
                .one();
        if (ObjectUtil.isNull(deviceBindingDO)) {
            throw new BizErrorException(BizExceptionMessageEnum.DEVICE_ATTRIBUTION_ERROR);
        }

        DeveloperDO developerDO = developerService.lambdaQuery()
                .select(DeveloperDO::getType)
                .eq(DeveloperDO::getAppId, DeveloperAppEnum.CONFIRM.value())
                .eq(BaseDO::getCreateId, deviceBindingDO.getUid())
                .one();
        if (developerDO.getType().equals(DeveloperTypeEnum.STOP.value())) {
            throw new BizErrorException(BizExceptionMessageEnum.SERVICE_DISABLED, DeveloperAppEnum.CONFIRM.description());
        }

        // 打印次数 + 设备sn
        WeighDataConfirmDO confirmDO = new WeighDataConfirmDO();
        WeighDataConfirmDO dataConfirmDO = this.lambdaQuery()
                .eq(WeighDataConfirmDO::getLocalId, confirmData.getTerminalDataId())
                .one();
        if (ObjectUtil.isNotNull(dataConfirmDO)) {
            confirmDO.setId(dataConfirmDO.getId());
        }
        confirmDO.setPrintCount(form.getActualPrintCounts());
        confirmDO.setDeviceSn(form.getTerminalSn());

        // 确认单本体数据
        confirmDO.setLocalCreateTime(confirmData.getTerminalDataCreateTime());
        confirmDO.setLocalModifyTime(confirmData.getTerminalDataModifyTime());
        confirmDO.setLocalId(confirmData.getTerminalDataId());
        if (ObjectUtil.isNotNull(deliveryDO)) {
            confirmDO.setDeliveryId(deliveryDO.getId());
            confirmDO.setPurchaseOrderId(deliveryDO.getPurchaseOrderId());
        }

        List<WeighDataDeliveryExtDO> confirmDataList = CollUtil.isEmpty(confirmData.getList()) ? Collections.emptyList() : confirmData.getList();

        // 运单明细
        List<WeighDataDeliveryExtDO> list = confirmDataList.stream().filter(e -> e.getIsConfirmed() == 1).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(list)) {
            confirmDO.setDeliveryDetailIds(list.stream().map(WeighDataDeliveryExtDO::getJsCargoId).collect(Collectors.joining(",")));
        }

        // 确认单辅助数据
        WeighDataConfirmDetailExtDO confirmDetail = confirmData.getConfirmData();
        if (ObjectUtil.isNotNull(confirmDetail)) {
            confirmDO.setTruckNo(confirmDetail.getTruckNo());
            confirmDO.setWeighType(confirmDetail.getWeighingType());
            confirmDO.setSignatureTime(confirmDetail.getSignatureTime());

            // 称重结果
            WeighResultExtDO weightResult = confirmDetail.getWeightResult();
            if (Objects.nonNull(weightResult)) {
                String unitInuse = weightResult.getUnitInuse();
                BigDecimal mul = BigDecimal.ONE;
                if (unitInuse.equals("千克")) {
                    mul = BigDecimal.valueOf(0.001);
                }
                WeighDataGrossExtDO grossWeight = weightResult.getGrossWeight();
                WeighDataTareExtDO tareWeight = weightResult.getTareWeight();
                if (Objects.nonNull(grossWeight) && Objects.nonNull(tareWeight)) {
                    confirmDO.setWeightGross(NumberUtil.mul(grossWeight.getWeightValue(),mul));
                    confirmDO.setWeightTare(NumberUtil.mul(tareWeight.getWeightValue(),mul));
                    confirmDO.setRecordId1(grossWeight.getSort() == 1 ? grossWeight.getOriginalId() : tareWeight.getOriginalId());
                    confirmDO.setRecordId2(grossWeight.getSort() == 2 ? grossWeight.getOriginalId() : tareWeight.getOriginalId());
                }
                confirmDO.setWeightDeduct(NumberUtil.mul(weightResult.getDeductWeight(),mul));
                confirmDO.setMoistureContent(weightResult.getDeductRatio());
                confirmDO.setEnterTime(weightResult.getEnterTime());
                confirmDO.setLeaveTime(weightResult.getLeaveTime());
            }

            // 换算结果
            convertResultExtDO convertResult = confirmDetail.getConvertResult();
            if (ObjectUtil.isNotNull(convertResult)) {
                confirmDO.setRatio(convertResult.getScaleFactor());
                confirmDO.setWeightUnit(convertResult.getUnitOfMeasurement());
                confirmDO.setWeightSend(convertResult.getWeightSend());
            }

            // 重新计算 净重 实重 实际数量 偏差量 偏差率
            confirmDO.setWeightNet(NumberUtil.sub(confirmDO.getWeightGross(), confirmDO.getWeightTare()));
            confirmDO.setWeightActual(NumberUtil.sub(confirmDO.getWeightNet(), confirmDO.getWeightDeduct()));
            confirmDO.setWeightActual(NumberUtil.mul(NumberUtil.sub(1, NumberUtil.div(confirmDO.getMoistureContent(), 100)), confirmDO.getWeightActual()));
            if (ObjectUtil.isNotNull(confirmDO.getRatio())) {
                confirmDO.setActualCount(NumberUtil.div(confirmDO.getWeightActual(), confirmDO.getRatio()));
                confirmDO.setDeviationCount(NumberUtil.sub(confirmDO.getActualCount(), confirmDO.getWeightSend()));
                confirmDO.setDeviationRate(NumberUtil.mul(NumberUtil.div(confirmDO.getDeviationCount(), confirmDO.getWeightSend()), 100));
            }
        }

        confirmDO.setConfirmNo(noUtil.getWeighDataConfirmKeyPrefixNo());
        confirmDO.setOriginJson(JSONObject.toJSONString(form));
        confirmDO.setAttributionId(deviceBindingDO.getAttributionId());
        confirmDO.setCreateId(deviceBindingDO.getUid());
        confirmDO.setModifyId(deviceBindingDO.getUid());
        confirmDO.setIsConfirmed(form.getIsConfirmed());

        List<WeighDataExtDO> weighDataList = form.getTerminalConfirmData().getWeighDataList();
        // 针对3型运单 保存记录id
        if (Objects.nonNull(deliveryDO) && CollUtil.isNotEmpty(weighDataList)) {
            for (int i = 0; i < weighDataList.size(); i++) {
                String recordId = weighDataList.get(i).getRecordId();
                if (i == 0) {
                    confirmDO.setRecordId1(recordId);
                }
                if (i == 1) {
                    confirmDO.setRecordId2(recordId);
                }
            }
        }
        this.saveOrUpdate(confirmDO);

        List<String> listWithConfirm = confirmDataList.stream().filter(e -> e.getIsConfirmed() == 1).map(WeighDataDeliveryExtDO::getJsCargoId).collect(Collectors.toList());
        List<String> listWithNonConfirm = confirmDataList.stream().filter(e -> e.getIsConfirmed() == 0).map(WeighDataDeliveryExtDO::getJsCargoId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(listWithConfirm)) {
            // 上传明细为"已确认"，云明细变更为"已确认"
            deliveryDetailService.lambdaUpdate()
                    .in(BaseDO::getId, listWithConfirm)
                    .set(DeliveryDetailDO::getStatus, DeliveryDetailStatusEnum.FOUR.value())
                    .update();
        }
        if (CollUtil.isNotEmpty(listWithNonConfirm)) {
            // 上传明细为"未确认"，云明细变更为"待确认"
            deliveryDetailService.lambdaUpdate()
                    .in(BaseDO::getId, listWithNonConfirm)
                    .set(DeliveryDetailDO::getStatus, DeliveryDetailStatusEnum.TWO.value())
                    .update();
        }

        // 运单状态变更
        deliveryStatusUpdateUtil.judge(deliveryDO, form.getIsConfirmed(), weighDataList);

        // 记录调用次数
        appInvocationDailyLogService.saveInvokeLog(DeveloperAppEnum.CONFIRM.value(), deviceBindingDO.getUid(), -1L, 1);

        // 订单的状态变更
//        purchaseDeliveryStatusUpdateUtil.judge(deliveryDO.getPurchaseOrderId());

        // 记录重组记录
        UserDO user = userService.lambdaQuery()
                .eq(UserDO::getUid, deviceBindingDO.getUid())
                .one();
        if (form.getIsConfirmed() != null && form.getIsConfirmed() == 1) {
            List<String> recordIdList = new ArrayList<>();
            recordIdList.add(confirmDO.getRecordId1());
            recordIdList.add(confirmDO.getRecordId2());
            List<WeighDataCombineDO> result = recordIdList.stream().map(e -> {
                WeighDataCombineDO weighDataCombineDO = new WeighDataCombineDO();
                weighDataCombineDO.setWeighDataId(e);
                weighDataCombineDO.setAppKey(user.getAppKey());
                weighDataCombineDO.setCreateId(deviceBindingDO.getUid());
                weighDataCombineDO.setAttributionId(deviceBindingDO.getAttributionId());
                return weighDataCombineDO;
            }).collect(Collectors.toList());
            List<WeighDataCombineDO> collect = result.stream().filter(Objects::nonNull).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(collect)) {
                weighDataCombineService.saveBatch(collect);
                log.info("{}确认数据重组完成", collect.stream().map(WeighDataCombineDO::getWeighDataId).collect(Collectors.toList()));
            }
        }
    }

    @Override
    public IPage<WeighDataConfirmVO> col(WeighDataPageQuery query) {
        IPage<WeighDataConfirmVO> page = weighDataConfirmExtMapper.col(query);

        if (CollUtil.isNotEmpty(page.getRecords())) {
            List<WeighDataConfirmVO> records = page.getRecords();

            List<WeighDataConfirmVO> listWithMaterial = records.stream().filter(e -> StrUtil.isNotBlank(e.getDeliveryDetailIds())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(listWithMaterial)) {
                Set<String> deliveryDetailIds = listWithMaterial.stream().flatMap(e -> Arrays.stream(e.getDeliveryDetailIds().split(","))).collect(Collectors.toSet());
                List<DeliveryDetailSimpleDTO> list = deliveryDetailMapper.selectMaterialByIds(deliveryDetailIds);
                Map<Long, String> materialMap = list.stream().collect(Collectors.toMap(DeliveryDetailSimpleDTO::getId, DeliveryDetailSimpleDTO::getMaterial));

                records.forEach(e -> {
                    if (StrUtil.isNotBlank(e.getDeliveryDetailIds())) {
                        List<Long> split = StrUtil.split(e.getDeliveryDetailIds(), ",").stream().map(Long::valueOf).collect(Collectors.toList());
                        String materialStr = split.stream().map(materialMap::get).collect(Collectors.joining(","));

                        e.setMaterial(materialStr);
                    }
                });
            }
        }
        return page;
    }

    @Override
    public WeighDataConfirmDetailDTO detail(String localId,Long id) {
        WeighDataConfirmDetailDTO vo = new WeighDataConfirmDetailDTO();

        WeighDataConfirmDO confirmDO = null;
        if (StrUtil.isNotBlank(localId)) {
            confirmDO = this.lambdaQuery()
                    .eq(WeighDataConfirmDO::getLocalId, localId)
                    .one();
        }
        if (ObjectUtil.isNotNull(id)) {
            confirmDO = this.lambdaQuery()
                    .eq(WeighDataConfirmDO::getId, id)
                    .one();
        }

        if (ObjUtil.isNotNull(confirmDO)) {
            BeanUtils.copyProperties(confirmDO, vo);

            WeighDataDO weighGross = new WeighDataDO();
            WeighDataDO weighTare = new WeighDataDO();
            WeighDataDO weighDataDO = weighDataService.lambdaQuery()
                    .eq(WeighDataDO::getRecordId, confirmDO.getRecordId1())
                    .one();
            WeighDataDO dataDO = weighDataService.lambdaQuery()
                    .eq(WeighDataDO::getRecordId, confirmDO.getRecordId2())
                    .one();
            weighGross = dataDO;
            weighTare = weighDataDO;
            try {
                if (weighTare.getWeight().compareTo(weighGross.getWeight()) > 0) {
                    weighGross = weighDataDO;
                    weighTare = dataDO;
                }
            } catch (Exception e) {
                return null;
            }

            vo.setLocalId(confirmDO.getLocalId());
            vo.setGrossId(weighGross.getRecordId());
            vo.setTareId(weighTare.getRecordId());
            vo.setGrossTime(weighGross.getWeighTime());
            vo.setTareTime(weighTare.getWeighTime());
            vo.setGrossRiskGrade(weighGross.getRiskGrade());
            vo.setTareRiskGrade(weighTare.getRiskGrade());

            if (StrUtil.isNotBlank(vo.getDocumentPic())) {
                vo.setDocumentPic(String.join(",", fileOssService.getUrlByUuids(StrUtil.split(vo.getDocumentPic(), ","))));
            }
            if (StrUtil.isNotBlank(vo.getSignerPic())) {
                vo.setSignerPic(String.join(",", fileOssService.getUrlByUuids(StrUtil.split(vo.getSignerPic(), ","))));
            }
            if (StrUtil.isNotBlank(vo.getSignPic())) {
                vo.setSignPic(String.join(",", fileOssService.getUrlByUuids(StrUtil.split(vo.getSignPic(), ","))));
            }

            List<WeighDataPicDO> tarePic = weighDataPicService.lambdaQuery()
                    .eq(WeighDataPicDO::getRecordId, weighTare.getRecordId())
                    .list();
            List<WeighDataPicDO> grossPic = weighDataPicService.lambdaQuery()
                    .eq(WeighDataPicDO::getRecordId, weighGross.getRecordId())
                    .list();
            if (CollUtil.isNotEmpty(tarePic)) {
                vo.setTarePic(String.join(",", fileOssService.getUrlByUuids(tarePic.stream().map(WeighDataPicDO::getFileId).collect(Collectors.toList()))));
            }
            if (CollUtil.isNotEmpty(grossPic)) {
                vo.setGrossPic(String.join(",", fileOssService.getUrlByUuids(grossPic.stream().map(WeighDataPicDO::getFileId).collect(Collectors.toList()))));
            }

            if (ObjUtil.isNotNull(confirmDO.getDeliveryId())) {
                List<String> split = StrUtil.split(confirmDO.getDeliveryDetailIds(), ",");

                DeliveryDetailDTO deliveryDetailVO = deliveryService.detailByDeliveryId(confirmDO.getDeliveryId());
                if (ObjectUtil.isNotNull(deliveryDetailVO)) {
                    List<DeliveryItemDTO> list = deliveryDetailVO.getList();
                    if (CollUtil.isNotEmpty(list)) {
                        list = list.stream().filter(e -> split.contains(String.valueOf(e.getId())) && e.getStatus().equals(DeliveryDetailStatusEnum.FOUR.value())).collect(Collectors.toList());
                        deliveryDetailVO.setList(list);
                    }
                }

                vo.setDeliveryDetailVO(deliveryDetailVO);
            }
        }

        return vo;
    }

    @Override
    public WeighDataAssemble getData(String deviceSn, String record1, String record2) {
        DeviceDO deviceDO = deviceService.lambdaQuery()
                .eq(DeviceDO::getDeviceSn, deviceSn)
                .eq(DeviceDO::getDeviceType, DeviceTypeEnum.SELF_CHECK.name())
                .one();
        if (ObjectUtil.isNull(deviceDO)) {
            throw new BizErrorException(BizExceptionMessageEnum.DECEIVE_IS_DELETED);
        }
        if (deviceDO.getIsUsed() == 2) {
            throw new BizErrorException(BizExceptionMessageEnum.DEVICE_IS_USED);
        }
        DeviceBindingDO deviceBindingDO = deviceBindingService.lambdaQuery()
                .eq(DeviceBindingDO::getDeviceId, deviceDO.getId())
                .one();
        if (ObjectUtil.isNull(deviceBindingDO)) {
            throw new BizErrorException(BizExceptionMessageEnum.DEVICE_ATTRIBUTION_ERROR);
        }

        WeighDataAssemble weighDataAssemble = combineService.weighDataAssembleWithoutExtCheck(Arrays.asList(record1, record2), deviceBindingDO.getUid(), deviceBindingDO.getAttributionId());
        long count = weighDataAssemble.getWeighData().stream().filter(WeighData::isCombine).count();
        if (count > 0) {
            throw new BizErrorException(BizExceptionMessageEnum.RECORD_HAS_USED);
        }

        return weighDataAssemble;
    }

    @Override
    public void savePic(ConfirmPicForm form) {
        String localId = form.getLocalId();
        Integer count = lambdaQuery().eq(WeighDataConfirmDO::getLocalId, localId).count();
        if (count > 0) {
            return;
        }

        WeighDataConfirmDO confirmDO = new WeighDataConfirmDO();

        WeighDataConfirmDO dataConfirmDO = this.lambdaQuery()
                .eq(WeighDataConfirmDO::getLocalId, form.getLocalId())
                .one();
        if (ObjectUtil.isNotNull(dataConfirmDO)) {
            confirmDO.setId(dataConfirmDO.getId());
        }

        confirmDO.setLocalId(form.getLocalId());
        if (form.getType().equals(ConfirmPicForm.TOP) || form.getType().equals(ConfirmPicForm.BOTTOM)) {
            if (ObjectUtil.isNotNull(dataConfirmDO) && StrUtil.isBlank(dataConfirmDO.getDocumentPic())) {
                confirmDO.setDocumentPic(form.getUuid());
            } else if (ObjectUtil.isNotNull(dataConfirmDO) && StrUtil.isNotBlank(dataConfirmDO.getDocumentPic())) {
                String format = StrUtil.format("{},{}", dataConfirmDO.getDocumentPic(), form.getUuid());
                confirmDO.setDocumentPic(format);
            } else if (ObjectUtil.isNull(dataConfirmDO)) {
                confirmDO.setDocumentPic(form.getUuid());
            }
        }
        if (form.getType().equals(ConfirmPicForm.SIGNATURE)) {
            confirmDO.setSignPic(form.getUuid());
        }
        if (form.getType().equals(ConfirmPicForm.PERSON)) {
            confirmDO.setSignerPic(form.getUuid());
        }

        log.info("上传图片完成================:{}", JSONUtil.toJsonStr(form));
        this.saveOrUpdate(confirmDO);
    }

    @Override
    public void confirmRecycle() {
        List<WeighDataConfirmDO> list = weighDataConfirmExtMapper.selectConfirmToRecycle();
        if (CollUtil.isNotEmpty(list)) {
            list.forEach(confirmDO -> {
                // 单据回收
                SelfDeviceOcrDTO dto = new SelfDeviceOcrDTO();
                dto.setDeviceSn(confirmDO.getDeviceSn());
                dto.setLocalId(confirmDO.getLocalId());
                dto.setWeighType(confirmDO.getWeighType());
                dto.setRecyclePics(StrUtil.split(confirmDO.getDocumentPic(), ","));
                dto.setTruckNo(confirmDO.getTruckNo());
                dto.setWeightGross(confirmDO.getWeightGross());
                dto.setWeightGrossTime(dto.getWeighType() == 1 ? confirmDO.getEnterTime() : confirmDO.getLeaveTime());
                dto.setWeightTare(confirmDO.getWeightTare());
                dto.setWeightTareTime(dto.getWeighType() == 1 ? confirmDO.getLeaveTime() : confirmDO.getEnterTime());
                dto.setWeightNet(confirmDO.getWeightNet());
                dto.setUnit("吨");
                dto.setRecycleTime(confirmDO.getSignatureTime());

                DeviceDO one = deviceService.lambdaQuery()
                        .eq(DeviceDO::getDeviceSn, dto.getDeviceSn())
                        .eq(DeviceDO::getDeviceType, DeviceTypeEnum.RECEIPT_RECYCLE.name())
                        .one();
                if (ObjectUtil.isNotNull(one)) {
                    ReceiptRecycleBatchDO batchDO = receiptRecycleBatchService.checkDeviceId(one.getId(), confirmDO.getCreateId());
                    if (ObjectUtil.isNotNull(batchDO)) {
                        if (batchDO.getAttributionId().equals(confirmDO.getAttributionId())) {
                            dto.setBatchId(batchDO.getId());
                        }
                    }
                }

                receiptRecycleService.selfDeviceRecycle(dto);
            });
        }
    }

    @Override
    public PageList<WeighDataConfirmDetailDTO> confirmQuery(WeighDataQuery query) {
        UserDO userDO = new UserDO();
        if (StringUtils.isNotBlank(query.getAppKey())) {
            userDO = userService.getUserByAppKey(query.getAppKey());
        } else {
            String uid = query.getUid();
            userDO = userService.getByUid(uid);
        }
        WeighDataLocalQuery queryLocal = new WeighDataLocalQuery();
        BeanUtils.copyProperties(query, queryLocal);
        queryLocal.setSize(SdkQueryConstant.DATA_MAX_SIZE);
        queryLocal.setCurrent(1);
        IPage<WeighDataConfirmDetailDTO> page = weighDataConfirmExtMapper.confirmQuery(userDO.getUid(), queryLocal);
        PageList<WeighDataConfirmDetailDTO> result = new PageList<>();
        BeanUtils.copyProperties(page, result);
        result.setDataList(page.getRecords());
        return result;
    }

    @Override
    public IPage<WeighConfirmPullVO> getConfirm(ConfirmPullQuery query) {
        IPage<WeighConfirmPullDTO> page = weighDataConfirmExtMapper.getConfirm(query);
        IPage<WeighConfirmPullVO> result = new Page<>();

        if (CollUtil.isNotEmpty(page.getRecords())) {
            List<WeighConfirmPullDTO> list = page.getRecords();
            Map<String, PushConfirmMaterialDTO> deliveryDetailDOMap = new HashMap<>();
            Map<String, String> weighDataIdPicMap = new HashMap<>();
            List<String> recordIdList = new ArrayList<>();
            List<String> recordPicUuidList = new ArrayList<>();
            Map<String, WeighDataDO> dataMap = new HashMap<>();
            Map<String,String> downloadMap = new HashMap<>();
            List<WeighDataPicDO> recordPicList = new ArrayList<>();

            // 签名、签名人、单据照片map
            List<String> picList = new ArrayList<>();
            List<String> signPic = list.stream().map(WeighConfirmPullDTO::getSignPic).filter(StrUtil::isNotBlank).collect(Collectors.toList());
            List<String> signerPic = list.stream().map(WeighConfirmPullDTO::getSignerPic).filter(StrUtil::isNotBlank).collect(Collectors.toList());
            List<String> documentPic = list.stream().filter(e -> StrUtil.isNotBlank(e.getDocumentPic())).flatMap(e -> StrUtil.split(e.getDocumentPic(), ",").stream()).collect(Collectors.toList());
            picList.addAll(signerPic);
            picList.addAll(signPic);
            picList.addAll(documentPic);
            // 原始记录信息、照片
            List<String> recordId1 = list.stream().map(WeighConfirmPullDTO::getRecordId1).distinct().collect(Collectors.toList());
            List<String> recordId2 = list.stream().map(WeighConfirmPullDTO::getRecordId2).distinct().collect(Collectors.toList());
            recordIdList.addAll(recordId1);
            recordIdList.addAll(recordId2);
            if (CollUtil.isNotEmpty(recordIdList)) {
                List<WeighDataDO> recordList = weighDataService.lambdaQuery()
                        .in(WeighDataDO::getRecordId, recordIdList)
                        .list();
                recordPicList = weighDataPicService.lambdaQuery()
                        .in(WeighDataPicDO::getRecordId, recordIdList)
                        .list();
                if (CollUtil.isNotEmpty(recordPicList)) {
                    recordPicUuidList = recordPicList.stream().map(WeighDataPicDO::getFileId).collect(Collectors.toList());
                    picList.addAll(recordPicUuidList);
                }
                if (CollUtil.isNotEmpty(recordIdList)) {
                    dataMap = recordList.stream().collect(Collectors.toMap(WeighDataDO::getRecordId, e -> e));
                }
            }
            if (CollUtil.isNotEmpty(picList)) {
                DownloadUrlOptionsDto options = new DownloadUrlOptionsDto();
                options.setTime(Date.from(LocalDate.now().plusYears(10).atStartOfDay(ZoneId.systemDefault()).toInstant()));
                List<FileIdentityDto> fileIdentities = picList.stream().map(e -> {
                    FileIdentityDto dto = new FileIdentityDto();
                    dto.setFileUuid(e);
                    return dto;
                }).collect(Collectors.toList());
                Map<FileIdentityDto, String> downloadDtoMap = fileCenterService.acquireFileDownloadUrls(fileIdentities, options);
                if (CollUtil.isNotEmpty(downloadDtoMap)) {
                    downloadDtoMap.forEach((k, v) -> downloadMap.put(k.getFileUuid(), v));
                }
            }
            if (CollUtil.isNotEmpty(downloadMap) && CollUtil.isNotEmpty(recordPicList)) {
                recordPicList.forEach(data -> {
                    if (StrUtil.isNotBlank(downloadMap.get(data.getFileId()))) {
                        if (weighDataIdPicMap.containsKey(data.getRecordId())) {
                            String pic = weighDataIdPicMap.get(data.getRecordId());
                            weighDataIdPicMap.put(data.getRecordId(), pic + "," + downloadMap.get(data.getFileId()));
                        } else {
                            weighDataIdPicMap.put(data.getRecordId(), downloadMap.get(data.getFileId()));
                        }
                    }
                });
            }

            // 订单明细map
            List<String> deliveryDetailIdList = list.stream().filter(e -> StrUtil.isNotBlank(e.getDeliveryDetailIds())).flatMap(e -> StrUtil.split(e.getDeliveryDetailIds(), ",").stream()).distinct().collect(Collectors.toList());
            if (CollUtil.isNotEmpty(deliveryDetailIdList)) {
                List<PushConfirmMaterialDTO> detailDOList = deliveryDetailMapper.selectInfoForConfirmPush(deliveryDetailIdList);
                if (CollUtil.isNotEmpty(detailDOList)) {
                    deliveryDetailDOMap = detailDOList.stream().collect(Collectors.toMap(PushConfirmMaterialDTO::getId, e -> e));
                }
            }

            Map<String, PushConfirmMaterialDTO> finalDeliveryDetailDOMap = deliveryDetailDOMap;
            Map<String, WeighDataDO> finalDataMap = dataMap;
            List<WeighConfirmPullVO> array = list.stream().map(e -> {
                WeighConfirmPullVO vo = new WeighConfirmPullVO();

                BeanUtils.copyProperties(e, vo);
                if (CollUtil.isNotEmpty(downloadMap)) {
                    if (StrUtil.isNotBlank(e.getSignerPic())) {
                        vo.setSignerPic(downloadMap.get(e.getSignerPic()));
                    }
                    if (StrUtil.isNotBlank(e.getSignPic())) {
                        vo.setSignPic(downloadMap.get(e.getSignPic()));
                    }
                    if (StrUtil.isNotBlank(e.getDocumentPic())) {
                        vo.setDocumentPic(StrUtil.split(e.getDocumentPic(), ",").stream().map(downloadMap::get).collect(Collectors.joining(",")));
                    }
                }
                if (StrUtil.isNotBlank(e.getDeliveryDetailIds())) {
                    List<PushConfirmMaterialDTO> collect = StrUtil.split(e.getDeliveryDetailIds(), ",").stream().map(finalDeliveryDetailDOMap::get).collect(Collectors.toList());
                    vo.setDeliveryDetails(collect);
                }
                if (CollUtil.isNotEmpty(finalDataMap) && CollUtil.isNotEmpty(weighDataIdPicMap)) {
                    WeighDataDO weighDataDO = finalDataMap.get(e.getRecordId1());
                    WeighDataDO weighDataDO1 = finalDataMap.get(e.getRecordId2());
                    if (ObjectUtil.isNotNull(weighDataDO1) && ObjectUtil.isNotNull(weighDataDO) && ObjectUtil.isNotNull(weighDataDO1.getWeighTime()) && ObjectUtil.isNotNull(weighDataDO.getWeighTime())) {
                        if (weighDataDO1.getWeighTime().isBefore(weighDataDO.getWeighTime())) {
                            vo.setEnterPic(weighDataIdPicMap.get(weighDataDO1.getRecordId()));
                            vo.setLeavePic(weighDataIdPicMap.get(weighDataDO.getRecordId()));
                        }else {
                            vo.setEnterPic(weighDataIdPicMap.get(weighDataDO.getRecordId()));
                            vo.setLeavePic(weighDataIdPicMap.get(weighDataDO1.getRecordId()));
                        }
                    }
                }

                return vo;
            }).collect(Collectors.toList());

            BeanUtils.copyProperties(page,result);
            result.setRecords(array);
            return result;
        }

        return result;
    }

    private QueryWrapper<WeighDataDO> getWeighDataDOQueryWrapper(String uid, List<String> ids, LocalDateTime startTime, LocalDateTime endTime, String deviceSn, String attributionCode, List<String> truckNos) {
        QueryWrapper<WeighDataDO> wrapper = new QueryWrapper<>();
        // 租户
        wrapper.lambda().eq(WeighDataDO::getUid, uid);
        // 称重记录ID
        if (CollectionUtil.isNotEmpty(ids)) {
            wrapper.lambda().in(WeighDataDO::getRecordId, ids);
        } else {
            // 时间范围
            wrapper.lambda().between(WeighDataDO::getWeighTime, startTime, endTime);
        }
        // 设备机器码
        if (StringUtils.isNotBlank(deviceSn)) {
            wrapper.lambda().eq(WeighDataDO::getDeviceSn, deviceSn);
        }
        // 设备归属方code
        if (StringUtils.isNotBlank(attributionCode)) {
            Long deviceAttributionId = deviceAttributionExtMapper.getDeviceAttributionIdByCode(uid, attributionCode);
            wrapper.lambda().eq(WeighDataDO::getAttributionId, deviceAttributionId);
        }
        // 车牌号
        if (CollectionUtil.isNotEmpty(truckNos)) {
            wrapper.lambda().in(WeighDataDO::getTruckNo, truckNos);
        }
        wrapper.lambda().orderByDesc(WeighDataDO::getWeighTime);
        return wrapper;
    }
}
