package cn.pinming.microservice.material.client.management.service.biz;

import cn.pinming.microservice.material.client.management.common.form.DeveloperDocumentForm;
import cn.pinming.microservice.material.client.management.common.form.DocumentForm;
import cn.pinming.microservice.material.client.management.common.vo.DocumentVO;

import java.util.Date;
import java.util.List;

public interface DocumentService {

    void developerDocumentCreate(DocumentForm documentForm);

    List<DocumentVO> developerDocumentList();

    String developerDocumentContent(Long id);

    String developerDocumentDownload(String uuid, Date expireDate);

    void developerDocumentDelete(Long id);

    void resourceDocumentUpload(DocumentForm documentForm);

    List<DocumentVO> resourceDocumentList();

    String resourceDocumentPreview(String uuid);

    String resourceDocumentDownload(String uuid);

    void resourceDocumentDelete(String uuid);

    void developerDocumentSave(DeveloperDocumentForm documentForm);
}
