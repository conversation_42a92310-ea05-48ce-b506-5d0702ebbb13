package cn.pinming.microservice.material.client.management.common.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
public class OCRDictForm {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "字典名")
    @NotBlank(message = "字典名为空")
    private String name;

    @ApiModelProperty(value = "字典值")
    @NotBlank(message = "字典值为空")
    private String value;

}
