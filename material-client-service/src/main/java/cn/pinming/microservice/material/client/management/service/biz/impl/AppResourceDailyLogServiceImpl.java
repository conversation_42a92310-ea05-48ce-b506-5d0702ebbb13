package cn.pinming.microservice.material.client.management.service.biz.impl;


import cn.pinming.microservice.material.client.management.common.mapper.AppResourceDailyLogMapper;
import cn.pinming.microservice.material.client.management.common.model.AppResourceDailyLogDO;
import cn.pinming.microservice.material.client.management.service.biz.AppResourceDailyLogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2023/8/8 15:40
 */
@Slf4j
@Service
public class AppResourceDailyLogServiceImpl extends ServiceImpl<AppResourceDailyLogMapper, AppResourceDailyLogDO> implements AppResourceDailyLogService {
}
