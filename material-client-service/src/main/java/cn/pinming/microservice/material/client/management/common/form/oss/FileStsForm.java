package cn.pinming.microservice.material.client.management.common.form.oss;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2023/6/16 14:50
 */
@Data
public class FileStsForm {

    @NotBlank(message = "缺少文件md5")
    private String fileMd5;
    /**
     * 文件mime类型
     */
    @NotBlank(message = "缺少文件Mime")
    private String fileMime;
    /**
     * 文件名称
     */
    @NotBlank(message = "缺少文件名称")
    private String fileName;
    /**
     * 文件大小单位（Byte）
     */
    @NotNull(message = "缺少文件大小")
    private Long fileSize;
    /**
     * 文件类型
     * 1-图片 2-音频 3-视频 4-其他
     */
    @NotNull(message = "缺少文件类型")
    private Byte fileType = 1;

}
