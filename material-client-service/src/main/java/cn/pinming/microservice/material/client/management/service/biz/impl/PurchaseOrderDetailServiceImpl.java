package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.pinming.microservice.material.client.management.common.mapper.PurchaseOrderDetailMapper;
import cn.pinming.microservice.material.client.management.common.model.PurchaseOrderDetailDO;
import cn.pinming.microservice.material.client.management.service.biz.IPurchaseOrderDetailService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 采购单明细表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-21
 */
@Service
public class PurchaseOrderDetailServiceImpl extends ServiceImpl<PurchaseOrderDetailMapper, PurchaseOrderDetailDO> implements IPurchaseOrderDetailService {

}
