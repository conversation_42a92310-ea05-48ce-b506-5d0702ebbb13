package cn.pinming.microservice.material.client.management.infrastructure.util;

public class Base62Util {
    private static final String BASE62 = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";

    // 十进制转62进制
    public static String toBase62(long number) {
        if (number == 0) {
            return "0";
        }

        StringBuilder sb = new StringBuilder();
        while (number > 0) {
            int remainder = (int) (number % 62);
            sb.append(BASE62.charAt(remainder));
            number /= 62;
        }

        // 翻转字符串获取正确的结果
        return sb.reverse().toString();
    }

    public static long decode(String shortLink) {
        long result = 0;
        for (char c : shortLink.toCharArray()) {
            result = result * 62 + BASE62.indexOf(c);
        }
        return result;
    }
}
