package cn.pinming.microservice.material.client.management.infrastructure.interceptor;

import cn.hutool.core.util.ObjectUtil;
import cn.pinming.material.v2.common.enums.SignatureMethod;
import cn.pinming.material.v2.exception.MaterialException;
import cn.pinming.material.v2.exception.MaterialExceptionMessage;
import cn.pinming.material.v2.signature.Signature;
import cn.pinming.material.v2.signature.SignatureConstant;
import cn.pinming.material.v2.signature.SignatureFactory;
import cn.pinming.microservice.material.client.management.common.dto.DeveloperDTO;
import cn.pinming.microservice.material.client.management.common.mapper.DeveloperMapper;
import cn.pinming.springboot.starter.redis.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2023/6/14
 */
@Slf4j
@Component
public class OpenapiInterceptor implements HandlerInterceptor {

    private static final int ONE_MIN_SECONDS = 60;
    private static final String REQUEST_ID_CACHE_PREFIX = "request_id_";
    private static final Pattern TIMESTAMP_PATTERN = Pattern.compile("^\\d{13}$");
    private static final Pattern REQUEST_ID_PATTERN = Pattern.compile("^[a-z0-9]{32}$");

    @Resource
    private DeveloperMapper developerExtMapper;

    @Resource
    private RedisUtil redisUtil;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 签名认证
        signatureAuthentication(request);
        return HandlerInterceptor.super.preHandle(request, response, handler);
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        String requestIdHeader = request.getHeader(SignatureConstant.REQUEST_ID_HEADER_NAME);
        redisUtil.set(REQUEST_ID_CACHE_PREFIX + requestIdHeader, requestIdHeader, ONE_MIN_SECONDS);
        HandlerInterceptor.super.postHandle(request, response, handler, modelAndView);
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        HandlerInterceptor.super.afterCompletion(request, response, handler, ex);
    }

    /**
     * 签名认证
     *
     * @param request
     */
    private void signatureAuthentication(HttpServletRequest request) throws MaterialException {
        String url = request.getRequestURL().toString();
        String method = request.getMethod();
        String requestURI = request.getRequestURI();

        String[] urlArr = url.split("//");
        String endpointHeader = urlArr[0] + "//" + urlArr[1].replaceFirst("/.*", "");
        String appKeyHeader = request.getHeader(SignatureConstant.APP_KEY_PARAM_HEADER_NAME);
        String signatureMethodHeader = request.getHeader(SignatureConstant.SIGNATURE_METHOD_HEADER_NAME);
        String timestampHeader = request.getHeader(SignatureConstant.TIMESTAMP_HEADER_NAME);
        String requestIdHeader = request.getHeader(SignatureConstant.REQUEST_ID_HEADER_NAME);
        String signatureHeader = request.getHeader(SignatureConstant.SIGNATURE_HEADER_NAME);
        Signature signature = new Signature();
        signature.setEndpoint(endpointHeader);
        signature.setHttpMethod(method);
        signature.setRequestUri(requestURI);
        signature.setAppKey(appKeyHeader);
        signature.setTimestamp(timestampHeader);
        signature.setRequestId(requestIdHeader);
        signature.setSignature(signatureHeader);

        if (StringUtils.isNotBlank(signatureMethodHeader)) {
            signature.setSignatureMethod(signatureMethodHeader);
        }

        // 校验
        this.validate(signature);
        // 校验通过
//        log.info("校验通过");

        // 防重放攻击拦截
        Object requestIdCache = redisUtil.get(REQUEST_ID_CACHE_PREFIX + requestIdHeader);
        if (ObjectUtil.isNotNull(requestIdCache)) {
            throw new MaterialException(MaterialExceptionMessage.INVALID_REQUEST_ID);
        }

        // 请求1min内有效性验证
        float seconds = (float) (System.currentTimeMillis() - Long.parseLong(timestampHeader)) / 1000;
        if (seconds > ONE_MIN_SECONDS) {
            throw new MaterialException(MaterialExceptionMessage.INVALID_TIMESTAMP);
        }

        // 签名
        String appKey = signature.getAppKey();
        String signatureString = signature.getSignature();
        DeveloperDTO developerDO = developerExtMapper.selectOneByAppKeyService(appKey);
        if (ObjectUtil.isNull(developerDO)) {
            throw new MaterialException(MaterialExceptionMessage.INVALID_APP_KEY);
        }
        String signatureHttpProduce = SignatureFactory.signatureHttpProduce(signature, developerDO.getAppSecretKey());
        if (!signatureString.equals(signatureHttpProduce)) {
            throw new MaterialException(MaterialExceptionMessage.INVALID_SIGNATURE);
        }
//        log.info("签名通过");
    }

    private void validate(Signature signature) throws MaterialException {
        String appKey = signature.getAppKey();
        if (StringUtils.isBlank(appKey)) {
            throwInvalidSignatureParamValueError(SignatureConstant.APP_KEY_PARAM_HEADER_NAME);
        }
        String timestamp = signature.getTimestamp();
        if (StringUtils.isBlank(timestamp) || !TIMESTAMP_PATTERN.matcher(timestamp).matches()) {
            throwInvalidSignatureParamValueError(SignatureConstant.TIMESTAMP_HEADER_NAME);
        }
        String requestId = signature.getRequestId();
        if (StringUtils.isBlank(requestId) || !REQUEST_ID_PATTERN.matcher(requestId).matches()) {
            throwInvalidSignatureParamValueError(SignatureConstant.REQUEST_ID_HEADER_NAME);
        }
        String signatureMethod = signature.getSignatureMethod();
        if (!SignatureMethod.check(signatureMethod)) {
            throwInvalidSignatureParamValueError(SignatureConstant.SIGNATURE_METHOD_HEADER_NAME);
        }
        String signatureString = signature.getSignature();
        if (StringUtils.isBlank(signatureString)) {
            throwInvalidSignatureParamValueError(SignatureConstant.SIGNATURE_HEADER_NAME);
        }
    }

    private void throwInvalidSignatureParamValueError(String paramName) {
        throw new MaterialException(MaterialExceptionMessage.INVALID_SIGNATURE_PARAM_VALUE, paramName);
    }

}
