package cn.pinming.microservice.material.client.management.service.biz;

import cn.pinming.microservice.material.client.management.common.form.RebarCheckDetailForm;
import cn.pinming.microservice.material.client.management.common.form.RebarCheckForm;
import cn.pinming.microservice.material.client.management.common.model.CheckDO;
import cn.pinming.microservice.material.client.management.common.query.CheckQuery;
import cn.pinming.microservice.material.client.management.common.vo.CheckDetailVO;
import cn.pinming.microservice.material.client.management.common.vo.CheckVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface CheckService extends IService<CheckDO> {
    Long rebarCheckAdd(RebarCheckForm form);

    void rebarCheckFirst(Long id);

    void rebarCheckSecond(Long id);

    void complexUpdate(RebarCheckForm form);

    void chooseConfirm(Long id,List<RebarCheckDetailForm> list);

    void truckOrPicUpdate(RebarCheckForm form);

    IPage<CheckVO> checkList(CheckQuery query);

    CheckDetailVO checkDetail(Long id);
}
