package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.hutool.core.util.ObjUtil;
import cn.pinming.microservice.material.client.management.common.form.weighCurveConfigForm;
import cn.pinming.microservice.material.client.management.common.mapper.WeighCurveConfigMapper;
import cn.pinming.microservice.material.client.management.common.model.WeighCurveConfigDO;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizErrorException;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizExceptionMessageEnum;
import cn.pinming.microservice.material.client.management.infrastructure.util.UserIdUtil;
import cn.pinming.microservice.material.client.management.service.biz.IWeighCurveConfigService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 原始记录称重曲线配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@Service
public class WeighCurveConfigServiceImpl extends ServiceImpl<WeighCurveConfigMapper, WeighCurveConfigDO> implements IWeighCurveConfigService {
    @Resource
    private UserIdUtil userIdUtil;

    @Override
    public void config(weighCurveConfigForm form) {
        String uId = userIdUtil.getUId();

        if (ObjUtil.isNull(form.getId())) {
            WeighCurveConfigDO one = this.lambdaQuery()
                    .eq(WeighCurveConfigDO::getAttributionId, form.getAttributionId())
                    .one();
            if (ObjUtil.isNotNull(one)) {
                throw new BizErrorException(BizExceptionMessageEnum.CURVE_CONFIG_EXIST);
            }
        }

        WeighCurveConfigDO weighCurveConfigDO = new WeighCurveConfigDO();
        BeanUtils.copyProperties(form, weighCurveConfigDO);
        weighCurveConfigDO.setUid(uId);
        this.saveOrUpdate(weighCurveConfigDO);
    }

}
