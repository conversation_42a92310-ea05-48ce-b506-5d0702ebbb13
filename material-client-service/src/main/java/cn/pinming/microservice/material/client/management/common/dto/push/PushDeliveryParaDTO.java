package cn.pinming.microservice.material.client.management.common.dto.push;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class PushDeliveryParaDTO {
    @ApiModelProperty(value = "扣杂率")
    private BigDecimal deductRatio;

    @ApiModelProperty(value = "换算系数 1立方米 = xxx吨")
    private BigDecimal scaleFactor;

    @ApiModelProperty(value = "重量单位 0 千克, 1 吨")
    private BigDecimal weightUnitForReference;
}
