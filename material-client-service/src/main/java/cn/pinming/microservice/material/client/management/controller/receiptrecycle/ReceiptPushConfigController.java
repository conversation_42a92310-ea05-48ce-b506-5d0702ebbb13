package cn.pinming.microservice.material.client.management.controller.receiptrecycle;

import cn.pinming.microservice.material.client.management.infrastructure.response.SingleResponse;
import cn.pinming.microservice.material.client.management.infrastructure.util.UserIdUtil;
import cn.pinming.microservice.material.client.management.common.form.ReceiptPushConfigForm;
import cn.pinming.microservice.material.client.management.common.model.ReceiptPushConfigDO;
import cn.pinming.microservice.material.client.management.service.biz.ReceiptPushConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Api(value = "单据回收推送配置", tags = {"receiptPushConfig"})
@RestController
@RequestMapping("/api/receiptPushConfig")
public class ReceiptPushConfigController {

    @Resource
    private ReceiptPushConfigService receiptPushConfigService;
    @Resource
    private UserIdUtil userIdUtil;

    @ApiOperation(value = "推送订阅配置", responseReference = "SingleResponse«?»", nickname = "saveReceiptPushConfig")
    @PostMapping("/save")
    public SingleResponse<?> saveReceiptPushConfig(@RequestBody ReceiptPushConfigForm form) {
        receiptPushConfigService.saveReceiptPushConfig(form);
        return SingleResponse.of(SingleResponse.buildSuccess());
    }

    @ApiOperation(value = "推送订阅配置展示", responseReference = "SingleResponse«ReceiptPushConfigDO»", nickname = "showReceiptPushConfig")
    @GetMapping("/show")
    public SingleResponse<ReceiptPushConfigDO> showReceiptPushConfig() {
        return SingleResponse.of(receiptPushConfigService.getByUid(userIdUtil.getUId()));
    }

}
