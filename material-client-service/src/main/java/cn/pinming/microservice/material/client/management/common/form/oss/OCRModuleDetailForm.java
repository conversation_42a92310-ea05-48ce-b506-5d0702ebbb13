package cn.pinming.microservice.material.client.management.common.form.oss;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class OCRModuleDetailForm {
    @ApiModelProperty(value = "数据键id")
    private Long id;

    @ApiModelProperty(value = "数据键父id")
    private Long pid;

    @ApiModelProperty(value = "数据组id")
    private String groupId;

    @ApiModelProperty(value = "数据组名称")
    private String groupName;

    @ApiModelProperty(value = "数据键名称")
    private String keyName;

    @ApiModelProperty(value = "key值关联")
    private String valueName;

    @ApiModelProperty(value = "索引值")
    private String dataIndex;

    @ApiModelProperty(value = "坐标(左上，右上，右下，左下)")
    private String coordinate;

    @ApiModelProperty(value = "锚点类型 1 主锚 2 副锚 3 业务锚点")
    private Byte type;

    @ApiModelProperty(value = "剔除内容")
    private String deletedContent;

    @ApiModelProperty(value = "模板键值类型（1-字符串 2-数字 3-日期）")
    private Byte keyType;
}
