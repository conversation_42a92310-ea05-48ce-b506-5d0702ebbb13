package cn.pinming.microservice.material.client.management.common.enums;

public enum LoginTypeEnum {
    LOGIN((byte) 1, "login"),
    MANAGER((byte) 2, "manager");

    private byte type;
    private String str;

    LoginTypeEnum(byte type, String str) {
        this.type = type;
        this.str = str;
    }

    public byte getType() {
        return type;
    }

    public String getStr() {
        return str;
    }
}
