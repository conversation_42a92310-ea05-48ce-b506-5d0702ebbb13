package cn.pinming.microservice.material.client.management.common.dto.push;

import cn.pinming.microservice.material.client.management.common.model.PurchaseOrderDO;
import cn.pinming.microservice.material.client.management.common.dto.push.PurchaseDetailSyncDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class PurchaseSyncDTO extends PurchaseOrderDO {
    @ApiModelProperty(value = "订单源头基石归属方主code")
    private String primaryCode;

    @ApiModelProperty(value = "订单源头基石租户名称")
    private String userName;

    @ApiModelProperty(value = "订单明细列表")
    private List<PurchaseDetailSyncDTO> list;
}
