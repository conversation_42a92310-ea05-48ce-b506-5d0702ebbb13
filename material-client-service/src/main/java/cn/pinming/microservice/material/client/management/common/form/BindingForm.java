package cn.pinming.microservice.material.client.management.common.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import java.math.BigDecimal;

@Data
public class BindingForm {
    @ApiModelProperty("绑定id")
    private Long id;

    @ApiModelProperty("设备id")
    private Long deviceId;

    @ApiModelProperty("设备名称")
    private String deviceName;

    @ApiModelProperty("归属方id")
    private Long attributionId;

    @ApiModelProperty("密码")
    private String password;

    @ApiModelProperty("外部辅助码")
    private String auxiliaryCode;

    @ApiModelProperty(value = "司机自助确认设备模式 1 运单模式  2 单据回收模式 3 双模式（1,2） 4 仅扫码模式")
    private Integer selfCheckMode;

    @ApiModelProperty(value = "组装超时时间 单位 分")
    @Min(value = 0, message = "超时时间不能小于0")
    private Integer timeout;

    @ApiModelProperty(value = "最短时长 单位 分")
    @Min(value = 0, message = "最短时长不能小于0")
    private Integer sameTruckMinDuration;

    @ApiModelProperty(value = "是否开启司机签名  0 关闭 1 开启")
    private Integer signature;

    @ApiModelProperty(value = "是否跳过扫码 0 否 1 是")
    private Integer skipScanCode;

    @ApiModelProperty("是否自动称重 0 关闭 1 开启")
    private Integer autoWeight;

    @ApiModelProperty("消息机器人id 逗号分割")
    private String msgRobot;

    @ApiModelProperty("消息接收人手机号 逗号分割")
    private String msgReceiver;

    /**
     * 平台期时长 秒
     */
    @Min(value = 1, message = "平台期时长不能小于1")
    @ApiModelProperty(value = "平台期时长 秒")
    private Integer platformDuration;

    /**
     * 平台期个数
     */
    @Min(value = 1, message = "平台期个数不能小于1")
    @ApiModelProperty(value = "平台期个数")
    private Integer platformCount;

    /**
     * 持续时长 秒
     */
    @Min(value = 1, message = "持续时长不能小于1")
    @ApiModelProperty(value = "持续时长 秒")
    private Integer sustainDuration;

    /**
     * 称重最大值 kg
     */
    @ApiModelProperty(value = "称重最大值 kg")
    private BigDecimal weight;

    /**
     * 防控仪监控周期 秒
     */
    @Min(value = 1, message = "防控仪监控周期不能小于1")
    @ApiModelProperty(value = "防控仪监控周期 秒")
    private Integer alarmTime;

    @ApiModelProperty(value = "告警项")
    private String alarmItem;

    @ApiModelProperty(value = "本地ip")
    private String localIp;

}
