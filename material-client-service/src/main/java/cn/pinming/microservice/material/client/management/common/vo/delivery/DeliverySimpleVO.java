package cn.pinming.microservice.material.client.management.common.vo.delivery;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2024/8/22 17:18
 */
@Data
public class DeliverySimpleVO {

    @ApiModelProperty("类型 1 - 运单模式的发货单(运单);2 - OCR单据回收预生成单据; 3 - 仅毛皮重运单;")
    private Integer type;

    @ApiModelProperty("车牌号")
    private String truckNo;

    @ApiModelProperty("发货单号")
    private String no;

    @ApiModelProperty("1 在途，2 到场确认中，3 已到场，4 已作废  5 到场确认中-待闭合")
    private Integer status;

    @ApiModelProperty("重量")
    private BigDecimal weight;

    @ApiModelProperty("称重单位")
    private String unit;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("本地数据id")
    private String localId;
}
