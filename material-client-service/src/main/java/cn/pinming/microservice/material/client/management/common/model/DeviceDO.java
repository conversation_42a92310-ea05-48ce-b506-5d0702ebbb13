package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * <p>
 * 设备管理-设备表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("s_device")
public class DeviceDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 设备机器码
     */
    private String deviceSn;

    /**
     * 0 自有 2租赁 3购买
     */
    private Byte source;

    /**
     * 设备名称
     */
    private String name;

    /**
     * 设备类型(称重一体机-WEIGH、单据回收终端机-RECEIPT_RECYCLE 、司机自助确认设备-SELF_CHECK、本地设备-LOCAL)
     */
    private String deviceType;

    /**
     * 1 启用 2 禁用
     */
    private Byte isUsed;

    /**
     * 授权到期日期
     */
    private LocalDate expireDate;

    /**
     * 备注
     */
    private String remark;
}
