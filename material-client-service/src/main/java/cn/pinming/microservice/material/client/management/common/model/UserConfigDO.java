package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 用户配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("s_user_config")
public class UserConfigDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 用户id
     */
    private String uid;

    /**
     * 购买空间， 单位GB
     */
    private BigDecimal spaceSize;

    /**
     * 使用空间， 单位GB
     */
    private BigDecimal spaceUseSize;

    /**
     * 购买空间到期时间
     */
    private LocalDateTime spaceExpire;

    /**
     * 称重数据服务调用总次数
     */
    private Long apiTotal;

    /**
     * 称重数据服务已调用次数
     */
    private Long apiUseTotal;

    /**
     * 单据匹配服务调用总次数
     */
    private Long receiptApiTotal;

    /**
     * 单据匹配服务服务已调用次数
     */
    private Long receiptApiUseTotal;

    /**
     * 数据归属方数量上限 
     */
    private Integer attributionAmount;

    /**
     * 是否无上限 1 否 2 是
     */
    private Byte isLimit;
}
