package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.pinming.microservice.material.client.management.common.mapper.RebarMaterialMapper;
import cn.pinming.microservice.material.client.management.common.mapper.ext.RebarMaterialExtMapper;
import cn.pinming.microservice.material.client.management.common.model.RebarMaterialDO;
import cn.pinming.microservice.material.client.management.service.biz.RebarMaterialService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class RebarMaterialServiceImpl extends ServiceImpl<RebarMaterialMapper, RebarMaterialDO> implements RebarMaterialService {
    @Resource
    private RebarMaterialExtMapper rebarMaterialExtMapper;

    @Override
    public List<RebarMaterialDO> rebarMaterialList(RebarMaterialDO query) {
        return rebarMaterialExtMapper.rebarMaterialList(query);
    }
}
