package cn.pinming.microservice.material.client.management.infrastructure.constant;

import com.alibaba.fastjson.JSONObject;

public class AuthorizationFileConstant {
    public static final String SECRET_KEY = "nhVyoBCtadvtJRfZ";
    private static final String ID = "id";
    private static final String TENANT_ACCOUNT = "tenant_account";
    private static final String TENANT_NAME = "tenant_name";
    private static final String ATTRIBUTION_NAME = "attribution_name";
    private static final String ATTRIBUTION_CODE = "attribution_code";
    private static final String APP_KEY = "app_key";
    private static final String APP_SECRET_KEY = "app_secret_key";

    public static String buildAuthorizationFileContent(Long id, String tenantAccount, String tenantName, String attributionName,
                                                       String attributionCode, String appKey, String appSecretKey) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(ID, id);
        jsonObject.put(TENANT_ACCOUNT, tenantAccount);
        jsonObject.put(TENANT_NAME, tenantName);
        jsonObject.put(ATTRIBUTION_NAME, attributionName);
        jsonObject.put(ATTRIBUTION_CODE, attributionCode);
        jsonObject.put(APP_KEY, appKey);
        jsonObject.put(APP_SECRET_KEY, appSecretKey);
        return jsonObject.toJSONString();
    }

}
