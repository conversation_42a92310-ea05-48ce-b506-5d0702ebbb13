package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 资源管理
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("s_resource_document")
public class ResourceDocumentDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 文件类型(PDF，PACKAGE)
     */
    private String fileType;

    /**
     * 文件uuid
     */
    private String fileUuid;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件说明
     */
    private String fileDesc;


}
