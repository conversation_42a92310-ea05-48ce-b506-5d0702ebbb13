package cn.pinming.microservice.material.client.management.common.mapper;

import cn.pinming.microservice.material.client.management.common.model.DeliveryDO;
import cn.pinming.microservice.material.client.management.common.model.PurchaseOrderDO;
import cn.pinming.microservice.material.client.management.common.query.DeliveryPageQuery;
import cn.pinming.microservice.material.client.management.common.vo.delivery.DeliveryItemDTO;
import cn.pinming.microservice.material.client.management.common.vo.delivery.DeliveryTruckVO;
import cn.pinming.microservice.material.client.management.common.vo.delivery.DeliveryVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * <p>
 * 发货单(运单) Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-22
 */
public interface DeliveryMapper extends BaseMapper<DeliveryDO> {

    List<DeliveryItemDTO> selectListByPurchaseOrderId(Long purchaseOrderId);

    List<DeliveryItemDTO> selectListByDeliveryId(Long deliveryId);

    List<DeliveryTruckVO> selectTruckList(String uid);

    Page<DeliveryVO> pageByQuery(DeliveryPageQuery query);

    PurchaseOrderDO getPurchaseOrder(Long purchaseOrderId, String uid);

}
