package cn.pinming.microservice.material.client.management.infrastructure.annotation;

import cn.pinming.microservice.material.client.management.common.enums.RoleEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface RoleAuthority {

    RoleEnum value();

}
