package cn.pinming.microservice.material.client.management.common.vo.print;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class PrintTemplateVO {

    @ApiModelProperty(value = "模板ID")
    private Long id;

    @ApiModelProperty(value = "模板名")
    private String name;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime gmtModify;

    @ApiModelProperty(value = "关联归属方数")
    private Integer amount = 0;

    @ApiModelProperty(value = "归属方ID")
    private String attributionId;

    @ApiModelProperty("状态 0 启用 1 禁用")
    private Byte enabled;

    @ApiModelProperty("是否系统模板")
    private boolean isSystem;

}
