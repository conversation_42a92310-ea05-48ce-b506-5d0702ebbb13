package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 基石客户端维护表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("s_client")
public class ClientDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 文件id
     */
    private String fileId;

    /**
     * 版本号
     */
    private String version;

    /**
     * 类型  1 客户端 2 工具 3 文档
     */
    private Byte type;

    /**
     * 描述
     */
    private String remark;


}
