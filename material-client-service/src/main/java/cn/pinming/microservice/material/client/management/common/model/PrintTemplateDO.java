package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 打印模板设置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("s_print_template")
public class PrintTemplateDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 模板名称
     */
    private String name;

    /**
     * 模板内容
     */
    private String content;

    /**
     * 打印业务类型  0 司机确认单 1 发货单
     */
    private Byte type;

    /**
     * 小票类型  进场小票 离场小票 完整确认单小票
     */
    private Byte formType;

    /**
     * 打印小票样式  小盒子打印样式 一体机打印样式
     */
    private Byte style;

    @TableField(exist = false)
    private Integer printLimit;

}
