package cn.pinming.microservice.material.client.management.common.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class CheckConfirmVO implements Serializable {
    private static final long serialVersionUID = 6362567621185280483L;

    @ApiModelProperty(value = "验收类型 1 仅称重验收, 2 称重+实点根数验收")
    private Integer checkType;

    @ApiModelProperty(value = "送货单重量")
    private BigDecimal sendWeight;

    @ApiModelProperty(value = "差异")
    private BigDecimal dif;

    @ApiModelProperty(value = "偏差率")
    private BigDecimal rate;

    @ApiModelProperty(value = "混装实称重量")
    private BigDecimal actualWeight;

    @ApiModelProperty(value = " 1 实称总重, 2 面单总重")
    private Integer reverseWeightType;

    @ApiModelProperty(value = "混装实称重量-是否为手动输入 1 是 2 否")
    private Byte isInput;

    @ApiModelProperty(value = "钢筋类型 1 直螺纹,2 盘螺")
    private Integer type;

    @ApiModelProperty(value = "材料确认列表")
    private List<CheckMaterialConfirmVO> list;
}
