package cn.pinming.microservice.material.client.management.common.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CurveForm {
    @ApiModelProperty(value = "设备sn")
    private String deviceSn;

    @ApiModelProperty(value = "称重曲线本地id")
    private String weighCurveId;

    @ApiModelProperty(value = "称重曲线数据")
    private String weighCurveData;

    @ApiModelProperty(value = "称重单位")
    private String weighUnit;

    @ApiModelProperty(value = "本地称重记录id")
    private String weighDataId;

    @ApiModelProperty(value = "称重曲线本地创建时间")
    private String createTime;
}
