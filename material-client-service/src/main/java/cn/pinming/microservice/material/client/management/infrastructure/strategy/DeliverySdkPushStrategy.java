package cn.pinming.microservice.material.client.management.infrastructure.strategy;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.pinming.microservice.material.client.management.common.enums.SdkPushTypeEnum;
import cn.pinming.microservice.material.client.management.common.BaseDO;
import cn.pinming.microservice.material.client.management.common.mapper.DeliveryDetailMapper;
import cn.pinming.microservice.material.client.management.common.model.DeliveryDO;
import cn.pinming.microservice.material.client.management.common.model.PurchaseOrderDO;
import cn.pinming.microservice.material.client.management.common.model.SupplierConfigDO;
import cn.pinming.microservice.material.client.management.common.vo.push.PushRouteConfigVO;
import cn.pinming.microservice.material.client.management.common.vo.selfcheck.CargoVO;
import cn.pinming.microservice.material.client.management.common.vo.selfcheck.PurchaseBaseInfoVO;
import cn.pinming.microservice.material.client.management.common.vo.selfcheck.SdkPushDeliveryVO;
import cn.pinming.microservice.material.client.management.service.biz.DeviceAttributionService;
import cn.pinming.microservice.material.client.management.service.biz.IDeliveryService;
import cn.pinming.microservice.material.client.management.service.biz.IPurchaseOrderService;
import cn.pinming.microservice.material.client.management.service.biz.SupplierConfigService;
import cn.pinming.microservice.material.client.management.service.push.sdk.dto.SdkRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2024/9/9 17:19
 */
@Slf4j
@Component("DELIVERY")
public class DeliverySdkPushStrategy extends AbstractSkdPushStrategy<SdkPushDeliveryVO> {

    @Resource
    private IPurchaseOrderService purchaseOrderService;
    @Resource
    private IDeliveryService deliveryService;
    @Resource
    private DeliveryDetailMapper deliveryDetailMapper;
    @Resource
    private SupplierConfigService supplierConfigService;
    @Resource
    private DeviceAttributionService deviceAttributionService;

    @Override
    Long getRouteConfigId() {
        return SdkPushTypeEnum.DELIVERY.getVal();
    }

    @Override
    Map<String, List<SdkPushDeliveryVO>> queryDataListMap(PushRouteConfigVO config, Map<Long, String> attributionCodeMap) {
        Map<String, List<SdkPushDeliveryVO>> result = new HashMap<>();
        //查询归属方
        String uid = config.getUid();
        Set<Long> attributionIdList = attributionCodeMap.keySet();

        // 查询开启推送的供应商
        List<String> supplierList = getSupplierList(uid, true, false);
        // 查询订单
        List<PurchaseOrderDO> purchaseOrderList = purchaseOrderService.lambdaQuery().eq(PurchaseOrderDO::getCreateId, uid)
                .in(CollUtil.isNotEmpty(attributionIdList), PurchaseOrderDO::getAttributionId, attributionIdList)
                .in(CollUtil.isNotEmpty(supplierList), PurchaseOrderDO::getSupplierId, supplierList)
                .select(PurchaseOrderDO::getId, PurchaseOrderDO::getAttributionId)
                .list();
        if (CollUtil.isNotEmpty(purchaseOrderList)) {
            Map<Long, List<PurchaseOrderDO>> attributionMap = purchaseOrderList.stream().collect(Collectors.groupingBy(PurchaseOrderDO::getAttributionId));
            attributionMap.forEach((k, v) -> {
                if (attributionCodeMap.containsKey(k)) {
                    List<Long> purchaseIdList = v.stream().map(BaseDO::getId).collect(Collectors.toList());
                    List<DeliveryDO> deliveryList = deliveryService.lambdaQuery().in(DeliveryDO::getPurchaseOrderId, purchaseIdList)
                            .eq(DeliveryDO::getPushState, 1).ne(DeliveryDO::getStatus, 4).list();
                    if (CollUtil.isNotEmpty(deliveryList)) {
                        // 查询运单明细
                        List<Long> deliveryIdList = deliveryList.stream().map(BaseDO::getId).collect(Collectors.toList());
                        List<CargoVO> cargoList = deliveryDetailMapper.selectCargoListByDeliveryId(deliveryIdList);

                        Map<Long, List<CargoVO>> cargoMap = new HashMap<>();
                        if (CollUtil.isNotEmpty(cargoList)) {
                            cargoMap = cargoList.stream().collect(Collectors.groupingBy(CargoVO::getDeliveryId));
                        }

                        // 组装推送数据 一次最多推送50条
                        Map<Long, List<CargoVO>> finalCargoMap = cargoMap;
                        List<SdkPushDeliveryVO> dataList = deliveryList.stream().map(obj -> {
                            SdkPushDeliveryVO vo = new SdkPushDeliveryVO();
                            vo.setJsDeliveryNo(obj.getNo());
                            vo.setJsDeliveryId(obj.getId());
                            vo.setTruckNo(obj.getTruckNo());
                            vo.setDriverName(obj.getDriver());
                            vo.setDriverMobile(obj.getDriverMobile());
                            vo.setDeliveryTime(LocalDateTimeUtil.format(obj.getGmtCreate(), DatePattern.NORM_DATETIME_PATTERN));

                            PurchaseBaseInfoVO info = new PurchaseBaseInfoVO();
                            info.setId(obj.getPurchaseOrderId());
                            info.setPurchaseId(obj.getOrderExtId());
                            info.setSupplierId(obj.getSupplierId());
                            SupplierConfigDO supplierConfigDO = supplierConfigService.lambdaQuery()
                                    .eq(SupplierConfigDO::getCreateId, uid)
                                    .eq(SupplierConfigDO::getSupplierExtId, obj.getSupplierExtId()).one();
                            if (supplierConfigDO != null) {
                                info.setSupplierName(supplierConfigDO.getName());
                            }
                            info.setOrderName(obj.getProject());
                            info.setOrderAddress(obj.getAddress());
                            info.setCargoReceiver(obj.getReceiver());
                            info.setReceiverTelNumber(obj.getMobile());
                            info.setRequireDate(obj.getReceiveDate().toString());
                            info.setRemark(obj.getRemark());
                            vo.setPurchaseBaseInfo(info);
                            vo.setCargoList(finalCargoMap.get(obj.getId()));
                            return vo;
                        }).collect(Collectors.toList());
                        result.put(attributionCodeMap.get(k), dataList);
                    }
                }
            });
        }
        return result;
    }

    @Override
    void afterCompletion(SdkRespDTO result, List<SdkPushDeliveryVO> pushedData) {
        if (CollUtil.isNotEmpty(pushedData) && CollUtil.isNotEmpty(result.getData())) {
            try {
                Set<String> pushedNos = pushedData.stream()
                        .map(SdkPushDeliveryVO::getJsDeliveryNo)
                        .collect(Collectors.toSet());

                Set<String> confirmedNos = result.getData().stream()
                        .map(Object::toString)
                        .collect(Collectors.toSet());

                Set<String> validNos = pushedNos.stream()
                        .filter(confirmedNos::contains)
                        .collect(Collectors.toSet());

                if (CollUtil.isNotEmpty(validNos)) {
                    log.info("[配送数据推送成功] 推送数量: {}, 确认数量: {}, 有效更新数量: {}, 有效Nos: {}",
                            pushedNos.size(), confirmedNos.size(), validNos.size(), validNos);

                    deliveryService.lambdaUpdate()
                            .in(DeliveryDO::getNo, validNos)
                            .set(DeliveryDO::getPushState, 2)
                            .update();
                } else {
                    log.warn("[配送数据推送异常] 推送的数据与确认的数据无交集, 推送Nos: {}, 确认Nos: {}", pushedNos, confirmedNos);
                }
            } catch (Exception e) {
                log.error("[配送数据推送异常] 处理返回数据失败", e);
            }
        }
    }
}
