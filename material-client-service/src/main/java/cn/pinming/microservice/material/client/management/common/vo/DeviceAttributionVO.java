package cn.pinming.microservice.material.client.management.common.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class DeviceAttributionVO {
    @ApiModelProperty(value = "主键id")
    private Long id;

    private Long deviceId;

    private Long attributionId;

    private String uid;

    @ApiModelProperty(value = "设备机器码")
    private String deviceSn;

    @ApiModelProperty(value = "设备类型(称重一体机-WEIGH、单据回收终端机-RECEIPT_RECYCLE)")
    private String deviceType;

    @ApiModelProperty(value = "设备名称")
    private String deviceName;

    @ApiModelProperty(value = "绑定时间")
    private LocalDateTime bindingTime;

    @ApiModelProperty(value = "累计上传数据量")
    private int num;

    @ApiModelProperty(value = "密码")
    private String password;

    @ApiModelProperty(value = "外部辅助码")
    private String auxiliaryCode;

    @ApiModelProperty(value = "司机自助确认设备模式 1 运单模式  2 单据回收模式 3 双模式（1,2） 4 仅扫码模式")
    private Integer selfCheckMode;

    @ApiModelProperty(value = "组装超时时间 单位 分")
    private Integer timeout;

    @ApiModelProperty(value = "最短时长 单位 分")
    private Integer sameTruckMinDuration;

    @ApiModelProperty(value = "是否开启签名 1 开启 0 关闭")
    private Integer signature;

    @ApiModelProperty(value = "是否跳过扫码 0 否 1 是")
    private Integer skipScanCode;

    @ApiModelProperty("是否自动称重 0 关闭 1 开启")
    private Integer autoWeight;

    @ApiModelProperty("消息机器人id 逗号分割")
    private String msgRobot;

    @ApiModelProperty("消息接收人手机号 逗号分割")
    private String msgReceiver;

    /**
     * 平台期时长 秒
     */
    @ApiModelProperty(value = "平台期时长 秒")
    private Integer platformDuration;

    /**
     * 平台期个数
     */
    @ApiModelProperty(value = "平台期个数")
    private Integer platformCount;

    /**
     * 持续时长 秒
     */
    @ApiModelProperty(value = "持续时长 秒")
    private Integer sustainDuration;

    /**
     * 称重最大值 kg
     */
    @ApiModelProperty(value = "称重最大值 kg")
    private BigDecimal weight;

    /**
     * 防控仪监控周期 秒
     */
    @ApiModelProperty(value = "防控仪监控周期 秒")
    private Integer alarmTime;

    @ApiModelProperty(value = "告警项")
    private String alarmItem;

    private String localIp;

}
