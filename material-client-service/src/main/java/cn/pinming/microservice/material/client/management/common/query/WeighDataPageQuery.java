package cn.pinming.microservice.material.client.management.common.query;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class WeighDataPageQuery extends Page {
    @ApiModelProperty("终端记录id")
    private String recordId;

    @ApiModelProperty("车牌号/识别码")
    private String truckNo;

    @ApiModelProperty("称重类型 1 收料过磅 2 发料过磅")
    private Byte weighType;

    @ApiModelProperty("1 载车称重 2 净货称重")
    private List<Byte> type;

    @ApiModelProperty("上传时间")
    private LocalDateTime gmtCreate;

    @ApiModelProperty("设备机器码")
    private String deviceSn;

    @ApiModelProperty("归属方名称")
    private String name;

    @ApiModelProperty("归属方code")
    private String code;

    @ApiModelProperty("称重开始日期")
    private String startDate;

    @ApiModelProperty("称重结束日期")
    private String endDate;

    @ApiModelProperty("称重时间")
    private String time;

    @ApiModelProperty("使用车牌识别事后checkout的车牌号")
    private String lprTruckNo;

    @ApiModelProperty("风险等级")
    private List<String> riskGrade;

    @ApiModelProperty("称重数据单号")
    private String no;

    @ApiModelProperty("订单id")
    private Long purchaseOrderId;

    @ApiModelProperty("1 一单一料  2 一单多料")
    private Integer materialType;

    @ApiModelProperty("发货单id")
    private Long deliveryId;

    @ApiModelProperty(value = "租户id")
    private String uid;

    @ApiModelProperty(value = "是否为仅毛皮重数据 1 是")
    private Integer isGrossAndTare;

    @ApiModelProperty(value = "id列表")
    private List<Long> idList;
}
