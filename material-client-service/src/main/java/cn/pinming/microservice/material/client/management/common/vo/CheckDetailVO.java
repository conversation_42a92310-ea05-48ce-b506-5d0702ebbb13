package cn.pinming.microservice.material.client.management.common.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class CheckDetailVO implements Serializable {
    private static final long serialVersionUID = 4080782254249021002L;

    @ApiModelProperty(value = "数量复核详情")
    private CheckReverseVO checkReverseVO;
    @ApiModelProperty(value = "车牌及其他")
    private CheckTruckVO checkTruckVO;
    @ApiModelProperty(value = "数量确认详情")
    private CheckConfirmVO checkConfirmVO;
}
