package cn.pinming.microservice.material.client.management.common.vo;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class MsgRobotVO {

    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("消息类型 1钉钉群消息机器人 2企微群消息机器人")
    private Integer type;

    @ApiModelProperty("机器人名称")
    private String name;

//    @ApiModelProperty("创建人名称")
//    private String createName;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate;

    @ApiModelProperty("钉钉access_token=xxxx  企业微信key=xxxx")
    private String token;

    @ApiModelProperty("钉钉 加签")
    private String secret;

    @ApiModelProperty("使用范围 多选")
    private String scope;

}
