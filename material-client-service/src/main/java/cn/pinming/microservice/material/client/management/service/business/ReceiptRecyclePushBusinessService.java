package cn.pinming.microservice.material.client.management.service.business;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.pinming.material.v2.model.dto.ReceiptRecycleModuleDTO;
import cn.pinming.material.v2.model.dto.ReceiptRecyclePushDTO;
import cn.pinming.material.v2.model.dto.ReceiptRecycleWeighDTO;
import cn.pinming.microservice.material.client.management.common.BaseDO;
import cn.pinming.microservice.material.client.management.common.enums.*;
import cn.pinming.microservice.material.client.management.common.mapper.ext.ReceiptPushConfigExtMapper;
import cn.pinming.microservice.material.client.management.common.mapper.ext.ReceiptRecycleExtMapper;
import cn.pinming.microservice.material.client.management.common.model.*;
import cn.pinming.microservice.material.client.management.push.Push;
import cn.pinming.microservice.material.client.management.push.PushClient;
import cn.pinming.microservice.material.client.management.service.biz.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.conn.ConnectTimeoutException;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.SocketTimeoutException;
import java.sql.SQLTimeoutException;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 单据回收推送服务
 */
@Service
@Slf4j
public class ReceiptRecyclePushBusinessService {
    @Resource
    private AppInvocationDailyLogService appInvocationDailyLogService;
    @Resource
    private ReceiptRecycleService receiptRecycleService;
    @Resource
    private ReceiptRecycleModuleService receiptRecycleModuleService;
    @Resource
    private ReceiptRecycleWeighService receiptRecycleWeighService;
    @Resource
    private ReceiptRecycleExtMapper receiptRecycleExtMapper;
    @Resource
    private DeviceAttributionService deviceAttributionService;
    @Resource
    private UserConfigService userConfigService;
    @Resource
    private ReceiptPushConfigExtMapper receiptPushConfigExtMapper;
    @Resource
    private PushLogService pushLogService;
    @Resource
    private FileOssService fileOssService;
    @Resource
    private OCRModuleService ocrModuleService;
    @Resource
    private IReceiptRecycleBatchService receiptRecycleBatchService;
    @Resource
    private AttributionExtConfigService attributionExtConfigService;

    /**
     * 异步推送单据回收数据
     */
    @Async("receiptExecutor")
    public void syncPushReceiptRecycle(ReceiptRecycleDO receiptRecycleDO) {
        //这里触发推送本应该单条数据，后续改造
        ReceiptPushConfigDTO receiptPushConfigDTO = checkUserBusinessConfig(receiptRecycleDO.getUid());
        if (receiptPushConfigDTO == null) {
            return;
        }
        //SDK推送方式
        this.pushReceiptRecycleList(receiptPushConfigDTO);
    }


    private ReceiptPushConfigDTO checkUserBusinessConfig(String uid) {
        ReceiptPushConfigDO receiptPushConfigDO = receiptPushConfigExtMapper.findReceiptPushConfig(uid);
        if (receiptPushConfigDO == null) {
            return null;
        }
        if (receiptPushConfigDO.getPushType() == 1 && receiptPushConfigDO.getPushStatus() == 2 && StrUtil.isNotEmpty(receiptPushConfigDO.getPushUrl())) {
            //SDK推送方式
            List<DeviceAttributionDO> attributionDOList = deviceAttributionService.lambdaQuery().eq(DeviceAttributionDO::getUid, receiptPushConfigDO.getUid()).list();
            if (CollUtil.isEmpty(attributionDOList)) {
                return null;
            }
            List<Long> attributionIdList = attributionDOList.stream().map(DeviceAttributionDO::getId).collect(Collectors.toList());
            return this.buildReceiptPushConfigDTO(receiptPushConfigDO, attributionIdList);
        }
        return null;
    }

    public ReceiptPushConfigDTO buildReceiptPushConfigDTO(ReceiptPushConfigDO receiptPushConfigDO, List<Long> attributionIdList) {
        if (CollUtil.isEmpty(attributionIdList)) {
            return null;
        }
        if (StrUtil.isNotBlank(receiptPushConfigDO.getExcludedAttributionIds())) {
            // 排除的归属方
            attributionIdList.removeAll(StrUtil.split(receiptPushConfigDO.getExcludedAttributionIds(), ",").stream().map(Long::new).collect(Collectors.toList()));
            if (CollUtil.isEmpty(attributionIdList)) return null;
        }
        ReceiptPushConfigDTO receiptPushConfigDTO = new ReceiptPushConfigDTO();
        BeanUtils.copyProperties(receiptPushConfigDO, receiptPushConfigDTO);
        receiptPushConfigDTO.setAttributionIdList(attributionIdList);
        return receiptPushConfigDTO;
    }

    /**
     * 推送租户下所有未推送的单据回收数据
     */
    @Async("receiptExecutor")
    public void pushReceiptRecycleList(ReceiptPushConfigDTO receiptPushConfigDTO) {
        // 查找数据 （"未推送" || 长时间处于"队列中"(5min)）
        List<ReceiptRecycleDO> pushReceiptRecycleList = receiptRecycleExtMapper.needPushDatas(receiptPushConfigDTO.getUid(), receiptPushConfigDTO.getAttributionIdList());
        if (CollUtil.isEmpty(pushReceiptRecycleList)) {
            return;
        }
        // 查出数据后置为“待确认”
        receiptRecycleService.lambdaUpdate()
                .in(ReceiptRecycleDO::getId, pushReceiptRecycleList.stream().map(ReceiptRecycleDO::getId).collect(Collectors.toList()))
                .set(ReceiptRecycleDO::getPushStatus, PushStatusEnum.UNCONFIRMED.getVal())
                .update();
        log.info("单据回收推送开启,推送内容:{},时间为:{}", pushReceiptRecycleList.stream().map(ReceiptRecycleDO::getId).collect(Collectors.toList()), LocalDateTime.now());
        for (ReceiptRecycleDO receiptRecycleDO : pushReceiptRecycleList) {
            boolean isUsageRemaining = this.pushOneReceiptRecycle(receiptPushConfigDTO, receiptRecycleDO);
            if (!isUsageRemaining) continue;//批量推送单据回收数据时,存在无使用次数则不再推送
        }
    }

    /**
     * 推送一条单据回收数据
     */
    public boolean pushOneReceiptRecycle(ReceiptPushConfigDTO receiptPushConfigDTO, ReceiptRecycleDO receiptRecycleDO) {
        Long apiRemaining = userConfigService.getReceiptApiRemainingCount(receiptPushConfigDTO.getUid());
//        String uid = receiptPushConfigDTO.getUid();
//        Long attributionId = receiptRecycleDO.getAttributionId();
//        AttributionExtConfigDO one = attributionExtConfigService.lambdaQuery().eq(AttributionExtConfigDO::getUid, uid)
//                .eq(AttributionExtConfigDO::getAttributionId, attributionId).one();
//        if (one != null) {
//            LocalDateTime dateExpire = one.getDateExpire();
//            if (dateExpire != null && dateExpire.isBefore(LocalDateTime.now())) {
//                // 归属方到期，不再推送
//                log.info("归属方到期，不再推送，uid：{}，attributionId：{}", uid, attributionId);
//                return false;
//            }
//        }
        if (apiRemaining == null || apiRemaining <= 0) {
            log.info("SDK推送次数已用完，uid：{}。", receiptPushConfigDTO.getUid());
            return false;
        }
        //模板数据组装
        List<ReceiptRecycleModuleDO> receiptRecycleModuleDOList = receiptRecycleModuleService.lambdaQuery()
                .eq(ReceiptRecycleModuleDO::getReceiptRecycleId, receiptRecycleDO.getId())
                .eq(ReceiptRecycleModuleDO::getModuleId, receiptRecycleDO.getModuleId())
                .eq(ReceiptRecycleModuleDO::getIsEffective, IsEffectiveEnum.YES.getValue())
                .list();
        List<ReceiptRecycleWeighDO> receiptRecycleWeighDOList = null;
        if (receiptRecycleDO.getOcrType() != null && receiptRecycleDO.getOcrType().equals(OcrTypeEnum.AUTO_WEIGH.getValue())) {
            //称重数据组装
            receiptRecycleWeighDOList = receiptRecycleWeighService.lambdaQuery()
                    .eq(ReceiptRecycleWeighDO::getReceiptRecycleId, receiptRecycleDO.getId())
                    .eq(ReceiptRecycleWeighDO::getIsEffective, IsEffectiveEnum.YES.getValue())
                    .orderByDesc(ReceiptRecycleWeighDO::getWeight).list();
        }
        ReceiptRecyclePushDTO receiptRecyclePushDTO = buildReceiptRecyclePushDTO(receiptRecycleDO, receiptRecycleModuleDOList, receiptRecycleWeighDOList);
        if (StrUtil.isBlank(receiptRecyclePushDTO.getPrimaryCode()) || CollUtil.isEmpty(receiptRecycleWeighDOList) || receiptRecycleWeighDOList.size() != 2) {
            return false;
        }
        this.push(receiptPushConfigDTO, receiptPushConfigDTO.getPushUrl(), receiptRecyclePushDTO);
//        //单据回收数据推送状态
//        receiptRecycleService.lambdaUpdate().eq(ReceiptRecycleDO::getId, receiptRecycleDO.getId())
//                .ne(ReceiptRecycleDO::getPushStatus, PushStatusEnum.PUSH.getVal())
//                .set(ReceiptRecycleDO::getPushStatus, isSuccess ? PushStatusEnum.UNCONFIRMED.getVal() : PushStatusEnum.WAIT.getVal())
//                .set(ReceiptRecycleDO::getWaitTime, LocalDateTime.now()).update();
//        if (isSuccess) {
//            appInvocationDailyLogService.saveInvokeLog(DeveloperAppEnum.RECYCLE_PUSH.value(), receiptPushConfigDTO.getUid(), null, 1);
//        }
        return true;
    }


    @NotNull
    public ReceiptRecyclePushDTO buildReceiptRecyclePushDTO(ReceiptRecycleDO receiptRecycleDO, List<ReceiptRecycleModuleDO> receiptRecycleModuleDOList, List<ReceiptRecycleWeighDO> receiptRecycleWeighDOList) {
        ReceiptRecyclePushDTO receiptRecyclePushDTO = new ReceiptRecyclePushDTO();
        BeanUtils.copyProperties(receiptRecycleDO, receiptRecyclePushDTO);
        DeviceAttributionDO one = deviceAttributionService.lambdaQuery()
                .eq(BaseDO::getId, receiptRecycleDO.getAttributionId())
                .one();
        receiptRecyclePushDTO.setPrimaryCode(one.getPrimaryCode());
        if (StrUtil.isNotEmpty(receiptRecycleDO.getRecyclePic())) {
            receiptRecyclePushDTO.setRecyclePicUrls(fileOssService.getPicUrlDTOs(Arrays.asList(receiptRecycleDO.getRecyclePic().split(","))));
        }
        OcrModuleDO moduleDO = ocrModuleService.lambdaQuery()
                .eq(BaseDO::getId, receiptRecycleDO.getModuleId())
                .one();
        receiptRecyclePushDTO.setContractId(moduleDO.getExtId());
        //称重数据组装
        if (CollUtil.isNotEmpty(receiptRecycleWeighDOList)) {
            receiptRecyclePushDTO.setWeighList(receiptRecycleWeighDOList.stream().map(receiptRecycleWeighDO -> {
                ReceiptRecycleWeighDTO receiptRecycleWeighDTO = new ReceiptRecycleWeighDTO();
                BeanUtil.copyProperties(receiptRecycleWeighDO, receiptRecycleWeighDTO);
                if (StrUtil.isNotEmpty(receiptRecycleWeighDO.getFileIds())) {
                    receiptRecycleWeighDTO.setFileIdUrls(fileOssService.getPicUrlDTOs(Arrays.asList(receiptRecycleWeighDO.getFileIds().split(","))));
                }
                return receiptRecycleWeighDTO;
            }).collect(Collectors.toList()));
        }
        //模板数据组装
        if (CollUtil.isNotEmpty(receiptRecycleModuleDOList)) {
            receiptRecyclePushDTO.setModuleList(receiptRecycleModuleDOList.stream().map(receiptRecycleModuleDO -> {
                ReceiptRecycleModuleDTO receiptRecycleModuleDTO = new ReceiptRecycleModuleDTO();
                BeanUtil.copyProperties(receiptRecycleModuleDO, receiptRecycleModuleDTO);
                return receiptRecycleModuleDTO;
            }).collect(Collectors.toList()));
        }

        // 添加 扩展编码
        if (receiptRecycleDO.getBatchId() != null) {
            ReceiptRecycleBatchDO recycleBatchDO = receiptRecycleBatchService.getById(receiptRecycleDO.getBatchId());
            if (recycleBatchDO != null) {
                receiptRecyclePushDTO.setExtCode(recycleBatchDO.getExtCode());
            }
        }
        return receiptRecyclePushDTO;
    }


    /**
     * 推送操作
     */
    private void push(ReceiptPushConfigDO receiptPushConfigDO, String pushUrl, ReceiptRecyclePushDTO receiptRecyclePushDTO) {
        String jsonBody = JSONUtil.toJsonStr(receiptRecyclePushDTO);
        try {
            Push push = new PushClient(receiptPushConfigDO.getEndpoint(), pushUrl, receiptPushConfigDO.getRequestHeader(), receiptPushConfigDO.getRequestMode(),
                    receiptPushConfigDO.getAppKey(), receiptPushConfigDO.getAppSecretKey(), receiptPushConfigDO.getSignatureTemplate(), receiptPushConfigDO.getSignatureAlgorithm(),
                    receiptPushConfigDO.getResponseType(), receiptPushConfigDO.getResponseFlag());
//            log.info("推送请求参数组装：{}", JSONUtil.toJsonStr(push));
            push.pushData(jsonBody);
            log.info("推送成功:uid:{},jsonStr：{}", receiptPushConfigDO.getUid(), jsonBody);
            //推送日志
            pushLogService.createLog(receiptPushConfigDO.getUid(), jsonBody, PushLogStatusEnum.TWO.value(), null);
        } catch (ConnectTimeoutException | SocketTimeoutException | SQLTimeoutException e) {
            //超时 单据回收数据推送状态置为“队列中”
            receiptRecycleService.lambdaUpdate().eq(ReceiptRecycleDO::getId, receiptRecyclePushDTO.getId())
                    .ne(ReceiptRecycleDO::getPushStatus, PushStatusEnum.PUSH.getVal())
                    .set(ReceiptRecycleDO::getPushStatus, PushStatusEnum.WAIT.getVal())
                    .set(ReceiptRecycleDO::getWaitTime, LocalDateTime.now()).update();
            return;
        } catch (Exception e) {
            System.out.println(e);
            log.error("推送失败:uid:{},jsonStr：{},errorMessage：{}", receiptPushConfigDO.getUid(), jsonBody, e.getMessage());
            pushLogService.createLog(receiptPushConfigDO.getUid(), jsonBody, PushLogStatusEnum.SIX.value(), e.getMessage());

            //非超时 单据回收数据推送状态置为“已推送”
            receiptRecycleService.lambdaUpdate().eq(ReceiptRecycleDO::getId, receiptRecyclePushDTO.getId())
                    .ne(ReceiptRecycleDO::getPushStatus, PushStatusEnum.PUSH.getVal())
                    .set(ReceiptRecycleDO::getPushStatus, PushStatusEnum.PUSH.getVal()).update();
            return;
        }
        // 获取回调结果 单据回收数据推送状态置为“已推送”
        receiptRecycleService.lambdaUpdate().eq(ReceiptRecycleDO::getId, receiptRecyclePushDTO.getId())
                .ne(ReceiptRecycleDO::getPushStatus, PushStatusEnum.PUSH.getVal())
                .set(ReceiptRecycleDO::getPushStatus, PushStatusEnum.PUSH.getVal()).update();
    }

    @Data
    public static class ReceiptPushConfigDTO extends ReceiptPushConfigDO {

        @ApiModelProperty(value = "需推送的归属方id")
        private List<Long> attributionIdList;
    }
}
