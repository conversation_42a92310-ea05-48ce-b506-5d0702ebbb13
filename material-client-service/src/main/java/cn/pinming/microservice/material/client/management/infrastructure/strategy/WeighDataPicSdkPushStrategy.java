package cn.pinming.microservice.material.client.management.infrastructure.strategy;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.pinming.material.v2.model.dto.WeighDataPicResDTO;
import cn.pinming.microservice.material.client.management.common.dto.push.PushWeighDataPicDTO;
import cn.pinming.microservice.material.client.management.common.enums.SdkPushTypeEnum;
import cn.pinming.microservice.material.client.management.common.mapper.ext.WeighDataPicExtMapper;
import cn.pinming.microservice.material.client.management.common.model.WeighDataPicDO;
import cn.pinming.microservice.material.client.management.common.vo.push.PushRouteConfigVO;
import cn.pinming.microservice.material.client.management.service.biz.WeighDataPicService;
import cn.pinming.microservice.material.client.management.service.push.sdk.dto.SdkRespDTO;
import cn.pinming.zhjz.cxptz.microservice.filecenter.api.FileCenterService;
import cn.pinming.zhjz.cxptz.microservice.filecenter.api.model.DownloadUrlOptionsDto;
import cn.pinming.zhjz.cxptz.microservice.filecenter.api.model.FileIdentityDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 称重照片推送策略
 *
 * <AUTHOR>
 */
@Slf4j
@Component("WEIGH_DATA_PIC")
public class WeighDataPicSdkPushStrategy extends AbstractSkdPushStrategy<PushWeighDataPicDTO> {

    @Resource
    private WeighDataPicService weighDataPicService;

    @Resource
    private WeighDataPicExtMapper weighDataPicExtMapper;

    @DubboReference
    private FileCenterService fileCenterService;

    @Override
    Long getRouteConfigId() {
        return SdkPushTypeEnum.WEIGH_DATA_PIC.getVal();
    }

    @Override
    Map<String, List<PushWeighDataPicDTO>> queryDataListMap(PushRouteConfigVO config, Map<Long, String> attributionMap) {
        Map<String, List<PushWeighDataPicDTO>> result = new HashMap<>();

        if (CollUtil.isNotEmpty(attributionMap)) {
            String uid = config.getUid();
            Set<Long> attributionIdList = attributionMap.keySet();

            // 查询需要推送的称重照片数据
            List<WeighDataPicDO> picList = weighDataPicExtMapper.selectPicToPush(uid, attributionIdList);

            if (CollUtil.isNotEmpty(picList)) {
                // 按归属方分组
                Map<Long, List<WeighDataPicDO>> attributionPicMap = picList.stream()
                        .collect(Collectors.groupingBy(WeighDataPicDO::getAttributionId));

                // 获取图片下载URL
                Map<String, String> recordPicUuidList = picList.stream()
                        .collect(Collectors.toMap(WeighDataPicDO::getFileId, WeighDataPicDO::getFilePath));

                if (CollUtil.isNotEmpty(recordPicUuidList)) {
                    DownloadUrlOptionsDto options = new DownloadUrlOptionsDto();
                    options.setTime(Date.from(LocalDate.now().plusYears(10).atStartOfDay(ZoneId.systemDefault()).toInstant()));

                    List<FileIdentityDto> fileIdentities = recordPicUuidList.keySet().stream().map(e -> {
                        FileIdentityDto dto = new FileIdentityDto();
                        dto.setFileUuid(e);
                        return dto;
                    }).collect(Collectors.toList());

                    Map<FileIdentityDto, String> downloadDtoMap = fileCenterService.acquireFileDownloadUrls(fileIdentities, options);

                    if (CollUtil.isNotEmpty(downloadDtoMap)) {
                        Map<String, String> downloadMap = new HashMap<>();
                        downloadDtoMap.forEach((k, url) -> downloadMap.put(k.getFileUuid(), url));

                        attributionPicMap.forEach((attributionId, pics) -> {
                            String attributionCode = attributionMap.get(attributionId);
                            if (attributionCode != null) {
                                List<PushWeighDataPicDTO> pushDataList = new ArrayList<>();

                                // 处理每张照片
                                for (WeighDataPicDO pic : pics) {
                                    try {
                                        String fileId = pic.getFileId();
                                        if (downloadMap.containsKey(fileId)) {
                                            PushWeighDataPicDTO pushPicDTO = new PushWeighDataPicDTO();
                                            pushPicDTO.setId(pic.getId());
                                            pushPicDTO.setRecordId(pic.getRecordId());
                                            // 从文件路径解析摄像头编号
                                            String filePath = pic.getFilePath();
                                            if (StrUtil.isNotBlank(filePath)) {
                                                String[] split = filePath.split("/");
                                                if (split.length > 2 && StrUtil.isNotBlank(split[2])) {
                                                    pushPicDTO.setCamNo(split[2]);
                                                }
                                            }
                                            // 设置图片URL
                                            pushPicDTO.setPic(downloadMap.get(fileId));

                                            pushDataList.add(pushPicDTO);
                                        }
                                    } catch (Exception e) {
                                        log.error("获取称重照片详情失败，recordId: {}, fileId: {}", pic.getRecordId(), pic.getFileId(), e);
                                    }
                                }

                                if (CollUtil.isNotEmpty(pushDataList)) {
                                    result.put(attributionCode, pushDataList);
                                }
                            }
                        });
                    }
                }
            }
        }

        return result;
    }

    @Override
    void afterCompletion(SdkRespDTO result, List<PushWeighDataPicDTO> pushedData) {
        if (CollUtil.isNotEmpty(pushedData) && CollUtil.isNotEmpty(result.getData())) {
            try {
                Set<Long> pushedIds = pushedData.stream()
                        .map(PushWeighDataPicDTO::getId)
                        .collect(Collectors.toSet());

                List<WeighDataPicResDTO> confirmedList = JSONUtil.toList(result.getData().toString(), WeighDataPicResDTO.class);
                Set<Long> confirmedIds = confirmedList.stream()
                        .map(WeighDataPicResDTO::getId)
                        .collect(Collectors.toSet());

                Set<Long> validIds = pushedIds.stream()
                        .filter(confirmedIds::contains)
                        .collect(Collectors.toSet());

                if (CollUtil.isNotEmpty(validIds)) {
                    log.info("[称重照片推送成功] 推送数量: {}, 确认数量: {}, 有效更新数量: {}, 有效IDs: {}",
                            pushedIds.size(), confirmedIds.size(), validIds.size(), validIds);

                    weighDataPicService.lambdaUpdate()
                            .in(WeighDataPicDO::getId, validIds)
                            .set(WeighDataPicDO::getPushStatus, (byte) 3)
                            .update();
                } else {
                    log.warn("[称重照片推送异常] 推送的数据与确认的数据无交集, 推送IDs: {}, 确认IDs: {}", pushedIds, confirmedIds);
                }
            } catch (Exception e) {
                log.error("[称重照片推送异常] 处理返回数据失败", e);
            }
        }
    }
}
