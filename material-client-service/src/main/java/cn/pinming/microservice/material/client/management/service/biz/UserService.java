package cn.pinming.microservice.material.client.management.service.biz;

import cn.pinming.microservice.material.client.management.common.form.*;
import cn.pinming.microservice.material.client.management.common.enums.EmailCaptchaEnum;
import cn.pinming.microservice.material.client.management.common.model.UserDO;
import cn.pinming.microservice.material.client.management.common.vo.UserVO;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.servlet.http.HttpSession;

/**
 * <AUTHOR>
 * @date 2023/6/12
 */
public interface UserService extends IService<UserDO> {
    String DEFAULT_NAME = "默认归属方";
    String DEFAULT_CODE = "default_data_owner";
    String GRAPH_CAPTCHA_SESSION_ATTRIBUTE = "graph-captcha";
    String GRAPH_CAPTCHA_REDIS_PREFIX = "graph-captcha-";
    String EMAIL_REGISTER_CAPTCHA_REDIS_PREFIX = "email-register-captcha-";
    String EMAIL_LOGIN_CAPTCHA_REDIS_PREFIX = "email-login-captcha-";
    String EMAIL_GENERATE_CAPTCHA_REDIS_PREFIX = "email-generate-captcha-";
    String EMAIL_DELETE_ATTRIBUTION_CAPTCHA_REDIS_PREFIX = "email-delete-attribution-captcha-";
    String EMAIL_BIND_ATTRIBUTION_CAPTCHA_REDIS_PREFIX = "email-bind-attribution-captcha-";
    String EMAIL_DELETE_MODULE_REDIS_PREFIX = "email-delete-module-captcha-";
    String EMAIL_UPDATE_PASSWORD_REDIS_PREFIX = "email-update-password-captcha-";
    String EMAIL_UPDATE_EMAIL_REDIS_PREFIX = "email-update-email-captcha-";
    /**
     * 注册
     * @param registerForm
     * @return
     */
    boolean register(UserRegisterForm registerForm);

    /**
     * 图形验证码
     * @param session
     * @return
     */
    String graphCaptcha(HttpSession session);

    Boolean emailCaptchaFree(String email, EmailCaptchaEnum captchaEnum);

    /**
     * 邮箱验证码
     * @param captchaEnum
     * @return
     */
    Boolean emailCaptcha(EmailCaptchaEnum captchaEnum);

    /**
     * 获取动态盐值
     * @param email
     * @return
     */
    String getDynamicSalt(String email);

    /**
     * 密码登录
     * @param loginForm
     * @return
     */
    String passwordLogin(PasswordLoginForm loginForm);

    /**
     * 邮箱验证码登录
     * @param loginForm
     * @return
     */
    String emailLogin(EmailLoginForm loginForm);

    /**
     * 获取当前登录用户
     * @return
     */
    UserVO currentUser();

    UserVO currentUserByType();


    void renew(UserUpdateForm form);

    UserDO getByUid(String uid);

    void updateUserPassword(UpdatePasswordForm form);

    void updateUserEmail(UpdateEmailForm form);

    UserDO getUserByAppKey(String appKey);
}
