package cn.pinming.microservice.material.client.management.common.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2024/7/26 下午4:58
 */
@Getter
public enum MessageRobotTypeEnum {

    DING_DING(1, "钉钉"),
    WECHAT(2, "企业微信"),
    ;

    public static final Map<Integer, String> KEY_MAP = Arrays.stream(values()).collect(Collectors.toMap(MessageRobotTypeEnum::getVal, MessageRobotTypeEnum::getDesc));

    public static final Map<String, Integer> VAL_MAP = Arrays.stream(values()).collect(Collectors.toMap(MessageRobotTypeEnum::getDesc, MessageRobotTypeEnum::getVal));

    private final Integer val;

    private final String desc;

    MessageRobotTypeEnum(Integer val, String desc) {
        this.val = val;
        this.desc = desc;
    }
}
