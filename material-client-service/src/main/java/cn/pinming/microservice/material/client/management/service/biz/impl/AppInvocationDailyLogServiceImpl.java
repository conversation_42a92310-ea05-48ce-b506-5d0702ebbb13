package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.pinming.microservice.material.client.management.common.enums.DeveloperAppEnum;
import cn.pinming.microservice.material.client.management.common.mapper.AppInvocationDailyLogMapper;
import cn.pinming.microservice.material.client.management.common.model.AppInvocationDailyLogDO;
import cn.pinming.microservice.material.client.management.infrastructure.constant.ConfigConstant;
import cn.pinming.microservice.material.client.management.service.biz.AppInvocationDailyLogService;
import cn.pinming.springboot.starter.redis.util.RedisUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Objects;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2023/8/8 10:22
 */
@Slf4j
@Service
public class AppInvocationDailyLogServiceImpl extends ServiceImpl<AppInvocationDailyLogMapper, AppInvocationDailyLogDO> implements AppInvocationDailyLogService {

    @Resource
    public RedisUtil redisUtil;

    @Override
    public void saveInvokeLog(Long appId, String uid, Long attributionId,long count) {
        String now = LocalDate.now().toString();
        // 归属方
        AppInvocationDailyLogDO dailyLogDO = this.lambdaQuery().eq(AppInvocationDailyLogDO::getAppId, appId)
                .eq(AppInvocationDailyLogDO::getDate, now)
                .eq(attributionId != null,AppInvocationDailyLogDO::getAttributionId, attributionId)
//                .isNull(attributionId == null, AppInvocationDailyLogDO::getAttributionId)
                .eq(AppInvocationDailyLogDO::getCreateId, uid).one();

        boolean isSuccess;
        if (dailyLogDO == null) {
            dailyLogDO = new AppInvocationDailyLogDO();
            dailyLogDO.setAppId(appId);
            dailyLogDO.setDate(now);
            dailyLogDO.setCreateId(uid);
            dailyLogDO.setModifyId(uid);
            dailyLogDO.setUsedApiTotal(count);
            dailyLogDO.setAttributionId(attributionId);
            isSuccess = this.save(dailyLogDO);
        } else {
            isSuccess = this.lambdaUpdate()
                    .set(AppInvocationDailyLogDO::getUsedApiTotal, dailyLogDO.getUsedApiTotal() + count)
                    .eq(AppInvocationDailyLogDO::getId, dailyLogDO.getId())
                    .eq(AppInvocationDailyLogDO::getUsedApiTotal, dailyLogDO.getUsedApiTotal())//防止并发场景
                    .update();
        }

        if (isSuccess) {
            String key;
            if (DeveloperAppEnum.weighDataService().contains(appId)) {
                //剩余数量递减
                key = String.format(ConfigConstant.REMAINING_API_KEY, uid);
            } else if (Objects.equals(DeveloperAppEnum.OCR.value(), appId) && attributionId == null) {
                key = String.format(ConfigConstant.REMAINING_RECEIPT_KEY, uid);
            } else {
                return;
            }
            try {
                if (redisUtil.hasKey(key)) {
                    redisUtil.decr(key, count);
                }
            } catch (Exception e) {
                log.error("redisUtil.decr error", e);
                redisUtil.del(key);
            }
        }
    }


}
