package cn.pinming.microservice.material.client.management.common.dto.push;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class PushDataDTO {
    /**
     * 终端记录id
     */
    private String recordId;

    /**
     * 设备机器码
     */
    private String deviceSn;

    /**
     * 外部辅助码
     */
    private String auxiliaryCode;

    /**
     * 车牌号
     */
    private String truckNo;

    /**
     * 材料名称
     */
    private String material;

    /**
     * 重量
     */
    private BigDecimal weight;

    /**
     * 称重单位
     */
    private String unit;

    /**
     * 1 载车称重 2 净货称重
     */
    private Byte type;

    /**
     * 风险等级（低：LOW，中：MIDDLE，高：HIGH）
     */
    private String riskGrade;

    /**
     * 称重时间
     */
    private String weighTime;

    /**
     * 使用车牌识别事后checkout的车牌号
     */
    private String lprTruckNo;

    /**
     * 归属方主主code
     */
    private String attributionCode;

    /**
     * 称重照片列表
     */
    private List<PushDataPicDTO> picList;
}
