package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.microservice.material.client.management.common.BaseDO;
import cn.pinming.microservice.material.client.management.common.dto.RebarMaterialCompareDTO;
import cn.pinming.microservice.material.client.management.common.enums.*;
import cn.pinming.microservice.material.client.management.common.form.RebarCheckDetailForm;
import cn.pinming.microservice.material.client.management.common.form.RebarCheckForm;
import cn.pinming.microservice.material.client.management.common.mapper.CheckInfoMapper;
import cn.pinming.microservice.material.client.management.common.mapper.CheckMapper;
import cn.pinming.microservice.material.client.management.common.mapper.ext.CheckDetailExtMapper;
import cn.pinming.microservice.material.client.management.common.model.*;
import cn.pinming.microservice.material.client.management.common.query.CheckQuery;
import cn.pinming.microservice.material.client.management.common.vo.*;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizErrorException;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizExceptionMessageEnum;
import cn.pinming.microservice.material.client.management.infrastructure.util.ConsumeIdUtil;
import cn.pinming.microservice.material.client.management.infrastructure.util.NoUtil;
import cn.pinming.microservice.material.client.management.proxy.FileServiceProxy;
import cn.pinming.microservice.material.client.management.proxy.MaterialServiceProxy;
import cn.pinming.microservice.material.client.management.service.biz.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class CheckServiceImpl extends ServiceImpl<CheckMapper, CheckDO> implements CheckService {
    @Resource
    private CheckInfoService checkInfoService;
    @Resource
    private CheckDetailService checkDetailService;
    @Resource
    private FileServiceProxy fileServiceProxy;
    @Resource
    private CheckMapper checkExtMapper;
    @Resource
    private CheckDetailExtMapper checkDetailExtMapper;
    @Resource
    private CheckInfoMapper checkInfoExtMapper;
    @Resource
    private FileOssService fileOssService;
    @Resource
    private ConsumeIdUtil consumeIdUtil;
    @Resource
    private DeveloperService developerService;
    @Resource
    private UserConsumerService userConsumerService;
    @Resource
    private UserConsumerCombineService userConsumerCombineService;
    @Resource
    private AttributionExtConfigService attributionExtConfigService;
    @Resource
    private NoUtil noUtil;
    @Resource
    private DeviceAttributionService deviceAttributionService;
    @Resource
    private MaterialServiceProxy materialServiceProxy;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long rebarCheckAdd(RebarCheckForm form) {
        // 获取用户身份
        ConsumerVO loginInfo = consumeIdUtil.getLoginInfo(form.getAttributionId());

        // 前期校验
        preCheck(loginInfo,form.getPhoneSn(),form.getAttributionId());

        CheckDO checkDO = new CheckDO();
        BeanUtil.copyProperties(form,checkDO);
        checkDO.setUid(loginInfo.getUid());
        checkDO.setConsumeId(loginInfo.getConsumeId());
        checkDO.setNo(noUtil.getRebarCheckKeyPrefixNo(loginInfo.getList().get(0).getCode()));
        this.save(checkDO);

        List<RebarCheckDetailForm> list = form.getList();
        List<CheckDetailDO> detailDOList = list.stream().map(e -> {
            CheckDetailDO checkDetailDO = new CheckDetailDO();
            BeanUtil.copyProperties(e, checkDetailDO);
            checkDetailDO.setCheckId(checkDO.getId());
            return checkDetailDO;
        }).collect(Collectors.toList());
        BigDecimal sendWeight = detailDOList.stream()
                .map(CheckDetailDO::getSendWeight)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        checkDetailService.saveBatch(detailDOList);

        CheckInfoDO checkInfoDO = new CheckInfoDO();
        BeanUtil.copyProperties(form,checkInfoDO);
        checkInfoDO.setCheckId(checkDO.getId());
        checkInfoDO.setSendWeight(sendWeight);
        BigDecimal weightDif = NumberUtil.sub(checkInfoDO.getActualWeight(), checkInfoDO.getSendWeight());
        BigDecimal weightRate = NumberUtil.mul(NumberUtil.div(weightDif, sendWeight, 4, RoundingMode.HALF_UP),100);
        checkInfoDO.setWeightCheckResult(checkInfoDO.getSendWeight().compareTo(checkInfoDO.getActualWeight()) == 0 ? CheckResultEnum.YES.value() : CheckResultEnum.NO.value());
        checkInfoDO.setWeightDif(weightDif);
        checkInfoDO.setWeightRate(weightRate);
        checkInfoService.save(checkInfoDO);

        return checkDO.getId();
    }

    @Override
    public void rebarCheckFirst(Long id) {
        // TODO: 2024/5/20 留口子给校验规则

        List<CheckDetailDO> list = checkDetailService.lambdaQuery()
                .eq(CheckDetailDO::getCheckId, id)
                .list();
        if (CollUtil.isNotEmpty(list)) {
            List<Long> materialIds = list.stream().map(CheckDetailDO::getMaterialId).distinct().collect(Collectors.toList());
            List<RebarMaterialDO> materialsByMaterialIds = materialServiceProxy.getMaterialsByMaterialIds(materialIds);

            if (CollUtil.isNotEmpty(materialsByMaterialIds)) {
                Map<Long, RebarMaterialDO> materialMap = materialsByMaterialIds.stream().collect(Collectors.toMap(BaseDO::getId, e -> e));

                list.forEach(e -> {
                    RebarMaterialDO rebarMaterialDO = materialMap.get(e.getMaterialId());
                    if (ObjectUtil.isNull(rebarMaterialDO) || ObjectUtil.isNull(rebarMaterialDO.getWeighValue())) {
                        throw new BizErrorException("-238","请先基石管理员维护" + rebarMaterialDO.getMaterialSpec() + "理重值");
                    }

                    // 根数自洽
                    BigDecimal theoryAmount = NumberUtil.div(NumberUtil.div(e.getSendWeight(), rebarMaterialDO.getWeighValue()),e.getLength(),4);
                    e.setTheoryAmount(theoryAmount);
                    e.setTheoryAmountDif(NumberUtil.sub(theoryAmount,e.getSendAmount()));
                    e.setTheoryAmountResult(BigDecimal.valueOf(e.getSendAmount()).compareTo(theoryAmount) == 0 ? CheckResultEnum.YES.value() : CheckResultEnum.NO.value());

                    // 重量自洽
                    BigDecimal theoryWeight = NumberUtil.mul(NumberUtil.mul(e.getSendAmount(),rebarMaterialDO.getWeighValue()),e.getLength());
                    e.setTheoryWeight(theoryWeight);
                    e.setTheoryWeightDif(NumberUtil.sub(theoryWeight,e.getSendWeight()));
                    e.setTheoryWeightResult(e.getSendWeight().compareTo(theoryWeight) == 0 ? CheckResultEnum.YES.value() : CheckResultEnum.NO.value());
                });

                checkDetailService.updateBatchById(list);
            }else {
                throw new BizErrorException(BizExceptionMessageEnum.MANAGE_MATERIAL);
            }
        }
    }

    @Override
    public void rebarCheckSecond(Long id) {
        CheckInfoDO checkInfoDO = checkInfoService.lambdaQuery()
                .eq(CheckInfoDO::getCheckId, id)
                .one();
        List<CheckDetailDO> checkDetailDOList = checkDetailService.lambdaQuery()
                .eq(CheckDetailDO::getCheckId, id)
                .list();

        BigDecimal sumWeight = BigDecimal.ZERO;
        if (ObjectUtil.isNull(checkInfoDO.getReverseWeightType())) {
            throw new BizErrorException(BizExceptionMessageEnum.REVERSEWEIGHTTYPE_IS_NULL);
        }
        if (checkInfoDO.getReverseWeightType() == 1) {
            sumWeight = checkInfoDO.getActualWeight();
        }
        if (checkInfoDO.getReverseWeightType() == 2) {
            sumWeight = checkInfoDO.getSendWeight();
        }

        BigDecimal finalSumWeight = sumWeight;
        // b:∑(理重值 * 规格长度 * 复核根数)
        final BigDecimal[] a = {BigDecimal.ZERO};
        // 材料id -> 复核根数
        Map<RebarMaterialCompareDTO,BigDecimal> amountMap = new HashMap<>();

        List<Long> materialIds = checkDetailDOList.stream().map(CheckDetailDO::getMaterialId).distinct().collect(Collectors.toList());
        List<RebarMaterialDO> materialsByMaterialIds = materialServiceProxy.getMaterialsByMaterialIds(materialIds);
        if (CollUtil.isNotEmpty(materialsByMaterialIds)) {
            Map<Long, RebarMaterialDO> materialMap = materialsByMaterialIds.stream().collect(Collectors.toMap(BaseDO::getId, e -> e));

            checkDetailDOList.forEach(e -> {
                RebarMaterialDO rebarMaterialDO = materialMap.get(e.getMaterialId());
                if (ObjectUtil.isNull(rebarMaterialDO) || ObjectUtil.isNull(rebarMaterialDO.getWeighValue())) {
                    throw new BizErrorException("-238","请先基石管理员维护" + rebarMaterialDO.getMaterialSpec() + "理重值");
                }

                // 根数自洽
                BigDecimal theoryAmount = NumberUtil.div(NumberUtil.div(NumberUtil.mul(finalSumWeight,NumberUtil.div(e.getSendWeight(),checkInfoDO.getSendWeight())),rebarMaterialDO.getWeighValue()),e.getLength(),4);
                e.setReverseTheoryAmount(theoryAmount);
                e.setReverseTheoryAmountDif(NumberUtil.sub(theoryAmount,e.getSendAmount()));
                e.setReverseTheoryAmountResult(BigDecimal.valueOf(e.getSendAmount()).compareTo(theoryAmount) == 0 ? CheckResultEnum.YES.value() : CheckResultEnum.NO.value());

                RebarMaterialCompareDTO dto = new RebarMaterialCompareDTO();
                dto.setMaterialId(e.getMaterialId());
                dto.setLength(e.getLength());
                // ∑(理重值(千克·米) * 规格长度 * 复核根数）
                if (checkInfoDO.getCheckType().equals(CheckTypeEnum.ONE.value())) {
                    // 仅称重模式  理重值 * 规格长度 * 复核根数(反向复核根数)
                    a[0] = NumberUtil.add(a[0],NumberUtil.mul(NumberUtil.mul(e.getLength(),rebarMaterialDO.getWeighValue()),e.getReverseTheoryAmount()));
                    amountMap.put(dto,e.getReverseTheoryAmount());
                }else {
                    // 实称+实点  理重值 * 规格长度 * 复核根数(实点根数)
                    a[0] = NumberUtil.add(a[0],NumberUtil.mul(NumberUtil.mul(e.getLength(),rebarMaterialDO.getWeighValue()),e.getActualAmount()));
                    amountMap.put(dto,BigDecimal.valueOf(e.getActualAmount()));
                }
            });

            checkDetailDOList.forEach(e -> {
                // 重量自洽
                RebarMaterialDO rebarMaterialDO = materialMap.get(e.getMaterialId());

                RebarMaterialCompareDTO dto = new RebarMaterialCompareDTO();
                dto.setMaterialId(e.getMaterialId());
                dto.setLength(e.getLength());
                // 理重 * 规格长度 * 复核根数
                BigDecimal b = NumberUtil.mul(NumberUtil.mul(rebarMaterialDO.getWeighValue(),e.getLength()),amountMap.get(dto));
                BigDecimal theoryWeight = NumberUtil.mul(NumberUtil.div(b, a[0],4), finalSumWeight);
                e.setReverseTheoryWeight(theoryWeight);
                e.setReverseTheoryWeightDif(NumberUtil.sub(theoryWeight,e.getSendWeight()));
                e.setReverseTheoryWeightResult(e.getSendWeight().compareTo(theoryWeight) == 0 ? CheckResultEnum.YES.value() : CheckResultEnum.NO.value());
            });

            checkDetailService.updateBatchById(checkDetailDOList);
        }else {
            throw new BizErrorException(BizExceptionMessageEnum.MANAGE_MATERIAL);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void complexUpdate(RebarCheckForm form) {
        // 归档校验
        verifyCheck(form.getId());

        checkDetailService.lambdaUpdate()
                .eq(CheckDetailDO::getCheckId,form.getId())
                .set(BaseDO::getDeleted,1)
                .update();
        List<RebarCheckDetailForm> list = form.getList();
        List<CheckDetailDO> detailDOList = list.stream().map(e -> {
            CheckDetailDO checkDetailDO = new CheckDetailDO();
            BeanUtil.copyProperties(e, checkDetailDO);
            checkDetailDO.setCheckId(form.getId());
            return checkDetailDO;
        }).collect(Collectors.toList());
        BigDecimal sendWeight = detailDOList.stream()
                .map(CheckDetailDO::getSendWeight)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        checkDetailService.saveBatch(detailDOList);

        CheckInfoDO infoDO = checkInfoService.lambdaQuery()
                .eq(CheckInfoDO::getCheckId, form.getId())
                .one();
        CheckInfoDO checkInfoDO = new CheckInfoDO();
        BeanUtil.copyProperties(form,checkInfoDO);
        checkInfoDO.setId(infoDO.getId());
        checkInfoDO.setSendWeight(sendWeight);
        checkInfoDO.setReverseWeightType(null);
        BigDecimal weightDif = NumberUtil.sub(checkInfoDO.getActualWeight(), checkInfoDO.getSendWeight());
        BigDecimal weightRate = NumberUtil.mul(NumberUtil.div(weightDif, sendWeight, 4, RoundingMode.HALF_UP),100);
        checkInfoDO.setWeightCheckResult(checkInfoDO.getSendWeight().compareTo(checkInfoDO.getActualWeight()) == 0 ? CheckResultEnum.YES.value() : CheckResultEnum.NO.value());
        checkInfoDO.setWeightDif(weightDif);
        checkInfoDO.setWeightRate(weightRate);
        checkInfoService.updateById(checkInfoDO);
    }

    @Override
    public void chooseConfirm(Long id,List<RebarCheckDetailForm> list) {
        // 归档校验
        verifyCheck(id);

        List<CheckDetailDO> detailDOList = list.stream().map(e -> {
            CheckDetailDO checkDetailDO = new CheckDetailDO();
            checkDetailDO.setId(e.getId());
            checkDetailDO.setConfirmAmount(e.getConfirmAmount());
            checkDetailDO.setConfirmWeight(e.getConfirmWeight());

            return checkDetailDO;
        }).collect(Collectors.toList());

        checkDetailService.updateBatchById(detailDOList);
    }

    @Override
    public void truckOrPicUpdate(RebarCheckForm form) {
        // 归档校验
        verifyCheck(form.getId());

        CheckDO checkDO = new CheckDO();
        List<String> pic = new ArrayList<>();
        BeanUtil.copyProperties(form,checkDO);
        if (StrUtil.isNotBlank(form.getTruckPic())) {
            pic.addAll(StrUtil.split(form.getTruckPic(),","));
        }
        if (StrUtil.isNotBlank(form.getGoodsPic())){
            pic.addAll(StrUtil.split(form.getGoodsPic(),","));
        }
        if (StrUtil.isNotBlank(form.getSendPic())) {
            pic.addAll(StrUtil.split(form.getSendPic(),","));
        }
        if (CollUtil.isNotEmpty(pic)) {
            fileServiceProxy.confirm(pic);
        }
        this.updateById(checkDO);
    }

    @Override
    public IPage<CheckVO> checkList(CheckQuery query) {
        IPage<CheckVO> page = checkExtMapper.checkList(query);
        if (CollUtil.isNotEmpty(page.getRecords())) {
            List<Long> ids = page.getRecords().stream().map(CheckVO::getId).collect(Collectors.toList());

            List<CheckMaterialVO> materialList = checkExtMapper.checkMaterialList(ids);
            Map<Long, List<CheckMaterialVO>> materialMap = materialList.stream().collect(Collectors.groupingBy(CheckMaterialVO::getCheckId));

            page.getRecords().forEach(e -> {
                List<CheckMaterialVO> checkMaterialVOS = materialMap.get(e.getId());
                e.setList(checkMaterialVOS);
                e.setType(checkMaterialVOS.get(0).getType());
            });
        }
        return page;
    }

    @Override
    public CheckDetailVO checkDetail(Long id) {
        CheckDetailVO vo = new CheckDetailVO();
        CheckReverseVO checkReverseVO = checkReverseVO(id);
        CheckTruckVO checkTruckVO = checkTruckVO(id);
        CheckConfirmVO checkConfirmVO = checkConfirmVO(id);

        vo.setCheckConfirmVO(checkConfirmVO);
        vo.setCheckTruckVO(checkTruckVO);
        vo.setCheckReverseVO(checkReverseVO);
        return vo;
    }

    /**
     * 判断是否已归档
     */
    public void verifyCheck(Long id) {
        CheckDO one = this.lambdaQuery()
                .eq(BaseDO::getId, id)
                .eq(CheckDO::getIsVerify, VerifyEnum.YES.getValue())
                .one();
        if (ObjectUtil.isNotNull(one)) {
            throw new BizErrorException(BizExceptionMessageEnum.HAS_VERIFY);
        }
    }

    private CheckReverseVO checkReverseVO(Long id) {
        CheckReverseVO checkReverseVO = new CheckReverseVO();

        List<CheckMaterialReverseVO> theoryCheckVO = checkDetailExtMapper.theoryCheckVO(id);
        List<CheckMaterialReverseVO> reverseCheckVO = checkDetailExtMapper.reverseCheckVO(id);
        CheckMaterialReverseVO totalCheckVO = checkInfoExtMapper.totalCheckVO(id);

        checkReverseVO.setReverseCheckVO(reverseCheckVO);
        checkReverseVO.setTheoryCheckVO(theoryCheckVO);
        checkReverseVO.setTotalCheckVO(totalCheckVO);

        return checkReverseVO;
    }

    private CheckTruckVO checkTruckVO(Long id) {
        CheckTruckVO vo = new CheckTruckVO();

        CheckDO one = this.lambdaQuery()
                .eq(BaseDO::getId, id)
                .one();
        if (ObjectUtil.isNotNull(one)) {
            BeanUtil.copyProperties(one,vo);

            if (StrUtil.isNotBlank(one.getTruckPic())) {
                Map<String, String> urlByUuids = fileOssService.getUrlMapByUuids(StrUtil.split(one.getTruckPic(), ","));
                vo.setTruckPics(urlByUuids);
            }
            if (StrUtil.isNotBlank(one.getSendPic())) {
                Map<String, String> urlByUuids = fileOssService.getUrlMapByUuids(StrUtil.split(one.getSendPic(), ","));
                vo.setSendPics(urlByUuids);
            }
            if (StrUtil.isNotBlank(one.getGoodsPic())) {
                Map<String, String> urlByUuids = fileOssService.getUrlMapByUuids(StrUtil.split(one.getGoodsPic(), ","));
                vo.setGoodsPics(urlByUuids);
            }
        }

        return vo;
    }

    private CheckConfirmVO checkConfirmVO(Long id) {
        CheckConfirmVO vo = new CheckConfirmVO();

        CheckMaterialReverseVO checkMaterialReverseVO = checkInfoExtMapper.totalCheckVO(id);
        if (ObjectUtil.isNotNull(checkMaterialReverseVO)) {
            BeanUtil.copyProperties(checkMaterialReverseVO,vo);
        }

        List<CheckMaterialConfirmVO> list = checkDetailExtMapper.confirmCheckVO(id);
        vo.setType(list.get(0).getType());
        vo.setList(list);

        return vo;
    }

    private void preCheck(ConsumerVO loginInfo,String phoneSn,Long attributionId) {
        // 服务是否启用
        DeveloperDO developerDO = developerService.lambdaQuery()
                .eq(BaseDO::getCreateId, loginInfo.getUid())
                .eq(DeveloperDO::getAppId, DeveloperAppEnum.REBAR_ACCEPTANCE.value())
                .eq(DeveloperDO::getType, DeveloperTypeEnum.STOP.value())
                .one();
        if (ObjectUtil.isNotNull(developerDO)) {
            throw new BizErrorException(BizExceptionMessageEnum.APP_NOT_CONFIG);
        }

        // 用户是否删除
        UserConsumerDO userConsumerDO = userConsumerService.lambdaQuery()
                .eq(BaseDO::getId, loginInfo.getConsumeId())
                .one();
        if (ObjectUtil.isNull(userConsumerDO)) {
            throw new BizErrorException(BizExceptionMessageEnum.CONSUMER_DELETED);
        }

        // 设备是否属于该用户
        UserConsumerCombineDO userConsumerCombineDO = userConsumerCombineService.lambdaQuery()
                .eq(UserConsumerCombineDO::getUid, loginInfo.getUid())
                .eq(UserConsumerCombineDO::getPhoneSn, phoneSn)
                .eq(UserConsumerCombineDO::getConsumeId, loginInfo.getConsumeId())
                .one();
        if (ObjectUtil.isNull(userConsumerCombineDO)) {
            throw new BizErrorException(BizExceptionMessageEnum.PHONESN_ERROR);
        }else {
            if (userConsumerCombineDO.getIsEnable() == 1) {
                throw new BizErrorException(BizExceptionMessageEnum.PHONGSN_UNABLE);
            }
        }

        // 归属方是否删除
        DeviceAttributionDO deviceAttributionDO = deviceAttributionService.lambdaQuery()
                .eq(BaseDO::getId, attributionId)
                .one();
        if (ObjectUtil.isNull(deviceAttributionDO)) {
            throw new BizErrorException(BizExceptionMessageEnum.ATTRIBUTION_IS_ERROR);
        }

        // 归属方是否过期
        AttributionExtConfigDO one = attributionExtConfigService.lambdaQuery()
                .eq(AttributionExtConfigDO::getAttributionId, attributionId)
                .eq(AttributionExtConfigDO::getAppId, DeveloperAppEnum.REBAR_ACCEPTANCE.value())
                .one();
        if (ObjectUtil.isNull(one) || one.getDateExpire().isBefore(LocalDateTime.now())) {
            throw new BizErrorException(BizExceptionMessageEnum.DATE_ARRIVED);
        }
    }
}
