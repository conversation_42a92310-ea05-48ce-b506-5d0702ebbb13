package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.pinming.microservice.material.client.management.common.enums.OcrTypeEnum;
import cn.pinming.microservice.material.client.management.common.form.ReceiptModuleConfigForm;
import cn.pinming.microservice.material.client.management.common.mapper.ReceiptModuleConfigMapper;
import cn.pinming.microservice.material.client.management.common.model.ReceiptModuleConfigDO;
import cn.pinming.microservice.material.client.management.service.biz.ReceiptModuleConfigService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class ReceiptModuleConfigServiceImpl extends ServiceImpl<ReceiptModuleConfigMapper, ReceiptModuleConfigDO> implements ReceiptModuleConfigService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void receiptModuleConfigSave(ReceiptModuleConfigForm form) {
        ReceiptModuleConfigDO receiptModuleConfigDO = new ReceiptModuleConfigDO();
        BeanUtils.copyProperties(form, receiptModuleConfigDO);
        ReceiptModuleConfigDO one = this.lambdaQuery().eq(ReceiptModuleConfigDO::getModuleId, form.getModuleId()).one();
        if (one != null) {
            receiptModuleConfigDO.setId(one.getId());
        }
        this.saveOrUpdate(receiptModuleConfigDO);
    }

    @Override
    public Integer getOcrType(Long moduleId) {
        ReceiptModuleConfigDO receiptModuleConfigDO = this.lambdaQuery().eq(ReceiptModuleConfigDO::getModuleId, moduleId).one();
        return receiptModuleConfigDO != null && receiptModuleConfigDO.getOcrType() != null ? receiptModuleConfigDO.getOcrType() : OcrTypeEnum.AUTO_WEIGH.getValue();
    }
}

