package cn.pinming.microservice.material.client.management.service.biz;

import cn.pinming.microservice.material.client.management.common.form.AlarmFrameForm;
import cn.pinming.microservice.material.client.management.common.model.CheatAlarmLogDO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 地磅作弊报警日志表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-08
 */
public interface ICheatAlarmLogService extends IService<CheatAlarmLogDO> {

    void saveAlarmFrame(AlarmFrameForm form);
}
