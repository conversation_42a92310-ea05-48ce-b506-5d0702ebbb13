package cn.pinming.microservice.material.client.management.service.openapi;

import cn.pinming.material.v2.model.PageList;
import cn.pinming.material.v2.model.QueryPage;
import cn.pinming.material.v2.model.WeighDataAssemble;
import cn.pinming.material.v2.model.dto.WeighDataDTO;
import cn.pinming.material.v2.model.form.WeighDataAssembleForm;
import cn.pinming.material.v2.model.query.WeighDataQuery;
import cn.pinming.microservice.material.client.management.common.dto.WeighPushDataDTO;
import cn.pinming.microservice.material.client.management.common.dto.WeighPushPicDTO;
import cn.pinming.microservice.material.client.management.common.form.MatchForm;
import cn.pinming.microservice.material.client.management.common.form.PicForm;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 */
public interface WeighDataAssembleService {

    WeighDataAssemble weighDataAssemble(String appKey, WeighDataAssembleForm assembleForm);

    boolean confirm(String appKey, WeighDataAssembleForm assembleForm);

    WeighDataAssemble weighDataQuery(String appKey, WeighDataAssembleForm assembleForm);

    PageList<WeighDataDTO> pageQuery(String appKeyHeader, QueryPage<WeighDataQuery> queryPage);

    WeighPushDataDTO getPushData(String recordId);

    WeighPushPicDTO getPushPic(Long id);

    boolean PushDataConfirm(String recordId);

    boolean PushPicConfirm(Long id);

    String matchModule(String appKeyHeader, MatchForm form);

    String ocr(PicForm form);

    boolean pushReceiptRecycleConfirm(Long id);


    List<String> getPicUrl(PicForm form);
}
