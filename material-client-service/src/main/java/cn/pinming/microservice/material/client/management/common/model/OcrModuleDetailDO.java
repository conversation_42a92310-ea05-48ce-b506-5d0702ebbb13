package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * OCR模版明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("s_ocr_module_detail")
public class OcrModuleDetailDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 数据键父id
     */
    private Long pid;

    /**
     * 模版id
     */
    private Long moduleId;

    /**
     * 数据组id
     */
    private String groupId;

    /**
     * 数据组名称
     */
    private String groupName;

    /**
     * 数据键名称
     */
    private String keyName;

    /**
     * key值关联
     */
    private String valueName;

    /**
     * 模板键值类型（1-字符串 2-数字 3-日期）
     */
    private Byte keyType;

    /**
     * 坐标(左上，右上，右下，左下)
     */
    private String coordinate;

    /**
     * 数据索引
     */
    private String dataIndex;

    /**
     * 锚点类型 1 主锚 2 副锚 3 业务锚点
     **/
    private Byte type;

    /**
     * 剔除内容
     **/
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String deletedContent;
}
