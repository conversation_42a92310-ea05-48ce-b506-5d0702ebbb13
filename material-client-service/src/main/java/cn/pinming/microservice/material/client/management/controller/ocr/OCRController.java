package cn.pinming.microservice.material.client.management.controller.ocr;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.microservice.material.client.management.common.BaseDO;
import cn.pinming.microservice.material.client.management.common.form.EmailLoginForm;
import cn.pinming.microservice.material.client.management.common.form.OCRModuleForm;
import cn.pinming.microservice.material.client.management.common.model.OcrModuleDO;
import cn.pinming.microservice.material.client.management.common.model.OcrResultDO;
import cn.pinming.microservice.material.client.management.common.query.OCRModuleQuery;
import cn.pinming.microservice.material.client.management.common.vo.OCRModuleVO;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizErrorException;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizExceptionMessageEnum;
import cn.pinming.microservice.material.client.management.infrastructure.response.SingleResponse;
import cn.pinming.microservice.material.client.management.service.biz.OCRModuleService;
import cn.pinming.microservice.material.client.management.service.biz.OCRResultService;
import cn.pinming.microservice.material.client.management.service.biz.UserExtConfigService;
import cn.pinming.microservice.material.client.management.service.biz.UserService;
import cn.pinming.springboot.starter.redis.util.RedisUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Api(value = "OCR-controller", tags = {"OCR"})
@RestController
@RequestMapping("/api/OCR")
public class OCRController {
    @Resource
    private OCRModuleService ocrModuleService;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private OCRResultService ocrResultService;
    @Resource
    private UserExtConfigService userExtConfigService;

    @ApiOperation(value = "新增模版", responseReference = "SingleResponse«Long»", nickname = "addOCRModule")
    @PostMapping("/add/module")
    public SingleResponse<Long> addOCRModule(@RequestBody OCRModuleForm form) {
        Long moduleId = ocrModuleService.addOCRModule(form);
        return SingleResponse.of(moduleId);
    }

    @ApiOperation(value = "编辑模版", responseReference = "SingleResponse«Boolean»", nickname = "updateOCRModule")
    @PostMapping("/update/module")
    public SingleResponse<Boolean> updateOCRModule(@RequestBody OCRModuleForm form) {
        ocrModuleService.updateOCRModule(form);
        return SingleResponse.of(true);
    }

    @ApiOperation(value = "启用禁用", responseReference = "SingleResponse«Boolean»", nickname = "enableOCRModule")
    @GetMapping("/enable/module/{moduleId}/{isEnable}")
    public SingleResponse<Boolean> enableOCRModule(@PathVariable("moduleId") Long moduleId, @PathVariable("isEnable") Byte isEnable) {
        ocrModuleService.lambdaUpdate()
                .eq(BaseDO::getId, moduleId)
                .set(OcrModuleDO::getIsEnable, isEnable)
                .update();
        return SingleResponse.of(true);
    }

    @ApiOperation(value = "删除模版", responseReference = "SingleResponse«Boolean»", nickname = "deleteOCRModule")
    @PostMapping("/delete/module/{moduleId}")
    public SingleResponse<Boolean> deleteOCRModule(@PathVariable("moduleId") Long moduleId, @RequestBody EmailLoginForm loginForm) {
        String emailCacheKey = UserService.EMAIL_DELETE_MODULE_REDIS_PREFIX + loginForm.getEmail();
        String emailCaptcha = loginForm.getEmailCaptcha();
        String emailCaptchaCache = (String) redisUtil.get(emailCacheKey);
        if (StringUtils.isBlank(emailCaptchaCache)) {
            throw new BizErrorException(BizExceptionMessageEnum.EMAIL_CAPTCHA_INVALID_ERROR);
        }
        if (StringUtils.isBlank(emailCaptcha) || !emailCaptcha.equals(emailCaptchaCache)) {
            throw new BizErrorException(BizExceptionMessageEnum.EMAIL_CAPTCHA_ERROR);
        }
        // 移除邮箱验证码缓存
        redisUtil.del(emailCacheKey);
        ocrModuleService.lambdaUpdate()
                .eq(BaseDO::getId, moduleId)
                .set(BaseDO::getDeleted, 1)
                .update();
        return SingleResponse.of(true);
    }

    @ApiOperation(value = "刷新引用", responseReference = "SingleResponse«Boolean»", nickname = "refreshOCRModule")
    @GetMapping("/refresh/module/{moduleId}")
    public SingleResponse<Boolean> refreshOCRModule(@PathVariable("moduleId") Long moduleId) {
        ocrModuleService.refreshOCRModule(moduleId);
        return SingleResponse.of(true);
    }

    @ApiOperation(value = "关联归属方", responseReference = "SingleResponse«Boolean»", nickname = "bindOCRModule")
    @GetMapping("/bind/module/{moduleId}")
    public SingleResponse<Boolean> bindOCRModule(@PathVariable("moduleId") Long moduleId, @RequestParam(value = "attributions", required = false) String attributions) {
        if (StrUtil.isNotBlank(attributions)) {
            ocrModuleService.lambdaUpdate()
                    .eq(BaseDO::getId, moduleId)
                    .set(OcrModuleDO::getAttributionIds, attributions)
                    .update();
        } else {
            ocrModuleService.lambdaUpdate()
                    .eq(BaseDO::getId, moduleId)
                    .set(OcrModuleDO::getAttributionIds, null)
                    .update();
        }

        return SingleResponse.of(true);
    }

    @ApiOperation(value = "模版列表", responseReference = "SingleResponse«List<OCRModuleVO>»", nickname = "listOCRModule")
    @PostMapping("/list/module")
    public SingleResponse<List<OCRModuleVO>> listOCRModule(@RequestBody OCRModuleQuery query) {
        List<OCRModuleVO> list = ocrModuleService.listOCRModule(query);
        return SingleResponse.of(list);
    }

    @ApiOperation(value = "模版详情", responseReference = "SingleResponse«OCRModuleVO»", nickname = "detailOCRModule")
    @GetMapping("/detail/module/{moduleId}")
    public SingleResponse<OCRModuleVO> detailOCRModule(@PathVariable("moduleId") Long moduleId) {
        OCRModuleVO vo = ocrModuleService.detailOCRModule(moduleId);
        return SingleResponse.of(vo);
    }

    @ApiOperation(value = "选择数据结构", responseReference = "SingleResponse«List<OCRModuleVO>»", nickname = "chooseCRModule")
    @PostMapping("/choose/module")
    public SingleResponse<List<OCRModuleVO>> chooseOCRModule(@RequestBody OCRModuleQuery query) {
        List<OCRModuleVO> list = ocrModuleService.chooseOCRModule(query);
        return SingleResponse.of(list);
    }

    @ApiOperation(value = "所有模板信息", responseReference = "SingleResponse«List<OCRModuleVO>»", nickname = "allOCRModule")
    @PostMapping("/all/module")
    public SingleResponse<List<OCRModuleVO>> allOCRModule(@RequestBody OCRModuleQuery query) {
        List<OCRModuleVO> list = ocrModuleService.allOCRModule(query);
        return SingleResponse.of(list);
    }

    @ApiOperation(value = "保存识别结果", responseReference = "SingleResponse«?»", nickname = "saveOCRResult")
    @PostMapping("/save/result")
    public SingleResponse<?> saveOCRResult(@RequestBody OcrResultDO ocrResultDO) {
        OcrResultDO one = ocrResultService.lambdaQuery()
                .eq(OcrResultDO::getModuleId, ocrResultDO.getModuleId())
                .one();
        if (ObjectUtil.isNotNull(one)) {
            ocrResultDO.setId(one.getId());
        }
        ocrResultService.saveOrUpdate(ocrResultDO);
        return SingleResponse.of(SingleResponse.buildSuccess());
    }

    @ApiOperation(value = "获取当前模板匹配类型", responseReference = "SingleResponse«Byte»", nickname = "billMatchType")
    @GetMapping("/billMatchType")
    public SingleResponse<Byte> billMatchType() {
        Byte result = userExtConfigService.getBillMatchType();
        return SingleResponse.of(result);
    }


}
