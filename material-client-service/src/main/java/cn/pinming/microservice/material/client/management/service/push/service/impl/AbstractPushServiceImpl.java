package cn.pinming.microservice.material.client.management.service.push.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.pinming.microservice.material.client.management.common.enums.DeveloperAppEnum;
import cn.pinming.microservice.material.client.management.common.enums.DeveloperTypeEnum;
import cn.pinming.microservice.material.client.management.common.BaseDO;
import cn.pinming.microservice.material.client.management.common.dto.push.PushDTO;
import cn.pinming.microservice.material.client.management.common.dto.push.PushPicDTO;
import cn.pinming.microservice.material.client.management.common.mapper.ext.WeighDataExtMapper;
import cn.pinming.microservice.material.client.management.common.mapper.ext.WeighDataPicExtMapper;
import cn.pinming.microservice.material.client.management.common.model.DeveloperDO;
import cn.pinming.microservice.material.client.management.common.model.DeviceAttributionDO;
import cn.pinming.microservice.material.client.management.common.model.PushConfigurationDO;
import cn.pinming.microservice.material.client.management.common.model.UserBusinessConfigDO;
import cn.pinming.microservice.material.client.management.service.biz.*;
import cn.pinming.microservice.material.client.management.service.push.service.PushService;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractPushServiceImpl implements PushService {
    @Resource
    private WeighDataExtMapper weighDataExtMapper;
    @Resource
    private WeighDataPicExtMapper weighDataPicExtMapper;
    @Resource
    private PushConfigurationService pushConfigurationService;
    @Resource
    private UserBusinessConfigService userBusinessConfigService;
    @Resource
    private DeviceAttributionService deviceAttributionService;
    @Resource
    private DeveloperService developerService;
    @Resource
    private FileOssService fileOssService;
    @Resource
    private UserConfigService userConfigService;

    protected PushConfigurationDO getConfiguration(String serviceName) {
        PushConfigurationDO one = pushConfigurationService.lambdaQuery()
                .eq(PushConfigurationDO::getServiceName, serviceName)
                .one();
        if (ObjUtil.isNull(one)) {
            PushConfigurationDO dto = new PushConfigurationDO();
            dto.setServiceName(serviceName);
            pushConfigurationService.save(dto);
            return dto;
        }
        if (one.getIsEnable() == 1) {
            log.info(serviceName + "已被禁用！");
            return null;
        }

        return one;
    }

    /**
     * 校验归属方是否使用一般推送
     *
     * @param configurationDO
     */
    protected List<String> check(PushConfigurationDO configurationDO) {
        if (StrUtil.isBlank(configurationDO.getUserIds())|| StrUtil.isBlank(configurationDO.getAttributionIds())) {
            return null;
        }
        List<String> uidList = StrUtil.split(configurationDO.getUserIds(), ",");
        // 推送服务设置
        List<DeveloperDO> developerDOList = developerService.lambdaQuery()
                .in(BaseDO::getCreateId, uidList)
                .eq(DeveloperDO::getAppId, DeveloperAppEnum.PUSH.value())
                .eq(DeveloperDO::getType, DeveloperTypeEnum.START.value())
                .list();
        if (CollUtil.isEmpty(developerDOList)) {
            log.info("所有推送服务未启用,uid:" + configurationDO.getUserIds());
            return null;
        }
        uidList = developerDOList.stream().map(BaseDO::getCreateId).collect(Collectors.toList());
        // sdk推送设置
        List<UserBusinessConfigDO> businessConfigDOList = userBusinessConfigService.lambdaQuery()
                .in(UserBusinessConfigDO::getUid, uidList)
                .eq(UserBusinessConfigDO::getPushType, 2)
                .list();
        if (CollUtil.isEmpty(businessConfigDOList)) {
            log.info("一般推送服务未配置,uid:" + JSONUtil.toJsonStr(uidList));
            return null;
        }
        uidList = businessConfigDOList.stream().map(UserBusinessConfigDO::getUid)
                .filter(uid -> userConfigService.getApiRemainingCount(uid) > 0)//过滤剩余次数大于0的租户
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(uidList)) {
            log.info("一般推送服务租户套餐余量不足,uid:" + JSONUtil.toJsonStr(uidList));
            return null;
        }
        // 过滤归属方
        List<DeviceAttributionDO> sdkAttributionDOList = deviceAttributionService.lambdaQuery()
                .in(DeviceAttributionDO::getUid, uidList).list();
        if (CollUtil.isEmpty(sdkAttributionDOList)) {
            log.info("一般推送服务未找到配置归属方");
            return null;
        }
        List<String> sdkAttributionIdList = sdkAttributionDOList.stream().map(e -> String.valueOf(e.getId())).collect(Collectors.toList());
        return StrUtil.split(configurationDO.getAttributionIds(), ",").stream().filter(sdkAttributionIdList::contains).collect(Collectors.toList());
    }

    /**
     * 获取指定租户归属方下的称重数据
     *
     * @param attributionIds 归属方id集合
     * @return list
     */
    protected List<PushDTO> getWeighDatas(List<String> attributionIds) {
        return weighDataExtMapper.getPushDatas(attributionIds);
    }

    /**
     * 获取指定租户归属方下的称重照片
     *
     * @param pushDTOList 称重数据集合
     */
    protected void getWeighPics(List<PushDTO> pushDTOList) {
        List<String> recordIds = pushDTOList.stream().map(PushDTO::getRecordId).collect(Collectors.toList());
        List<PushPicDTO> list = weighDataPicExtMapper.getWeighPics(recordIds);

        if (CollUtil.isNotEmpty(list)) {
            // 获取真实下载地址
            LocalDate currentDate = LocalDate.now();
            LocalDate futureDate = currentDate.plusYears(10);
            Date date = Date.from(futureDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
            List<String> uuids = list.stream().map(PushPicDTO::getFileId).collect(Collectors.toList());
            Map<String, String> downloadPicMap = fileOssService.getUrlByUuidAndTimeToMap(uuids, date, false);
            if (CollUtil.isNotEmpty(downloadPicMap)) {
                list.forEach(e -> {
                    if (StrUtil.isNotBlank(downloadPicMap.get(e.getFileId()))) {
                        e.setDownloadUrl(downloadPicMap.get(e.getFileId()));
                    }
                });
            }

            Map<String, List<PushPicDTO>> picMap = list.stream().collect(Collectors.groupingBy(PushPicDTO::getRecordId));
            pushDTOList.forEach(e -> {
                if (CollUtil.isNotEmpty(picMap.get(e.getRecordId()))) {
                    e.setList(picMap.get(e.getRecordId()));
                }
            });
        }
    }
}
