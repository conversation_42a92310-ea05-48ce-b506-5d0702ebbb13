package cn.pinming.microservice.material.client.management.service.biz;

import cn.pinming.microservice.material.client.management.common.form.AttributionAmountForm;
import cn.pinming.microservice.material.client.management.common.form.UserInvocationForm;
import cn.pinming.microservice.material.client.management.common.form.UserSpaceForm;
import cn.pinming.microservice.material.client.management.common.model.UserConfigDO;
import cn.pinming.microservice.material.client.management.common.query.UserQuery;
import cn.pinming.microservice.material.client.management.common.vo.UserSpaceVO;
import cn.pinming.microservice.material.client.management.common.vo.UserSubscribeInfoVO;
import cn.pinming.microservice.material.client.management.common.vo.UserSubscribeVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2023/8/7 16:07
 */
public interface UserConfigService extends IService<UserConfigDO> {
    IPage<UserSubscribeVO> subscribeList(UserQuery query);

    UserSpaceVO queryUserSpace(Long id);

    void editUserStorageConfig(UserSpaceForm form);

    void editUserInvocationConfig(UserInvocationForm form);

    UserSubscribeInfoVO queryUserSubscribeInfo(String uid);

    void updateAttributionAmount(AttributionAmountForm form);

    /**
     * 获取api剩余调用次数，实时从Redis获取，不存在则从数据库统计
     */
    Long getApiRemainingCount(String uid);

    /**
     * 获取单据匹配剩余调用次数
     */
    Long getReceiptApiRemainingCount(String uid);
}
