package cn.pinming.microservice.material.client.management.common.dto.push;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 基石转发订单数据体给第三方json结构体-订单明细信息
 */
@Data
public class PushPurchaseMaterialDTO {
    @ApiModelProperty(value = "货物品牌，可为空")
    private String cargoBrand;

    @ApiModelProperty(value = "来源业务系统中的货物唯一ID，可以为空，但强烈建议设置")
    private String cargoId;

    @ApiModelProperty(value = "货物规格，不能为空")
    private String cargoModel;

    @ApiModelProperty(value = "货物名称，不能为空")
    private String cargoName;

    @ApiModelProperty(value = "货物其它参数，可为空")
    private String cargoParameter;

    @ApiModelProperty(value = "货物将被使用去处的名称，可以为空，但是强烈建议设置")
    private String cargoUsage;

    @ApiModelProperty(value = "货物将被使用去处的ID，比如WBS元素的ID；可以为空，但强烈建议设置")
    private String cargoUsageId;

    @ApiModelProperty(value = "货物备注，可为空")
    private String remark;

    @ApiModelProperty(value = "订单采购数量，不能为空，数字类型")
    private BigDecimal waybillCounts;

    @ApiModelProperty(value = "订单采购数量单位")
    private String waybillUnit;

    @ApiModelProperty(value = "称重计算用到的参数")
    private PushPurchaseCalDTO calParameters;

    @ApiModelProperty(value = "基石货物明细")
    private PushPurchaseMaterialJSDTO jsInfo;
}
