package cn.pinming.microservice.material.client.management.service.biz;

import cn.pinming.microservice.material.client.management.common.form.AttributionExtConfigForm;
import cn.pinming.microservice.material.client.management.common.model.AttributionExtConfigDO;
import cn.pinming.microservice.material.client.management.common.vo.AttributionExtConfigVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface AttributionExtConfigService extends IService<AttributionExtConfigDO> {
    void updateAttributionExtConfig(AttributionExtConfigForm form);

    List<AttributionExtConfigVO> showAttributionExtConfig(String uid,Long appId);
}
