package cn.pinming.microservice.material.client.management.common.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ReceiptRecycleModuleVO implements Serializable {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("单据回收id")
    private Long receiptRecycleId;

    @ApiModelProperty("模版id")
    private Long moduleId;

    @ApiModelProperty("模板明细id")
    private Long ocrModuleDetailId;

    @ApiModelProperty("数据组名称")
    private String groupName;

    @ApiModelProperty("模板键值")
    private String keyName;

    @ApiModelProperty("模板键值类型（1-字符串 2-数字 3-日期）")
    private Byte keyType;

    @ApiModelProperty("模板键值")
    private String keyValue;

    @ApiModelProperty("是否有效(0-否、1-是)")
    private Integer isEffective;

    @ApiModelProperty("是否必须回收(false-否、true-是)")
    private Boolean isMustRecycle;

}
