package cn.pinming.microservice.material.client.management.infrastructure.aop;

import cn.pinming.microservice.material.client.management.infrastructure.annotation.RoleAuthority;
import cn.pinming.microservice.material.client.management.common.enums.RoleEnum;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizErrorException;
import cn.pinming.microservice.material.client.management.common.vo.UserVO;
import cn.pinming.microservice.material.client.management.service.biz.UserService;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static cn.pinming.microservice.material.client.management.infrastructure.exception.BizExceptionMessageEnum.NOT_ADMIN_ERROR;


@Aspect
@Component
public class RoleAuthorityAspect {

    @Resource
    private UserService userService;

    @Pointcut(value = "@annotation(cn.pinming.microservice.material.client.management.infrastructure.annotation.RoleAuthority)")
    private void pointcut() {
    }

    @Before(value = "pointcut() && @annotation(roleAuthority)")
    public void warehouseAuthorityFilter(RoleAuthority roleAuthority) throws RuntimeException {
        UserVO userVO = userService.currentUser();
        if (roleAuthority.value() == RoleEnum.ADMIN && userVO.getIsAdmin() != 0) {
            throw new BizErrorException(NOT_ADMIN_ERROR);
        }
    }

}
