package cn.pinming.microservice.material.client.management.common.form;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class PurchaseItemForm {

    @NotBlank(message = "外部明细业务ID为空")
    private String extId;

    /**
     * 货物名称
     */
    @NotBlank(message = "货物名称为空")
    private String name;

    /**
     * 规格型号
     */
    @NotBlank(message = "规格型号为空")
    private String spec;

    /**
     * 品牌
     */
//    @NotBlank(message = "品牌为空")
    private String brand;

    /**
     * 采购数量
     */
    @NotNull(message = "采购数量为空")
    @DecimalMin(value = "0", message = "采购数量须大于0", inclusive = false)
    private BigDecimal amount;

    /**
     * 采购单位
     */
    @NotBlank(message = "采购单位为空")
    private String unit;

    @NotNull(message = "扣杂率为空")
    @DecimalMin(value = "0", message = "扣杂率须大于0", inclusive = true)
    private BigDecimal deductRatio;

    @NotNull(message = "称重换算系数为空")
    private BigDecimal scaleFactor;

    @NotNull(message = "参照的重量单位为空  参照的重量单位：0 = 千克；1 = 吨")
    private Integer unitType;

    /**
     * 其他参数
     */
    private String argument;

    /**
     * 备注
     */
    @Length(max = 200, message = "备注字数超过200")
    private String remark;

    private String position;

}
