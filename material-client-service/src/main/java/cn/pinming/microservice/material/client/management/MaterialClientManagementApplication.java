package cn.pinming.microservice.material.client.management;

import cn.pinming.core.actuator.probe.ProbeServlet;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.net.Inet4Address;
import java.net.InetAddress;

@Slf4j
@EnableScheduling
@SpringBootApplication(scanBasePackages = {"cn.pinming.microservice.material.client.management", "cn.pinming.springboot.starter"})
@MapperScan(basePackages = {"cn.pinming.microservice.material.client.management.common.mapper"})
@ServletComponentScan(basePackageClasses = ProbeServlet.class)
public class MaterialClientManagementApplication implements CommandLineRunner {

    @Value("${server.port}")
    private int port;

    public static void main(String[] args) {
        SpringApplication.run(MaterialClientManagementApplication.class, args);
    }

    @Override
    public void run(String... args) throws Exception {
        InetAddress localHost = Inet4Address.getLocalHost();
        log.info("swagger API 文档地址：" + localHost.getHostAddress() + ":" + port + "/swagger-ui.html");
    }

}

