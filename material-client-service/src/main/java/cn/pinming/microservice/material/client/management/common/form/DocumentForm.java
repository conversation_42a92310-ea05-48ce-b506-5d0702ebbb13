package cn.pinming.microservice.material.client.management.common.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DocumentForm {

    @ApiModelProperty("文件uuid")
    private String uuid;

    @ApiModelProperty("文件类型(PDF: PDF, 压缩包: PACKAGE, 前端文档: FRONTEND, 后端文档: BACKEND)")
    private String type;

    @ApiModelProperty("文件名称")
    private String name;

    @ApiModelProperty("文件说明")
    private String desc;

}
