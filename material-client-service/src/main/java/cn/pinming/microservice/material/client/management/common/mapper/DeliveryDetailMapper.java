package cn.pinming.microservice.material.client.management.common.mapper;

import cn.pinming.microservice.material.client.management.common.dto.DeliveryDetailSimpleDTO;
import cn.pinming.microservice.material.client.management.common.dto.push.PushConfirmMaterialDTO;
import cn.pinming.microservice.material.client.management.common.model.DeliveryDetailDO;
import cn.pinming.microservice.material.client.management.common.vo.delivery.DeliveryItemDTO;
import cn.pinming.microservice.material.client.management.common.vo.selfcheck.CargoVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 发货单(运单)明细 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-22
 */
public interface DeliveryDetailMapper extends BaseMapper<DeliveryDetailDO> {

    List<DeliveryDetailSimpleDTO> selectMaterialByIds(@Param("list") Set<String> deliveryDetailIds);

    List<DeliveryItemDTO> selectListByDeliveryId(Long deliveryId);

    List<CargoVO> selectCargoListByDeliveryId(@Param("list") List<Long> ids);

    List<PushConfirmMaterialDTO> selectInfoForConfirmPush(@Param("list") List<String> deliveryDetailIdList);
}
