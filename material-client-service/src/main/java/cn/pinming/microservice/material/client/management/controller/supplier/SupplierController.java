package cn.pinming.microservice.material.client.management.controller.supplier;

import cn.pinming.microservice.material.client.management.infrastructure.response.SingleResponse;
import cn.pinming.microservice.material.client.management.common.form.supplier.SupplierConfigForm;
import cn.pinming.microservice.material.client.management.common.form.supplier.SupplierConfigPushForm;
import cn.pinming.microservice.material.client.management.common.vo.SupplierConfigVO;
import cn.pinming.microservice.material.client.management.service.biz.SupplierConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(value = "供应商关联管理", tags = {"supplier"})
@RestController
@RequestMapping("/api/supplier/config")
public class SupplierController {

    @Resource
    private SupplierConfigService supplierConfigService;

    @ApiOperation(value = "关联列表", responseReference = "SingleResponse«List<SupplierConfigVO>»", nickname = "supplierConfigList")
    @GetMapping("/list")
    public SingleResponse<List<SupplierConfigVO>> list() {
        List<SupplierConfigVO> list = supplierConfigService.listByUID();
        return SingleResponse.of(list);
    }

    @ApiOperation(value = "保存", responseReference = "SingleResponse«?»", nickname = "supplierConfigSave")
    @PostMapping("/save")
    public SingleResponse<?> save(@Validated @RequestBody SupplierConfigForm form) {
        supplierConfigService.saveConfig(form);
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "删除", responseReference = "SingleResponse«?»", nickname = "supplierConfigDelete")
    @DeleteMapping("/{id}")
    public SingleResponse<?> delete(@PathVariable Long id) {
        supplierConfigService.deleteById(id);
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "推送配置编辑", responseReference = "SingleResponse«?»", nickname = "supplierPushConfig")
    @PostMapping("/pushConfig")
    public SingleResponse<?> config(@Validated @RequestBody SupplierConfigPushForm form) {
        supplierConfigService.savePushConfig(form);
        return SingleResponse.buildSuccess();
    }


}
