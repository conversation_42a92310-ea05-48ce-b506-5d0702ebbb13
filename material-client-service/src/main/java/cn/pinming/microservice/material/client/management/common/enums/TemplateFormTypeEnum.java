package cn.pinming.microservice.material.client.management.common.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
public enum TemplateFormTypeEnum {
    ONE((byte) 0, "进场小票"),
    TWO((byte) 1, "离场小票"),
    THREE((byte) 2, "完整确认单小票"),
    FOUR((byte) 3, "简单称重小票"),
    ;

    public static final Map<Byte, String> KEY_MAP = Arrays.stream(values()).collect(Collectors.toMap(TemplateFormTypeEnum::getVal, TemplateFormTypeEnum::getDesc));

    public static final Map<String, Byte> VAL_MAP = Arrays.stream(values()).collect(Collectors.toMap(TemplateFormTypeEnum::getDesc, TemplateFormTypeEnum::getVal));
    /**
     * 状态值
     */
    private final Byte val;
    /**
     * 状态的描述
     */
    private final String desc;

    TemplateFormTypeEnum(Byte val, String desc) {
        this.val = val;
        this.desc = desc;
    }

}
