package cn.pinming.microservice.material.client.management.common.enums;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

public enum DeveloperAppEnum {
    ASSEMBLE(1L, "数据重组"),
    UPLOAD(2L, "数据上传"),
    QUERY(3L, "数据查询"),
    PUSH(4L, "数据推送"),
    OCR(5L, "单据识别"),
    RECYCLE(6L, "单据回收"),
    REBAR_ACCEPTANCE(7L, "钢筋验收小助手"),
    RECYCLE_PUSH(8L, "称重类单据回收结果推送"),
    CONFIRM(9L, "司机自助确认设备服务"),
    PURCHASE_SYNC(10L, "订单接收服务"),
    DELIVERY_SYNC(11L, "运单接受服务"),
    BAIDU_OCR(12L, "百度OCR服务"),
    ;

    private final Long type;
    private final String description;

    DeveloperAppEnum(Long type, String description) {
        this.type = type;
        this.description = description;
    }

    public Long value() {
        return type;
    }

    public String description() {
        return description;
    }

    /**
     * 称重数据服务组合ID
     */
    public static List<Long> weighDataService() {
        return Arrays.asList(ASSEMBLE.value(), UPLOAD.value(), QUERY.value(), PUSH.value());
    }

    public static DeveloperAppEnum getEnum(Long type) {
        return Arrays.stream(DeveloperAppEnum.values()).filter(e -> Objects.equals(e.type, type)).findFirst().orElse(null);
    }

    public static String descByType(Long type) {
        for (DeveloperAppEnum value : DeveloperAppEnum.values()) {
            if (Objects.equals(value.type, type)) {
                return value.description;
            }
        }
        return "";
    }
}
