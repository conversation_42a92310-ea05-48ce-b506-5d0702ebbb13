package cn.pinming.microservice.material.client.management.common.query;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2024/8/22 10:46
 */
@Data
public class PurchasePageQuery extends BasePageQuery {

    @ApiModelProperty(value = "业务系统订单id")
    private String orderExtId;

    @ApiModelProperty(value = "采购类目")
    private String materialCategory;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "账户名称")
    private String accountName;

    @ApiModelProperty(value = "采购方")
    private String project;

    @ApiModelProperty(value = "采购单状态 1 启用 2 弃用")
    private List<Integer> status;

    @ApiModelProperty(value = "发货状态 1 待发货 2 发货中 3 发货完毕")
    private List<Integer> deliveryStatus;

}
