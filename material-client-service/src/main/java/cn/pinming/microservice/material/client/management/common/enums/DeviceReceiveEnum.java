package cn.pinming.microservice.material.client.management.common.enums;

public enum DeviceReceiveEnum {
    START((byte) 0, "开始接收"),
    STOP((byte) 1, "暂停接收");

    private byte type;
    private String description;

    DeviceReceiveEnum(byte type, String description) {
        this.type = type;
        this.description = description;
    }

    public byte value() {
        return type;
    }

    public String description() {
        return description;
    }
}
