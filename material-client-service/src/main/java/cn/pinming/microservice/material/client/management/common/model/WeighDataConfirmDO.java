package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 现场称重确认单
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("d_weigh_data_confirm")
public class WeighDataConfirmDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 确认单本地id
     */
    private String localId;

    /**
     * 发货单id
     */
    private Long deliveryId;

    /**
     * 订单id
     */
    private Long purchaseOrderId;

    /**
     * 归属方id
     */
    private Long attributionId;

    /**
     * 确认单号
     */
    private String confirmNo;

    /**
     * 设备机器sn
     */
    private String deviceSn;

    /**
     * 车牌号
     */
    private String truckNo;

    /**
     * 称重类型 1 收料 2 发料
     */
    private Integer weighType;

    /**
     * 运单明细(英文逗号分割)
     */
    private String deliveryDetailIds;

    /**
     * 毛重
     */
    private BigDecimal weightGross;

    /**
     * 皮重
     */
    private BigDecimal weightTare;

    /**
     * 扣重
     */
    private BigDecimal weightDeduct;

    /**
     * 净重
     */
    private BigDecimal weightNet;

    /**
     * 含水率
     */
    private BigDecimal moistureContent;

    /**
     * 实重
     */
    private BigDecimal weightActual;

    /**
     * 换算系数
     */
    private BigDecimal ratio;

    /**
     * 实际数量：实重 / 换算系数
     */
    private BigDecimal actualCount;

    /**
     * 结算单位
     */
    private String weightUnit;

    /**
     * 面单应收量：发货数量
     */
    private BigDecimal weightSend;

    /**
     * 偏差量
     */
    private BigDecimal deviationCount;

    /**
     * 偏差率
     */
    private BigDecimal deviationRate;

    /**
     * 进场时间
     */
    private LocalDateTime enterTime;

    /**
     * 出场时间
     */
    private LocalDateTime leaveTime;

    /**
     * 确认单本地创建时间
     */
    private LocalDateTime localCreateTime;

    /**
     * 确认单本地修改时间
     */
    private LocalDateTime localModifyTime;

    /**
     * 1 未推送
2 已推送
3 推送失败
     */
    private Integer pushState;

    /**
     * 单据照片
     */
    private String documentPic;

    /**
     * 签名照片
     */
    private String signPic;

    /**
     * 签名人照片
     */
    private String signerPic;

    /**
     * 签名时间
     */
    private LocalDateTime signatureTime;

    /**
     * 确认单打印次数
     */
    private Integer printCount;

    /**
     * 称重数据id1
     */
    @TableField(value = "record_id_1")
    private String recordId1;

    /**
     * 称重数据id2
     */
    @TableField(value = "record_id_2")
    private String recordId2;

    /**
     * 源数据
     */
    private String originJson;

    /**
     * 是否确认 0：未确认 1：已确认
     */
    private Integer isConfirmed;
}
