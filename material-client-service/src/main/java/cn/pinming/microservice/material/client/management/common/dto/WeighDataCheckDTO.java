package cn.pinming.microservice.material.client.management.common.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class WeighDataCheckDTO {

    /**
     * 设备id
     */
    private Long deviceId;
    private Long deviceBindingId;
    /**
     * 服务使用状态 0 使用中 1 停用中
     */
    private Byte type;
    /**
     * 设备是否接收 0 开始接收 1 暂停接收
     */
    private Byte receive;
    /**
     * 归属方id
     */
    private Long attributionId;

    /**
     * 外部辅助码
     */
    private String auxiliaryCode;

    /**
     * 设备开启状态 1 启用 2 禁用
     */
    private Byte isUsed;

    /**
     * 用户id
     */
    private String uid;

    /**
     * 已用订阅存储空间(G)
     */
    private BigDecimal spaceUseSize;

    /**
     * 购买空间， 单位GB
     */
    private BigDecimal spaceSize;

    /**
     * 购买空间到期时间
     */
    private LocalDateTime spaceExpire;

    /**
     * 已购买服务调用总次数
     */
    private Long apiTotal;

    /**
     * 已调用次数
     */
    private Long apiUseTotal;
}
