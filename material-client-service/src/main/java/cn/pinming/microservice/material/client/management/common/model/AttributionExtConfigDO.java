package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 用户额外服务配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("s_attribution_ext_config")
public class AttributionExtConfigDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 用户id
     */
    private String uid;

    /**
     * 归属方主键id
     */
    private Long attributionId;

    /**
     * appId
     */
    private Long appId;

    /**
     * 订阅到期时间
     */
    private LocalDateTime dateExpire;


}
