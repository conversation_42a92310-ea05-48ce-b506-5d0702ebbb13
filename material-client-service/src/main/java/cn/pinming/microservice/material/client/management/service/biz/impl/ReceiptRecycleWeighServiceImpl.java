package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.hutool.core.collection.CollUtil;
import cn.pinming.microservice.material.client.management.common.enums.IsEffectiveEnum;
import cn.pinming.microservice.material.client.management.common.enums.WeighDataUnitEnum;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizErrorException;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizExceptionMessageEnum;
import cn.pinming.microservice.material.client.management.common.model.ReceiptRecycleDO;
import cn.pinming.microservice.material.client.management.common.model.ReceiptRecycleWeighDO;
import cn.pinming.microservice.material.client.management.common.mapper.ReceiptRecycleWeighMapper;
import cn.pinming.microservice.material.client.management.common.model.WeighDataDO;
import cn.pinming.microservice.material.client.management.common.model.WeighDataPicDO;
import cn.pinming.microservice.material.client.management.service.biz.ReceiptRecycleWeighService;
import cn.pinming.microservice.material.client.management.service.biz.WeighDataPicService;
import cn.pinming.microservice.material.client.management.service.biz.WeighDataService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 单据回收称重表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-03
 */
@Service
public class ReceiptRecycleWeighServiceImpl extends ServiceImpl<ReceiptRecycleWeighMapper, ReceiptRecycleWeighDO> implements ReceiptRecycleWeighService {
    @Resource
    private WeighDataPicService weighDataPicService;
    @Resource
    private WeighDataService weighDataService;

    @Override
    public void updateCompleteWeigh(List<String> recordIdList, ReceiptRecycleDO receiptRecycleDO) {
        //更新称重数据
        List<WeighDataDO> weighDataDOList = weighDataService.lambdaQuery().in(WeighDataDO::getRecordId, recordIdList)
                .eq(WeighDataDO::getUid, receiptRecycleDO.getUid())
                .eq(WeighDataDO::getAttributionId, receiptRecycleDO.getAttributionId())
                .orderByDesc(WeighDataDO::getWeight)
                .list();
        if (CollUtil.isEmpty(weighDataDOList) || weighDataDOList.size() != recordIdList.size()) {
            throw new BizErrorException(BizExceptionMessageEnum.WEIDATE_IS_NO_EXIST);
        }
        //查询称重图片
        List<WeighDataPicDO> weighDataPicDOList = weighDataPicService.lambdaQuery().in(WeighDataPicDO::getRecordId, recordIdList)
                .eq(WeighDataPicDO::getUid, receiptRecycleDO.getUid())
                .eq(WeighDataPicDO::getAttributionId, receiptRecycleDO.getAttributionId())
                .list();
        Map<String, String> weighDataPicDOMap = new HashMap<>();
        if (CollUtil.isNotEmpty(weighDataPicDOList)) {
            weighDataPicDOMap.putAll(weighDataPicDOList.stream().collect(Collectors.groupingBy(WeighDataPicDO::getRecordId, Collectors.mapping(WeighDataPicDO::getFileId, Collectors.joining(",")))));
        }
        //更新称重数据
        for (WeighDataDO weighDataDO : weighDataDOList) {
            ReceiptRecycleWeighDO receiptRecycleWeighDO = new ReceiptRecycleWeighDO();
            BeanUtils.copyProperties(weighDataDO, receiptRecycleWeighDO);
            receiptRecycleWeighDO.setWeighDataId(weighDataDO.getId());
            receiptRecycleWeighDO.setWeight(WeighDataUnitEnum.convertTon(weighDataDO.getWeight(), weighDataDO.getUnit()));
            receiptRecycleWeighDO.setUnit(WeighDataUnitEnum.TON.value());
            receiptRecycleWeighDO.setFileIds(weighDataPicDOMap.get(weighDataDO.getRecordId()));
            receiptRecycleWeighDO.setIsEffective(IsEffectiveEnum.YES.getValue());
            receiptRecycleWeighDO.setId(null);
            this.lambdaUpdate().eq(ReceiptRecycleWeighDO::getRecordId, weighDataDO.getRecordId())
                    .eq(ReceiptRecycleWeighDO::getReceiptRecycleId, receiptRecycleDO.getId())
                    .eq(ReceiptRecycleWeighDO::getIsEffective, IsEffectiveEnum.NO.getValue())
                    .update(receiptRecycleWeighDO);
        }
    }

}
