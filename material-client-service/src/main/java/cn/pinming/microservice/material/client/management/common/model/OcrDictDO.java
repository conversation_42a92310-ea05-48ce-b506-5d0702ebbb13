package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * OCR模版字典表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("s_ocr_dict")
public class OcrDictDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 字典名
     */
    private String name;

    /**
     * 字典值
     */
    private String value;


}
