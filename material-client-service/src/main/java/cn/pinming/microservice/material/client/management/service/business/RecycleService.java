package cn.pinming.microservice.material.client.management.service.business;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.pinming.microservice.material.client.management.common.dto.OCRConvertDTO;
import cn.pinming.microservice.material.client.management.common.dto.OCRHtmlDTO;
import cn.pinming.microservice.material.client.management.common.enums.*;
import cn.pinming.microservice.material.client.management.common.form.ReceiptRecycleUpdateForm;
import cn.pinming.microservice.material.client.management.common.model.*;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizErrorException;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizExceptionMessageEnum;
import cn.pinming.microservice.material.client.management.infrastructure.util.CheckUtil;
import cn.pinming.microservice.material.client.management.service.biz.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class RecycleService {
    @Resource
    private ReceiptRecycleService receiptRecycleService;
    @Resource
    private OcrService ocrService;
    @Resource
    private OCRModuleService ocrModuleService;
    @Resource
    private OcrModuleDetailService ocrModuleDetailService;
    @Resource
    private DeveloperService developerService;
    @Resource
    private DeviceAttributionService deviceAttributionService;
    @Resource
    private ReceiptRecycleModuleService receiptRecycleModuleService;
    @Resource
    private ReceiptRecycleWeighService receiptRecycleWeighService;
    @Resource
    private ReceiptModuleConfigService receiptModuleConfigService;
    @Resource
    private CombineService combineService;
    @Resource
    private CheckUtil checkUtil;
    @Resource
    private UserExtConfigService userExtConfigService;


    /**
     * 异步处理单价回收
     */
    @Async("receiptExecutor")
    public void syncProcessRecycle(ReceiptRecycleDO receiptRecycleDO) {
        processRecycle(receiptRecycleDO);
    }

    /**
     * 处理单价回收
     */
    public void processRecycle(ReceiptRecycleDO receiptRecycleDO) {
        if (receiptRecycleDO == null || StrUtil.isEmpty(receiptRecycleDO.getRecyclePic())) {
            return;
        }

        Long id = receiptRecycleDO.getId();
        String uid = receiptRecycleDO.getUid();
        Long attributionId = receiptRecycleDO.getAttributionId();
        String recyclePic = receiptRecycleDO.getRecyclePic();

        boolean recycleStatusFlag = false;
        Set<ReceiptFailTypeEnum> receiptFailTypeEnums = new HashSet<>();
        Set<String> recordIdList = new HashSet<>();

        // 查询符合条件的开发者列表
        if (!areAllRelevantAppsEnabled(uid)) {
            log.error("单据回收app未全部开启，uid ={}", uid);
            return;
        }

        try {
            //检查数据重组使用次数是否足够
            checkUtil.appCheck(uid, DeveloperAppEnum.ASSEMBLE);
            //检查ocr使用次数是否足够
            ocrService.checkOcrService(uid, attributionId);
        } catch (Exception e) {
            log.error("数据重组或单据识别使用次数不足，uid ={}, attributionId={}", uid, attributionId);
            return;
        }

        DeviceAttributionDO deviceAttributionDO = deviceAttributionService.getById(attributionId);
        if (deviceAttributionDO == null) {
            log.error("设备归属方信息未找到，attributionId ={}", attributionId);
            return;
        }

        // 增加处理中状态，防止并发
        if (!updateRecycleStatus(receiptRecycleDO.getId(), RecycleStatusEnum.WAIT, RecycleStatusEnum.PROCESSING)) {
            return;
        }

        // 查询当前租户启用的模板匹配方式
        UserExtConfigDO userExtConfigDO = userExtConfigService.lambdaQuery().eq(UserExtConfigDO::getUid, uid).one();
        Byte billMatchType = userExtConfigDO.getBillMatchType();

        if (billMatchType == null || billMatchType == 0) { // 按坐标区域匹配
            Map<Long, OCRConvertDTO> moduleMap = new HashMap<>();
            for (String uuid : StrUtil.split(recyclePic, StrUtil.COMMA)) {
                try {
                    // 区域ocr识别
                    OCRConvertDTO ocrConvertDTO = ocrService.ocrMatchByUuid(deviceAttributionDO, uuid);
                    // 保存识别数据
                    saveRecycleModuleDO(receiptRecycleDO, ocrConvertDTO, moduleMap, receiptFailTypeEnums);
                    saveRecycleWeighDO(receiptRecycleDO, ocrConvertDTO.getBarcodes(), recordIdList);
                } catch (Exception e) {
                    log.error("单据回收OCR识别异常receiptRecycleId:{}, uuid:{}", id, uuid, e);
                }
            }
            //单据模板匹配
            if (moduleMap.isEmpty()) {
                receiptFailTypeEnums.add(ReceiptFailTypeEnum.UNMATCHED_TEMPLATE);
            } else if (moduleMap.size() == 1) {
                try {
                    Long moduleId = moduleMap.keySet().iterator().next();//只有一个
                    recycleStatusFlag = ocrModuleMatch(moduleId, receiptRecycleDO);
                    if (!recycleStatusFlag) receiptFailTypeEnums.add(ReceiptFailTypeEnum.KEY_FIELD_ERROR);
                } catch (Exception e) {
                    log.error("单据回收匹配异常receiptRecycleId:{}", id);
                }
            } else {
                receiptFailTypeEnums.add(ReceiptFailTypeEnum.MATCH_MULTIPLE);
            }
            //称重数据匹配
            if (receiptRecycleDO.getRecycleSource() != 4 && receiptRecycleDO.getOcrType() != null && receiptRecycleDO.getOcrType().equals(OcrTypeEnum.AUTO_WEIGH.getValue())) {
                if (recordIdList.isEmpty()) {
                    receiptFailTypeEnums.add(ReceiptFailTypeEnum.UNRECOGNIZED_TICKET);
                } else if (recordIdList.size() == 2) {
                    try {
                        recycleStatusFlag = weighDataMatch(new ArrayList<>(recordIdList), receiptRecycleDO);
                        if (!recycleStatusFlag) receiptFailTypeEnums.add(ReceiptFailTypeEnum.DISABLED);
                    } catch (Exception e) {
                        log.error("称重数据匹配异常receiptRecycleId:{}", id);
                    }
                } else {
                    receiptFailTypeEnums.add(ReceiptFailTypeEnum.COUNT_ERROR);
                }
            }
        } else if (billMatchType == 1) {
            Map<Long, OCRHtmlDTO> moduleMap = new HashMap<>();
            // 按索引匹配
            for (String uuid : StrUtil.split(recyclePic, StrUtil.COMMA)) {
                try {
                    OCRHtmlDTO ocrHtmlDTO = ocrService.ocrHtmlMatchByUuid(deviceAttributionDO, uuid);
                    saveRecycleModuleDO(receiptRecycleDO, ocrHtmlDTO, moduleMap, receiptFailTypeEnums);
                    saveRecycleWeighDO(receiptRecycleDO, ocrHtmlDTO.getBarcodes(), recordIdList);
                } catch (Exception e) {
                    log.error("单据回收OCR识别异常receiptRecycleId:{}, uuid:{}", id, uuid, e);
                }
            }
            //单据模板匹配
            if (moduleMap.isEmpty()) {
                receiptFailTypeEnums.add(ReceiptFailTypeEnum.UNMATCHED_TEMPLATE);
            } else if (moduleMap.size() == 1) {
                try {
                    Long moduleId = moduleMap.keySet().iterator().next();//只有一个
                    recycleStatusFlag = ocrModuleMatch(moduleId, receiptRecycleDO);
                    if (!recycleStatusFlag) receiptFailTypeEnums.add(ReceiptFailTypeEnum.KEY_FIELD_ERROR);
                } catch (Exception e) {
                    log.error("单据回收匹配异常receiptRecycleId:{}", id);
                }
            } else {
                receiptFailTypeEnums.add(ReceiptFailTypeEnum.MATCH_MULTIPLE);
            }
            //称重数据匹配
            if (receiptRecycleDO.getRecycleSource() != 4 && receiptRecycleDO.getOcrType() != null && receiptRecycleDO.getOcrType().equals(OcrTypeEnum.AUTO_WEIGH.getValue())) {
                if (recordIdList.isEmpty()) {
                    receiptFailTypeEnums.add(ReceiptFailTypeEnum.UNRECOGNIZED_TICKET);
                } else if (recordIdList.size() == 2) {
                    try {
                        recycleStatusFlag = weighDataMatch(new ArrayList<>(recordIdList), receiptRecycleDO);
                        if (!recycleStatusFlag) receiptFailTypeEnums.add(ReceiptFailTypeEnum.DISABLED);
                    } catch (Exception e) {
                        log.error("称重数据匹配异常receiptRecycleId:{}", id);
                    }
                } else {
                    receiptFailTypeEnums.add(ReceiptFailTypeEnum.COUNT_ERROR);
                }
            }
        }

        if (recycleStatusFlag && receiptFailTypeEnums.isEmpty()) {
            //成功回收记录
            try {
                receiptRecycleService.successRecycle(receiptRecycleDO.getId());
                return;
            } catch (Exception e) {
                log.error("确认回收异常receiptRecycleId:{}", receiptRecycleDO.getId(), e);
            }
        }
        //回收失败
        receiptRecycleService.lambdaUpdate().eq(ReceiptRecycleDO::getId, id)
                .set(CollUtil.isNotEmpty(receiptFailTypeEnums), ReceiptRecycleDO::getReceiptFailTypes, receiptFailTypeEnums.stream().map(t -> t.getValue().toString()).collect(Collectors.joining(",")))
                .set(ReceiptRecycleDO::getRecycleStatus, RecycleStatusEnum.FAIL.getValue()).update();
    }

    private boolean areAllRelevantAppsEnabled(String uid) {
        Integer developerDOList = developerService.lambdaQuery().eq(DeveloperDO::getCreateId, uid)
                .in(DeveloperDO::getAppId, Arrays.asList(DeveloperAppEnum.ASSEMBLE.value(), DeveloperAppEnum.OCR.value(), DeveloperAppEnum.RECYCLE.value()))
                .eq(DeveloperDO::getType, DeveloperTypeEnum.START.value()).count();
        return developerDOList == 3;
    }

    private boolean updateRecycleStatus(Long receiptRecycleId, RecycleStatusEnum currentStatus, RecycleStatusEnum newStatus) {
        return receiptRecycleService.lambdaUpdate().eq(ReceiptRecycleDO::getId, receiptRecycleId)
                .eq(ReceiptRecycleDO::getRecycleStatus, currentStatus.getValue())
                .set(ReceiptRecycleDO::getRecycleStatus, newStatus.getValue())
                .update();
    }

    private void saveRecycleModuleDO(ReceiptRecycleDO receiptRecycleDO, OCRConvertDTO ocrConvertDTO, Map<Long, OCRConvertDTO> moduleMap, Set<ReceiptFailTypeEnum> receiptFailTypeEnums) {
        log.error("ocrConvertDTO:{}, moduleMap:{}", JSONUtil.toJsonStr(ocrConvertDTO), JSONUtil.toJsonStr(moduleMap));
        if (StrUtil.isNotEmpty(ocrConvertDTO.getId()) && CollUtil.isNotEmpty(ocrConvertDTO.getResult())) {
            Long moduleId = Long.valueOf(ocrConvertDTO.getId());
            if (moduleMap.containsKey(moduleId)) {
                return;
            }
            List<OcrModuleDetailDO> ocrModuleDetailDOList = ocrModuleDetailService.lambdaQuery().eq(OcrModuleDetailDO::getModuleId, moduleId)
                    .eq(OcrModuleDetailDO::getType, 3).list();
            if (CollUtil.isEmpty(ocrModuleDetailDOList)) {
                throw new BizErrorException(BizExceptionMessageEnum.OCR_MATCH_EMPTY);
            }
            Map<String, Map<String, String[]>> result = ocrConvertDTO.getResult();
            List<ReceiptRecycleModuleDO> receiptRecycleModuleDOList = new ArrayList<>();
            //循环组装数据
            ocrModuleDetailDOList.forEach(ocrModuleDetailDO -> {
                ReceiptRecycleModuleDO receiptRecycleModuleDO = new ReceiptRecycleModuleDO();
                BeanUtil.copyProperties(ocrModuleDetailDO, receiptRecycleModuleDO);
                receiptRecycleModuleDO.setUid(receiptRecycleDO.getUid());
                receiptRecycleModuleDO.setReceiptRecycleId(receiptRecycleDO.getId());
                receiptRecycleModuleDO.setOcrModuleDetailId(ocrModuleDetailDO.getId());
                receiptRecycleModuleDO.setId(null);

                Map<String, String[]> groupValueMap = result.get(ocrModuleDetailDO.getGroupName());
                if (CollUtil.isNotEmpty(groupValueMap) && groupValueMap.containsKey(ocrModuleDetailDO.getKeyName())) {
                    String[] values = groupValueMap.get(ocrModuleDetailDO.getKeyName());
                    if (values != null && values.length > 0) {
                        if (!OcrKeyTypeEnum.validate(ocrModuleDetailDO.getKeyType(), String.join("", values)) && receiptFailTypeEnums != null) {
                            receiptFailTypeEnums.add(ReceiptFailTypeEnum.CONVERT_ERROR);
                        }
                        String deletedContent = ocrModuleDetailDO.getDeletedContent();

                        receiptRecycleModuleDO.setKeyValue(String.join("", values));
                        if (StrUtil.isNotBlank(receiptRecycleModuleDO.getKeyValue()) && StrUtil.isNotBlank(deletedContent)) {
                            String replace = receiptRecycleModuleDO.getKeyValue();
                            //字符串长度排序倒序 再替换
                            List<String> collect = StrUtil.split(deletedContent, StrUtil.COMMA).stream().sorted(Comparator.comparingInt(String::length).reversed()).collect(Collectors.toList());
                            for (String s : collect) {
                                replace = replace.replace(s, "");
                            }
                            receiptRecycleModuleDO.setKeyValue(replace);
                        }

                    }
                }
                receiptRecycleModuleDO.setIsEffective(IsEffectiveEnum.NO.getValue());
                receiptRecycleModuleDOList.add(receiptRecycleModuleDO);

            });
            receiptRecycleModuleService.saveBatch(receiptRecycleModuleDOList);
            moduleMap.put(moduleId, ocrConvertDTO);
        }
    }

    private void saveRecycleModuleDO(ReceiptRecycleDO receiptRecycleDO, OCRHtmlDTO ocrConvertDTO, Map<Long, OCRHtmlDTO> moduleMap, Set<ReceiptFailTypeEnum> receiptFailTypeEnums) {
        if (StrUtil.isNotEmpty(ocrConvertDTO.getId()) && CollUtil.isNotEmpty(ocrConvertDTO.getResult())) {
            Long moduleId = Long.valueOf(ocrConvertDTO.getId());
            if (moduleMap.containsKey(moduleId)) {
                return;
            }
            List<OcrModuleDetailDO> ocrModuleDetailDOList = ocrModuleDetailService.lambdaQuery().eq(OcrModuleDetailDO::getModuleId, moduleId)
                    .eq(OcrModuleDetailDO::getType, 3).list();
            if (CollUtil.isEmpty(ocrModuleDetailDOList)) {
                throw new BizErrorException(BizExceptionMessageEnum.OCR_MATCH_EMPTY);
            }
            Map<String, Map<String, String>> result = ocrConvertDTO.getResult(); // {'groupName':{'keyName':['value1','value2']}}
            List<ReceiptRecycleModuleDO> receiptRecycleModuleDOList = new ArrayList<>();
            //循环组装数据
            ocrModuleDetailDOList.forEach(ocrModuleDetailDO -> {
                ReceiptRecycleModuleDO receiptRecycleModuleDO = new ReceiptRecycleModuleDO();
                BeanUtil.copyProperties(ocrModuleDetailDO, receiptRecycleModuleDO);
                receiptRecycleModuleDO.setUid(receiptRecycleDO.getUid());
                receiptRecycleModuleDO.setReceiptRecycleId(receiptRecycleDO.getId());
                receiptRecycleModuleDO.setOcrModuleDetailId(ocrModuleDetailDO.getId());
                receiptRecycleModuleDO.setId(null);

                Map<String, String> groupValueMap = result.get(ocrModuleDetailDO.getGroupName());
                if (CollUtil.isNotEmpty(groupValueMap) && groupValueMap.containsKey(ocrModuleDetailDO.getKeyName())) {
                    String value = groupValueMap.get(ocrModuleDetailDO.getKeyName());
                    if (StrUtil.isNotBlank(value)) {
                        if (!OcrKeyTypeEnum.validate(ocrModuleDetailDO.getKeyType(), value) && receiptFailTypeEnums != null) {
                            receiptFailTypeEnums.add(ReceiptFailTypeEnum.CONVERT_ERROR);
                        }
                        receiptRecycleModuleDO.setKeyValue(value);
                    }
                }
                receiptRecycleModuleDO.setIsEffective(IsEffectiveEnum.NO.getValue());
                receiptRecycleModuleDOList.add(receiptRecycleModuleDO);

            });
            receiptRecycleModuleService.saveBatch(receiptRecycleModuleDOList);
            moduleMap.put(moduleId, ocrConvertDTO);
        }
    }

    private void saveRecycleWeighDO(ReceiptRecycleDO receiptRecycleDO, List<String> barcodes, Set<String> recordIdList) {
        if (receiptRecycleDO.getRecycleSource() == 4 || CollUtil.isEmpty(barcodes)) {
            return;
        }
        Set<String> barcodeSet = new HashSet<>(barcodes);
        barcodeSet.removeAll(recordIdList);//删掉已有的
        if (CollUtil.isEmpty(barcodeSet)) {
            return;
        }
        List<ReceiptRecycleWeighDO> receiptRecycleWeighDOList = barcodeSet.stream().map(recordId -> {
            ReceiptRecycleWeighDO receiptRecycleWeighDO = new ReceiptRecycleWeighDO();
            BeanUtil.copyProperties(receiptRecycleDO, receiptRecycleWeighDO);
            receiptRecycleWeighDO.setReceiptRecycleId(receiptRecycleDO.getId());
            receiptRecycleWeighDO.setRecordId(recordId);
            receiptRecycleWeighDO.setIsEffective(IsEffectiveEnum.NO.getValue());
            return receiptRecycleWeighDO;
        }).collect(Collectors.toList());
        ArrayList<ReceiptRecycleWeighDO> collect = receiptRecycleWeighDOList.stream().collect(Collectors.collectingAndThen(
                Collectors.toMap(
                        ReceiptRecycleWeighDO::getRecordId,
                        e -> e,
                        (existing, replacement) -> existing
                ),
                map -> new ArrayList<>(map.values())
        ));
        receiptRecycleWeighService.saveBatch(collect);
        recordIdList.addAll(barcodeSet);
    }

    private boolean weighDataMatch(List<String> recordIdList, ReceiptRecycleDO receiptRecycleDO) {
        try {
            //组装称重数据
            List<WeighDataDO> weighDataDOList = combineService.receiptRecycleAssemble(recordIdList, receiptRecycleDO);
            if (CollUtil.isEmpty(weighDataDOList)) return false;//数据异常，组装失败

            //确认组装称重数据
            ReceiptRecycleUpdateForm receiptRecycleUpdateForm = new ReceiptRecycleUpdateForm();
            receiptRecycleUpdateForm.setId(receiptRecycleDO.getId());
            WeighDataDO weighDataDO1 = weighDataDOList.get(0);
            WeighDataDO weighDataDO2 = weighDataDOList.get(1);
            receiptRecycleUpdateForm.setWeighType(weighDataDO1.getWeight().compareTo(weighDataDO2.getWeight()) > 0 ? RecycleWeighTypeEnum.RECEIVE.getValue() : RecycleWeighTypeEnum.MATERIAL.getValue());
            receiptRecycleUpdateForm.setTruckNo(weighDataDO1.getTruckNo());
            receiptRecycleUpdateForm.setWeightGross(receiptRecycleUpdateForm.getWeighType() == 1 ? weighDataDO1.getWeight() : weighDataDO2.getWeight());
            receiptRecycleUpdateForm.setWeightGrossTime(receiptRecycleUpdateForm.getWeighType() == 1 ? weighDataDO1.getWeighTime() : weighDataDO2.getWeighTime());
            receiptRecycleUpdateForm.setWeightTare(receiptRecycleUpdateForm.getWeighType() == 1 ? weighDataDO2.getWeight() : weighDataDO1.getWeight());
            receiptRecycleUpdateForm.setWeightTareTime(receiptRecycleUpdateForm.getWeighType() == 1 ? weighDataDO2.getWeighTime() : weighDataDO1.getWeighTime());
            receiptRecycleUpdateForm.setUnit(weighDataDO1.getUnit());
            receiptRecycleUpdateForm.setWeightNet(receiptRecycleUpdateForm.getWeightGross().subtract(receiptRecycleUpdateForm.getWeightTare()));
            receiptRecycleUpdateForm.setRecordIdList(recordIdList);
            receiptRecycleService.correctionWeigh(receiptRecycleUpdateForm);
            return true;
        } catch (Exception e) {
            log.error("称重数据验证异常receiptRecycleId:{}", receiptRecycleDO.getId(), e);
            return false;
        }
    }

    /**
     * 组装模板数据
     */
    public void assembleModule(ReceiptRecycleDO receiptRecycleDO) {
        if (receiptRecycleDO == null || StrUtil.isEmpty(receiptRecycleDO.getRecyclePic())) {
            throw new BizErrorException(BizExceptionMessageEnum.OCR_MATCH_EMPTY);
        }
        DeviceAttributionDO deviceAttributionDO = deviceAttributionService.getById(receiptRecycleDO.getAttributionId());
        if (deviceAttributionDO == null) {
            throw new BizErrorException(BizExceptionMessageEnum.ATTRIBUTION_IS_ERROR);
        }
        Map<Long, OCRConvertDTO> moduleMap = new HashMap<>();
        for (String uuid : receiptRecycleDO.getRecyclePic().split(",")) {
            try {
                OCRConvertDTO ocrConvertDTO = ocrService.ocrMatchByUuid(deviceAttributionDO, uuid);
                saveRecycleModuleDO(receiptRecycleDO, ocrConvertDTO, moduleMap, null);
            } catch (Exception e) {
                log.error("单据回收OCR识别异常receiptRecycleId:{}, uuid:{}", receiptRecycleDO.getId(), uuid, e);
            }
        }
        if (moduleMap.size() != 1) {
            return;//超过一个或无模板则直接返回
        }
        Long moduleId = moduleMap.keySet().iterator().next();//只有一个
        //单据模板匹配
        ocrModuleMatch(moduleId, receiptRecycleDO);
    }

    public boolean ocrModuleMatch(Long moduleId, ReceiptRecycleDO receiptRecycleDO) {
        //确定ocr识别类型OcrTypeEnum并更新moduleId
        receiptRecycleDO.setOcrType(receiptModuleConfigService.getOcrType(moduleId));
        receiptRecycleDO.setModuleId(moduleId);
        receiptRecycleService.updateById(receiptRecycleDO);
        //验证并修正模板数据组
        return receiptRecycleModuleService.verifyAndCorrectionModule(receiptRecycleDO, moduleId);
    }


    public void saveChooseModule(Long moduleId, ReceiptRecycleDO receiptRecycleDO) {
        //确定ocr识别类型OcrTypeEnum并更新moduleId
        receiptRecycleDO.setOcrType(receiptModuleConfigService.getOcrType(moduleId));
        receiptRecycleDO.setModuleId(moduleId);
        receiptRecycleService.updateById(receiptRecycleDO);
        //保存模板数据组
        List<OcrModuleDetailDO> ocrModuleDetailDOList = ocrModuleDetailService.lambdaQuery().eq(OcrModuleDetailDO::getModuleId, moduleId)
                .eq(OcrModuleDetailDO::getType, 3).list();
        if (CollUtil.isEmpty(ocrModuleDetailDOList)) {
            throw new BizErrorException(BizExceptionMessageEnum.OCR_MATCH_EMPTY);
        }
        List<ReceiptRecycleModuleDO> receiptRecycleModuleDOList = new ArrayList<>();
        //循环组装数据
        ocrModuleDetailDOList.forEach(ocrModuleDetailDO -> {
            ReceiptRecycleModuleDO receiptRecycleModuleDO = new ReceiptRecycleModuleDO();
            BeanUtil.copyProperties(ocrModuleDetailDO, receiptRecycleModuleDO);
            receiptRecycleModuleDO.setUid(receiptRecycleDO.getUid());
            receiptRecycleModuleDO.setReceiptRecycleId(receiptRecycleDO.getId());
            receiptRecycleModuleDO.setOcrModuleDetailId(ocrModuleDetailDO.getId());
            receiptRecycleModuleDO.setId(null);
            receiptRecycleModuleDO.setIsEffective(IsEffectiveEnum.NO.getValue());
            receiptRecycleModuleDOList.add(receiptRecycleModuleDO);
        });
        receiptRecycleModuleService.saveBatch(receiptRecycleModuleDOList);
    }


    public static void main(String[] args) {
        List<String> collect = StrUtil.split("22222,333,12131,1213", StrUtil.COMMA).stream()
                .sorted(Comparator.comparingInt(String::length).reversed()).collect(Collectors.toList());
        System.out.println(collect);
    }
}
