package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 资源采购表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("s_purchase_log")
public class PurchaseLogDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 租户ID
     */
    private String uid;

    /**
     * 修改类型(1-储存空间、2-称重数据服务、3-单据匹配服务)
     */
    private Integer modifyType;

    /**
     * 修改的订阅存储空间(G)
     */
    private BigDecimal modifySpace;

    /**
     * 购买空间到期时间
     */
    private LocalDateTime spaceExpire;

    /**
     * 修改的调用次数
     */
    private Long modifyApiTotal;

    /**
     * 修改后api总次数
     */
    private Long apiTotal;

    /**
     * 描述
     */
    private String description;


}
