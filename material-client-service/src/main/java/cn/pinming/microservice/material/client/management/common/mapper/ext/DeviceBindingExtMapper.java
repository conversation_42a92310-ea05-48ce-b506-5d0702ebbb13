package cn.pinming.microservice.material.client.management.common.mapper.ext;

import cn.pinming.material.v2.model.dto.DeviceBindingDTO;
import cn.pinming.microservice.material.client.management.common.vo.DeviceAttributionVO;
import cn.pinming.microservice.material.client.management.common.vo.DeviceUserVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DeviceBindingExtMapper {

    String appAttributionCheck(@Param("deviceSn") String deviceSn, @Param("deviceType") String deviceType);

    List<DeviceUserVO> userList(@Param("uId") String uId);

    List<DeviceAttributionVO> attributionList(@Param("attributionId") Long attributionId);

    List<DeviceBindingDTO> getDeviceBindingList(@Param("code") Integer projectId);
}
