package cn.pinming.microservice.material.client.management.infrastructure.util;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2023/10/27 16:06
 */
public class MatchUtil {

    /**
     * @param str1 输入值1
     * @param str2 输入值2
     * @param size 比较的最大长度
     * @return 返回位置、字符相同 且 长度最大 的字符串
     */
    public static String equals(String str1, String str2, int size) {
        String longestMatch = "";
        String currentMatch = "";

        for (int i = 0; i < size; i++) {
            if (str1.charAt(i) == str2.charAt(i)) {
                currentMatch += str1.charAt(i);
            } else {
                if (currentMatch.length() > longestMatch.length()) {
                    longestMatch = currentMatch;
                }
                currentMatch = "";
            }
        }

        if (currentMatch.length() > longestMatch.length()) {
            longestMatch = currentMatch;
        }

        if (longestMatch.length() >= 3) {
            System.out.println("最长连续相同子串为: " + longestMatch);
            return longestMatch;
        }
        return "";
    }

    /**
     * 连续车牌相同
     *
     * @param str1 车牌1
     * @param str2 车牌2
     * @return 返回位置、字符相同 且 长度最大 的字符串
     */
    public static String plateSequenceEquals(String str1, String str2) {
        if (str1.length() != 7 || str2.length() != 7) {
            return "";
        }
        str1 = str1.substring(2);
        str2 = str2.substring(2);
        return equals(str1, str2, 5);
    }

    /**
     * 统计连续相同字符数 不考虑位置
     *
     * @param str1 车牌1
     * @param str2 车牌2
     * @return 连续相同字符数
     */
    public static int countCommonCharacters(String str1, String str2) {
        // 将两个字符串转换为字符数组
        char[] chars1 = str1.toCharArray();
        char[] chars2 = str2.toCharArray();

        // 使用一个集合来存储字符，以便检查重复字符
        Set<Character> charSet = new HashSet<>();

        // 将第一个字符串的字符添加到集合中
        for (char c : chars1) {
            charSet.add(c);
        }
        int commonCount = 0;
        // 检查第二个字符串中的字符是否在集合中存在，如果存在，增加计数
        for (char c : chars2) {
            if (charSet.contains(c)) {
                commonCount++;
                // 防止重复计数
                charSet.remove(c);
            }
        }
        return commonCount;
    }

    public static String getKeyWithMaxValue(Map<String, Integer> map) {
        return map.entrySet()
                .stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse(null);
    }


}
