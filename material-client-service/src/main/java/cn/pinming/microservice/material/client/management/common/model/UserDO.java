package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("s_user")
public class UserDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 用户id
     */
    private String uid;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 用户email
     */
    private String email;

    /**
     * 密码md5加密值
     */
    private String password;

    /**
     * appKey
     */
    private String appKey;

    /**
     * appSecretKey
     */
    private String appSecretKey;

    /**
     * 密码+盐md5加密值
     */
    private String passwordSalt;

    /**
     * 盐值(动态)
     */
    private String salt;

    /**
     * 0 管理员 1 普通用户
     */
    private Byte isAdmin;

//    /**
//     * app销售状态 1 正常使用 2 请续费
//     */
//    private Byte serviceStatus;

    /**
     * 租户logo
     */
    private String logoPic;

}
