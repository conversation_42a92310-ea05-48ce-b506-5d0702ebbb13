package cn.pinming.microservice.material.client.management.common.vo;

import cn.pinming.microservice.material.client.management.common.dto.WeighDataSavedDTO;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class WeighDataCurvePullVO {
    /**
     * 重量列表(与时间点一一对应)
     */
    List<Double> weights ;

    /**
     * 时间列表(与重量点一一对应)
     */
    List<LocalDateTime> times ;

    /**
     * 称重点列表(正常情况，一次完整称重称重点就一个)
     */
    List<WeighDataSavedDTO> points ;
}
