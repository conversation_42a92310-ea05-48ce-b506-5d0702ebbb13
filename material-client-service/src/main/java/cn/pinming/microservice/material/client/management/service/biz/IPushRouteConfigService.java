package cn.pinming.microservice.material.client.management.service.biz;

import cn.pinming.microservice.material.client.management.common.model.PushRouteConfigDO;
import cn.pinming.microservice.material.client.management.common.vo.push.PushRouteConfigVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 公共推送路由配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-11
 */
public interface IPushRouteConfigService extends IService<PushRouteConfigDO> {

    void enablePush(Long routeId);

    List<PushRouteConfigVO> sdkPushConfig();
}
