package cn.pinming.microservice.material.client.management.common.enums;


import lombok.Getter;

@Getter
public enum RecycleSourceEnum {
    //回收来源(1-单据回收终端、2-高拍仪、3-外部推送)
    DEVICE(1, "单据回收终端"),
    CAMERA(2, "高拍仪"),
    PUSH(3, "外部推送"),
    SELF_SERVICE(4, "磅房自助终端"),
    ;

    private final Integer value;
    private final String desc;

    RecycleSourceEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

}
