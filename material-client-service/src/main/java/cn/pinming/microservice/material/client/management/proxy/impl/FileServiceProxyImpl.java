package cn.pinming.microservice.material.client.management.proxy.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.core.upload.DynamicUploadComponent;
import cn.pinming.core.upload.OssFile;
import cn.pinming.core.upload.UploadComponent;
import cn.pinming.core.upload.UploadConfig;
import cn.pinming.core.upload.domain.enums.FileTypeEnums;
import cn.pinming.microservice.material.client.management.proxy.FileServiceProxy;
import cn.pinming.zhjz.cxptz.microservice.filecenter.api.FileCenterService;
import cn.pinming.zhjz.cxptz.microservice.filecenter.api.model.DownloadUrlOptionsDto;
import cn.pinming.zhjz.cxptz.microservice.filecenter.api.model.FileIdentityDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

/**
 * Created by jin on 2020-03-17.
 */
@Slf4j
@Component
public class FileServiceProxyImpl implements FileServiceProxy {
    @Value("${temporary.path}")
    private String temporaryPath;
    @DubboReference
    private FileCenterService fileCenterService;
    private UploadComponent fileCenterServiceUploadComponent;
    @Value("${address}")
    private String address;

    @Override
    public String uploadByBase64(String base64){
        String uuid =  this.uploadByBase64ForUuid(base64);
        return getUrlByUuid(uuid);
    }

    @Override
    public String uploadByBase64ForUuid(String base64){
        try {
            BufferedImage bufferedImage = ImgUtil.toImage(base64);
            String filePath = temporaryPath + StrUtil.format("{}.png", System.currentTimeMillis());
            FileOutputStream outputStream = new FileOutputStream(filePath);
            ImgUtil.writePng(bufferedImage, outputStream);
            File file = new File(filePath);
            OssFile ossFile = new OssFile(file, "png", FileTypeEnums.IMAGE);
            UploadConfig uploadConfig = new UploadConfig("2c91808766adedec0166ce6d1765003c");
            uploadConfig.setCustomSubSystem((byte) 30);
            DynamicUploadComponent dynamicUploadComponent = new DynamicUploadComponent(fileCenterService);
            return dynamicUploadComponent.uploadFile(ossFile, uploadConfig);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public String uploadFileForUuid(MultipartFile multipartFile) {
        try {
            // 获取文件名
            String fileName = StrUtil.isEmpty(multipartFile.getOriginalFilename()) ? StrUtil.format("{}.png", System.currentTimeMillis()) : multipartFile.getOriginalFilename();
            // 获取文件后缀
            String prefix = fileName.substring(fileName.lastIndexOf("."));
            // 若需要防止生成的临时文件重复,可以在文件名后添加随机码
            File file = File.createTempFile(fileName, prefix);
            multipartFile.transferTo(file);

            OssFile ossFile = new OssFile(file, prefix, FileTypeEnums.IMAGE);
            UploadConfig uploadConfig = new UploadConfig("2c91808766adedec0166ce6d1765003c");
            uploadConfig.setCustomSubSystem((byte) 30);
            DynamicUploadComponent dynamicUploadComponent = new DynamicUploadComponent(fileCenterService);
            return dynamicUploadComponent.uploadFile(ossFile, uploadConfig);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void confirm(List<String> list) {
        if (CollUtil.isNotEmpty(list)) {
            fileCenterService.addFileUuidsToFileServer(list);
        }
    }

    /**
     * 通过uuid获取图片路径
     * @param uuid
     * @return
     */
    private String getUrlByUuid(String uuid) {
        FileIdentityDto fileIdentityDto = new FileIdentityDto();
        fileIdentityDto.setFileUuid(uuid);
        DownloadUrlOptionsDto optionsDto = new DownloadUrlOptionsDto();
        LocalDate currentDate = LocalDate.now();
        LocalDate futureDate = currentDate.plusYears(100);
        Date date = Date.from(futureDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        optionsDto.setTime(date);
        return fileCenterService.acquireFileDownloadUrl(fileIdentityDto, optionsDto);
    }

    @Override
    public UploadComponent getDynamicUploadComponent() {
        return new DynamicUploadComponent(fileCenterService);
    }

}
