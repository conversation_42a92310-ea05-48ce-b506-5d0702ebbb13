package cn.pinming.microservice.material.client.management.infrastructure.util;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;

public class AppSecretUtil {

    /**
     * 生成指定长度的字符串
     *
     * @param length    长度
     * @param upperCase 是否大写
     * @return
     */
    public static String generate(int length, boolean upperCase) {
        long timestamp = System.currentTimeMillis();
        String baseString = IdUtil.fastSimpleUUID() + timestamp;
        String s = RandomUtil.randomString(baseString, length);
        return upperCase ? s.toUpperCase() : s;
    }

}
