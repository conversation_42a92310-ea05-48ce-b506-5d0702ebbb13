package cn.pinming.microservice.material.client.management.common.query;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class SelfCheckQuery {

    @ApiModelProperty("设备sn")
    @NotBlank(message = "设备sn为空")
    private String deviceSn;

    @ApiModelProperty("运单条码号")
    @NotBlank(message = "运单条码号为空")
    private String deliveryNo;

}
