package cn.pinming.microservice.material.client.management.common.vo.selfcheck;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class SdkPushDeliveryVO {

    @ApiModelProperty("此发货单在基石平台的唯一编号，不能为空")
    private String jsDeliveryNo;

    @ApiModelProperty("此发货单在基石平台的唯一id，不能为空")
    private Long jsDeliveryId;

    @ApiModelProperty("送货车牌号，不能为空")
    private String truckNo;

    @ApiModelProperty("司机姓名，不能为空")
    private String driverName;

    @ApiModelProperty("司机手机号，不能为空")
    private String driverMobile;

    @ApiModelProperty("发货时间，格式按yyyy-MM-dd hh:mm:ss，不能为空")
    private String deliveryTime;

    @ApiModelProperty("订单基础信息，不可为空")
    private PurchaseBaseInfoVO purchaseBaseInfo;

    @ApiModelProperty("发货单物料明细，不能为空")
    private List<CargoVO> cargoList;
}

