package cn.pinming.microservice.material.client.management.infrastructure.exception;

/**
 * <AUTHOR>
 * @description
 */
public class BizErrorException extends RuntimeException {

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String errorMessage;

    public BizErrorException(BizExceptionMessageEnum bizExceptionMessageEnum) {
        this.errorCode = bizExceptionMessageEnum.errorCode();
        this.errorMessage = bizExceptionMessageEnum.errorMessage();
    }

    public BizErrorException(BizExceptionMessageEnum bizExceptionMessageEnum, Object... variable) {
        this.errorCode = bizExceptionMessageEnum.errorCode();
        this.errorMessage = String.format(bizExceptionMessageEnum.errorMessage(), variable);
    }

    public BizErrorException(String errorCode, String errorMessage) {
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }

    public String errorCode() {
        return errorCode;
    }

    public String errorMessage() {
        return errorMessage;
    }
}
