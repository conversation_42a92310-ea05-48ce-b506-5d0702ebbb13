package cn.pinming.microservice.material.client.management.common.mapper;

import cn.pinming.microservice.material.client.management.common.model.WeighCurveConfigDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 原始记录称重曲线配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
public interface WeighCurveConfigMapper extends BaseMapper<WeighCurveConfigDO> {

    WeighCurveConfigDO getWeighCurveConfig(@Param("deviceId") Long deviceId, @Param("attributionId") Long attributionId, @Param("deviceBindingId") Long deviceBindingId);
}
