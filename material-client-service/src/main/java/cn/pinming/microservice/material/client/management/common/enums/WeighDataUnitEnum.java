package cn.pinming.microservice.material.client.management.common.enums;

import java.math.BigDecimal;
import java.math.RoundingMode;

public enum WeighDataUnitEnum {
    TON("吨", 2),
    KG("千克", 3),
    ;

    private final String unit;

    private final Integer type;//推送称重单位

    WeighDataUnitEnum(String unit, Integer type) {
        this.unit = unit;
        this.type = type;
    }

    public String value() {
        return unit;
    }


    public Integer getType() {
        return type;
    }

    /**
     * 转换为吨系数
     */
    public static BigDecimal convertTon(BigDecimal weight, String unit) {
        if (KG.value().equals(unit)) {
            return weight.divide(BigDecimal.valueOf(1000), 4, RoundingMode.HALF_UP);
        }
        return weight;
    }

    public static WeighDataUnitEnum valueOfType(Integer type) {
        if (type.equals(TON.getType())) {
            return TON;
        } else if (type.equals(KG.getType())) {
            return KG;
        }
        return KG;
    }
}
