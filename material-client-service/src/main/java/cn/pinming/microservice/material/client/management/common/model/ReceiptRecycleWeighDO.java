package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 单据回收称重表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("d_receipt_recycle_weigh")
public class ReceiptRecycleWeighDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 用户id
     */
    private String uid;

    /**
     * 单据回收id
     */
    private Long receiptRecycleId;

    /**
     * 称重数据id
     */
    private Long weighDataId;

    /**
     * 终端记录id
     */
    private String recordId;

    /**
     * 设备机器码
     */
    private String deviceSn;

    /**
     * 重量
     */
    private BigDecimal weight;

    /**
     * 称重单位
     */
    private String unit;

    /**
     * 称重时间
     */
    private LocalDateTime weighTime;

    /**
     * 风险等级（低：LOW，中：MIDDLE，高：HIGH）
     */
    private String riskGrade;

    /**
     * 文件id集合
     */
    private String fileIds;

    /**
     * 是否有效(0-否、1-是)
     */
    private Integer isEffective;


}
