package cn.pinming.microservice.material.client.management.service.business;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.pinming.microservice.material.client.management.common.enums.DeveloperAppEnum;
import cn.pinming.microservice.material.client.management.common.enums.PushLogStatusEnum;
import cn.pinming.microservice.material.client.management.common.enums.PushStatusEnum;
import cn.pinming.microservice.material.client.management.push.Push;
import cn.pinming.microservice.material.client.management.push.PushClient;
import cn.pinming.microservice.material.client.management.common.dto.UserBusinessConfigDTO;
import cn.pinming.microservice.material.client.management.common.dto.WeighSimpleDTO;
import cn.pinming.microservice.material.client.management.common.mapper.ext.UserBusinessConfigExtMapper;
import cn.pinming.microservice.material.client.management.common.mapper.ext.WeighDataExtMapper;
import cn.pinming.microservice.material.client.management.common.mapper.ext.WeighDataPicExtMapper;
import cn.pinming.microservice.material.client.management.common.model.DeviceAttributionDO;
import cn.pinming.microservice.material.client.management.common.model.UserBusinessConfigDO;
import cn.pinming.microservice.material.client.management.common.model.WeighDataDO;
import cn.pinming.microservice.material.client.management.common.model.WeighDataPicDO;
import cn.pinming.microservice.material.client.management.service.biz.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 推送业务服务
 */
@Service
@Slf4j
public class WeighDataPushBusinessService {
    @Resource
    private AppInvocationDailyLogService appInvocationDailyLogService;
    @Resource
    private WeighDataService weighDataService;
    @Resource
    private WeighDataExtMapper weighDataExtMapper;
    @Resource
    private DeviceAttributionService deviceAttributionService;
    @Resource
    private UserConfigService userConfigService;
    @Resource
    private WeighDataPicExtMapper weighDataPicExtMapper;
    @Resource
    private WeighDataPicService weighDataPicService;
    @Resource
    private UserBusinessConfigExtMapper userBusinessConfigExtMapper;
    @Resource
    private PushLogService pushLogService;

    private static final String dataTag = "data";

    private static final String picTag = "pic";

    /**
     * 异步推送称重数据
     */
    @Async("receiptExecutor")
    public void syncPushWeighData(WeighDataDO weighDataDO) {
        //这里触发推送本应该单条数据，后续改造
        UserBusinessConfigDTO userBusinessConfigDTO = checkUserBusinessConfig(weighDataDO.getUid());
        if (userBusinessConfigDTO == null) {
           return;
        }
        //SDK推送方式
        this.pushWeighDataList(userBusinessConfigDTO);
        //一般推送方式 todo 这里可改造为多种方式推送，后续改造
    }

    @Async("receiptExecutor")
    public void syncPushWeighDataPic(WeighDataPicDO weighDataPicDO) {
        //这里触发推送本应该单条数据，后续改造
        UserBusinessConfigDTO userBusinessConfigDTO = checkUserBusinessConfig(weighDataPicDO.getUid());
        if (userBusinessConfigDTO == null) {
            return;
        }
        //SDK推送方式
        this.pushWeighDataPicList(userBusinessConfigDTO);
    }

    private UserBusinessConfigDTO checkUserBusinessConfig(String uid) {
        UserBusinessConfigDO userBusinessConfigDO = userBusinessConfigExtMapper.findUserBusinessConfig(uid);
        if (userBusinessConfigDO == null) {
            return null;
        }
        if (userBusinessConfigDO.getPushType() == 1 && userBusinessConfigDO.getWeighDataPushStatus() == 2 && StrUtil.isNotEmpty(userBusinessConfigDO.getWeighDataPushUrl())) {
            //SDK推送方式
            List<DeviceAttributionDO> attributionDOList = deviceAttributionService.lambdaQuery().eq(DeviceAttributionDO::getUid, userBusinessConfigDO.getUid()).list();
            if (CollUtil.isEmpty(attributionDOList)) {
                return null;
            }
            List<Long> attributionIdList = attributionDOList.stream().map(DeviceAttributionDO::getId).collect(Collectors.toList());
            return this.buildUserBusinessConfigDTO(userBusinessConfigDO, attributionIdList);
        }
        return null;
    }

    public UserBusinessConfigDTO buildUserBusinessConfigDTO(UserBusinessConfigDO userBusinessConfigDO, List<Long> attributionIdList) {
        if (CollUtil.isEmpty(attributionIdList)) {
            return null;
        }
        if (StrUtil.isNotBlank(userBusinessConfigDO.getWeighDataPushAttribution())) {
            // 排除的归属方
            attributionIdList.removeAll(StrUtil.split(userBusinessConfigDO.getWeighDataPushAttribution(), ",").stream().map(Long::new).collect(Collectors.toList()));
            if (CollUtil.isEmpty(attributionIdList)) return null;
        }
        UserBusinessConfigDTO userBusinessConfigDTO = new UserBusinessConfigDTO();
        BeanUtils.copyProperties(userBusinessConfigDO, userBusinessConfigDTO);
        userBusinessConfigDTO.setAttributionIdList(attributionIdList);
        return userBusinessConfigDTO;
    }

    /**
     * 推送租户下所有未推送的称重数据
     */
    public void pushWeighDataList(UserBusinessConfigDTO userBusinessConfigDTO) {
        // 查找数据 （"未推送" || 长时间处于"队列中"(1H)）
        List<WeighSimpleDTO> pushWeighDataList = weighDataExtMapper.needPushWeighDataS(userBusinessConfigDTO.getUid(), userBusinessConfigDTO.getAttributionIdList());
        if (CollUtil.isEmpty(pushWeighDataList)) {
            return;
        }
        for (WeighSimpleDTO weighSimpleDTO : pushWeighDataList) {
            boolean isUsageRemaining = this.pushOneWeighData(userBusinessConfigDTO, weighSimpleDTO);
            if (!isUsageRemaining) break;//批量推送称重数据时,存在无使用次数则不再推送
        }
    }

    /**
     * 推送一条称重数据
     */
    public boolean pushOneWeighData(UserBusinessConfigDTO userBusinessConfigDTO, WeighSimpleDTO weighSimpleDTO) {
        Long apiRemaining = userConfigService.getApiRemainingCount(userBusinessConfigDTO.getUid());
        if (apiRemaining == null || apiRemaining <= 0) {
            log.info("SDK推送次数已用完，uid：{}。", userBusinessConfigDTO.getUid());
            return false;
        }
        weighSimpleDTO.setTag(dataTag);
        boolean isSuccess = this.push(userBusinessConfigDTO, userBusinessConfigDTO.getWeighDataPushUrl(), weighSimpleDTO);
        //称重数据推送状态
        weighDataService.lambdaUpdate().eq(WeighDataDO::getRecordId, weighSimpleDTO.getRecordId())
                .ne(WeighDataDO::getPushStatus, PushStatusEnum.PUSH.getVal())
                .set(WeighDataDO::getPushStatus, isSuccess ? PushStatusEnum.UNCONFIRMED.getVal() : PushStatusEnum.WAIT.getVal())
                .set(WeighDataDO::getWaitTime, LocalDateTime.now()).update();
        if (isSuccess) {
            appInvocationDailyLogService.saveInvokeLog(DeveloperAppEnum.PUSH.value(), userBusinessConfigDTO.getUid(), -1L, 1);
        }
        return true;
    }

    /**
     * 推送租户下所有未推送的称重照片数据
     */
    public void pushWeighDataPicList(UserBusinessConfigDTO userBusinessConfigDTO) {
        if (!(userBusinessConfigDTO.getWeighPicPushStatus() == 2 && StrUtil.isNotEmpty(userBusinessConfigDTO.getWeighPicPushUrl()))) {
            return;
        }
        // 查找未推送成功的照片数据(称重数据已推送过去的 && ("未推送" || 长时间处于"队列中"(1H)))
        List<WeighSimpleDTO> picPushDataList = weighDataPicExtMapper.needPushWeighPicS(userBusinessConfigDTO.getUid(), userBusinessConfigDTO.getAttributionIdList());
        List<Long> successPicIdList = new ArrayList<>();
        List<Long> failPicIdList = new ArrayList<>();
        for (WeighSimpleDTO weighSimpleDTO : picPushDataList) {
            weighSimpleDTO.setTag(picTag);
            if (this.push(userBusinessConfigDTO, userBusinessConfigDTO.getWeighPicPushUrl(), weighSimpleDTO)) {
                successPicIdList.add(Long.valueOf(weighSimpleDTO.getPicId()));
            } else {
                failPicIdList.add(Long.valueOf(weighSimpleDTO.getPicId()));
            }
        }
        if (!CollUtil.isEmpty(successPicIdList)) {
            weighDataPicService.lambdaUpdate().in(WeighDataPicDO::getId, successPicIdList)
                    .ne(WeighDataPicDO::getPushStatus, PushStatusEnum.PUSH.getVal())
                    .set(WeighDataPicDO::getPushStatus, PushStatusEnum.UNCONFIRMED.getVal())
                    .set(WeighDataPicDO::getWaitTime, LocalDateTime.now()).update();
        }
        if (!CollUtil.isEmpty(failPicIdList)) {
            weighDataPicService.lambdaUpdate().in(WeighDataPicDO::getId, failPicIdList)
                    .ne(WeighDataPicDO::getPushStatus, PushStatusEnum.PUSH.getVal())
                    .set(WeighDataPicDO::getPushStatus, PushStatusEnum.WAIT.getVal())
                    .set(WeighDataPicDO::getWaitTime, LocalDateTime.now()).update();
        }
    }


    /**
     * 推送操作
     */
    private boolean push(UserBusinessConfigDO userBusinessConfigDO, String pushUrl, WeighSimpleDTO weighSimpleDTO) {
        String jsonBody = JSONUtil.toJsonStr(weighSimpleDTO);
        try {
            Push push = new PushClient(userBusinessConfigDO.getEndpoint(), pushUrl, userBusinessConfigDO.getRequestHeader(), userBusinessConfigDO.getRequestMode(),
                    userBusinessConfigDO.getAppKey(), userBusinessConfigDO.getAppSecretKey(), userBusinessConfigDO.getSignatureTemplate(), userBusinessConfigDO.getSignatureAlgorithm(),
                    userBusinessConfigDO.getResponseType(), userBusinessConfigDO.getResponseFlag());
            push.pushData(jsonBody);
            log.info("推送成功:uid:{},jsonStr：{}", userBusinessConfigDO.getUid(), jsonBody);
            //推送日志
            pushLogService.createLog(userBusinessConfigDO.getUid(), jsonBody, PushLogStatusEnum.TWO.value(), null);
            return true;
        } catch (Exception e) {
            log.error("推送失败:uid:{},jsonStr：{},errorMessage：{}", userBusinessConfigDO.getUid(), jsonBody, e.getMessage());
            pushLogService.createLog(userBusinessConfigDO.getUid(), jsonBody, PushLogStatusEnum.SIX.value(), e.getMessage());
        }
        return false;
    }
}
