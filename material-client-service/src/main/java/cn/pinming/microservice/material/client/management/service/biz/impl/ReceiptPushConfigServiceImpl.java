package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.pinming.microservice.material.client.management.infrastructure.util.UserIdUtil;
import cn.pinming.microservice.material.client.management.common.form.ReceiptPushConfigForm;
import cn.pinming.microservice.material.client.management.common.mapper.ReceiptPushConfigMapper;
import cn.pinming.microservice.material.client.management.common.model.ReceiptPushConfigDO;
import cn.pinming.microservice.material.client.management.service.biz.ReceiptPushConfigService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <p>
 * 单据回收推送配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-27
 */
@Service
public class ReceiptPushConfigServiceImpl extends ServiceImpl<ReceiptPushConfigMapper, ReceiptPushConfigDO> implements ReceiptPushConfigService {
    @Resource
    private UserIdUtil userIdUtil;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveReceiptPushConfig(ReceiptPushConfigForm form) {
        ReceiptPushConfigDO one = this.lambdaQuery().eq(ReceiptPushConfigDO::getUid, userIdUtil.getUId()).one();
        if (ObjectUtil.isNotNull(one)) {
            form.setId(one.getId());
        }
        ReceiptPushConfigDO receiptPushConfigDO = new ReceiptPushConfigDO();
        BeanUtils.copyProperties(form, receiptPushConfigDO);
        if (form.getPushType() == 2) {
            // 一般推送自动关闭推送开关
            receiptPushConfigDO.setPushStatus(1);
        }
        receiptPushConfigDO.setUid(userIdUtil.getUId());
        this.saveOrUpdate(receiptPushConfigDO);
    }


    @Override
    public ReceiptPushConfigDO getByUid(String uid) {
        return this.lambdaQuery().eq(ReceiptPushConfigDO::getUid, uid).one();
    }
}
