package cn.pinming.microservice.material.client.management.common.vo.delivery;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2024/8/22 17:18
 */
@Data
public class DeliveryDetailDTO {

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("发货单号")
    private String no;

    @ApiModelProperty("订单id")
    private Long purchaseOrderId;

    @ApiModelProperty("车牌号")
    private String truckNo;

    @ApiModelProperty("司机姓名")
    private String driver;

    @ApiModelProperty("司机手机号")
    private String driverMobile;

    @ApiModelProperty("状态 1 在途，2 到场确认中，3 已到场，4 已作废")
    private Integer status;

    @ApiModelProperty("推送状态 1 未推送 2 已推送 3 推送失败")
    private Integer pushState;

    @ApiModelProperty("业务系统订单id")
    private String orderExtId;

    @ApiModelProperty("外部系统供应商id")
    private String supplierExtId;

    @ApiModelProperty("供应商id")
    private String supplierId;

    @ApiModelProperty("供应商名称")
    private String supplierName;

    @ApiModelProperty("收货项目")
    private String project;

    @ApiModelProperty("收货地址")
    private String address;

    @ApiModelProperty("收货人姓名")
    private String receiver;

    @ApiModelProperty("收货人电话")
    private String mobile;

    @ApiModelProperty("要货日期")
    private LocalDate receiveDate;

    @ApiModelProperty("发货单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreate;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("发货明细列表")
    private List<DeliveryItemDTO> list;

}
