package cn.pinming.microservice.material.client.management.service.biz;

import cn.pinming.microservice.material.client.management.common.form.OCRDictForm;
import cn.pinming.microservice.material.client.management.common.model.OcrDictDO;
import cn.pinming.microservice.material.client.management.common.query.OCRDictQuery;
import cn.pinming.microservice.material.client.management.common.vo.OCRDictVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * OCR模版字典表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-03
 */
public interface IOcrDictService extends IService<OcrDictDO> {

    void saveDict(OCRDictForm form);

    void updateDict(OCRDictForm form);

    void deleteDict(Long id);

    Page<OCRDictVO> pageByQuery(OCRDictQuery query);


}
