package cn.pinming.microservice.material.client.management.common.form.supplier;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2024/8/19 17:42
 */
@Data
public class SupplierConfigPushForm {

    @ApiModelProperty("id")
    @NotNull(message = "id为空")
    private Long id;

    @ApiModelProperty("运单推送 0 关闭 1 开启")
    private Integer deliveryPush;

    @ApiModelProperty("确认单推送 0 关闭 1 开启")
    private Integer confirmPush;

    @ApiModelProperty("组装超时时间 单位 分")
    @Min(value = 0, message = "超时时间不能小于0")
    private Integer timeout;

    @ApiModelProperty("供应商材料分类 逗号分隔")
    private String group;

    @ApiModelProperty("一车多料  0 不允许 1 允许")
    private Integer multiCargo;

    @ApiModelProperty(value = "最短时长 单位 分")
    private Integer sameTruckMinDuration;

}
