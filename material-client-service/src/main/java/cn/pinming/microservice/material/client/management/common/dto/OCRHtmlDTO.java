package cn.pinming.microservice.material.client.management.common.dto;


import lombok.Data;

import java.util.List;
import java.util.Map;


@Data
public class OCRHtmlDTO {

    /**
     * 模版id
     */
    private String id;
    /**
     * 其他id
     */
    private String otherId;
    /**
     * html表格内容
     */
    private String table;
    /**
     * 其他字段
     */
    private List<String> ext;
    /**
     * 二维码
     */
    private List<String> barcodes;

    private Map<String, Map<String, String>> result;


}
