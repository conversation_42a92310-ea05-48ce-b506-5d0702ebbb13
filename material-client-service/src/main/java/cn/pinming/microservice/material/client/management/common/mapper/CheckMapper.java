package cn.pinming.microservice.material.client.management.common.mapper;

import cn.pinming.microservice.material.client.management.common.model.CheckDO;
import cn.pinming.microservice.material.client.management.common.query.CheckQuery;
import cn.pinming.microservice.material.client.management.common.vo.CheckMaterialVO;
import cn.pinming.microservice.material.client.management.common.vo.CheckVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface CheckMapper extends BaseMapper<CheckDO> {

    IPage<CheckVO> checkList(@Param("query") CheckQuery query);

    List<CheckMaterialVO> checkMaterialList(@Param("list") List<Long> ids);
}
