package cn.pinming.microservice.material.client.management.common.query;

import cn.pinming.material.v2.model.Page;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class ConfirmPullQuery extends BasePageQuery{
    /**
     * 称重开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 称重结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 数据归属方code(精确搜索)
     */
    private String attributionCode;

    /**
     * 设备机器码(精确搜索)
     */
    private String deviceSn;

    /**
     * 车牌号(支持模糊搜索,与“车牌号列表”二选一)
     */
    private String truckNo;

    /**
     * 车牌号列表(精确搜索;与“车牌号”二选一)
     */
    private List<String> truckNos;

    /**
     * 推送状态  1 未推送 2 已推送 3 推送失败
     */
    private Integer pushStatus;

    /**
     * 收发料类型 1 收料 2 发料
     */
    private Integer weighType;
}
