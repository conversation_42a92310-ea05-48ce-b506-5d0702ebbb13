package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * sdk推送日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("d_push_sdk_log")
public class PushSdkLogDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 请求uri地址
     */
    private String requestUrl;

    /**
     * 请求参数
     */
    private String requestBody;

    /**
     * 返回结果
     */
    private String requestResult;

    /**
     * 错误详情
     */
    private String errorInfo;


}
