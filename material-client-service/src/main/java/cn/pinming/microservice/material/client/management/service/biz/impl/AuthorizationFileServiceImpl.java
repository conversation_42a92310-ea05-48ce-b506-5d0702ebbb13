package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.pinming.microservice.material.client.management.infrastructure.annotation.RoleAuthority;
import cn.pinming.microservice.material.client.management.infrastructure.constant.AuthorizationFileConstant;
import cn.pinming.microservice.material.client.management.common.enums.RoleEnum;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizErrorException;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizExceptionMessageEnum;
import cn.pinming.microservice.material.client.management.infrastructure.util.AesEncryptUtil;
import cn.pinming.microservice.material.client.management.common.form.AuthorizationFileBindForm;
import cn.pinming.microservice.material.client.management.common.form.AuthorizationFileForm;
import cn.pinming.microservice.material.client.management.common.mapper.AuthorizationFileMapper;
import cn.pinming.microservice.material.client.management.common.mapper.DeviceAttributionMapper;
import cn.pinming.microservice.material.client.management.common.model.AuthorizationFileDO;
import cn.pinming.microservice.material.client.management.common.model.DeviceAttributionDO;
import cn.pinming.microservice.material.client.management.common.model.UserDO;
import cn.pinming.microservice.material.client.management.common.vo.AuthorizationFileDownloadVO;
import cn.pinming.microservice.material.client.management.common.vo.AuthorizationFileVO;
import cn.pinming.microservice.material.client.management.common.vo.UserVO;
import cn.pinming.microservice.material.client.management.service.biz.AuthorizationFileService;
import cn.pinming.microservice.material.client.management.service.biz.UserService;
import cn.pinming.springboot.starter.redis.util.RedisUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static cn.pinming.microservice.material.client.management.infrastructure.exception.BizExceptionMessageEnum.*;

@Service
public class AuthorizationFileServiceImpl implements AuthorizationFileService {

    @Resource
    private AuthorizationFileMapper authorizationFileMapper;

    @Resource
    private DeviceAttributionMapper deviceAttributionMapper;

    @Resource
    private UserService userService;
    @Resource
    private RedisUtil redisUtil;

    @Override
    public List<AuthorizationFileVO> getAuthorizationFiles(String uid) {
        List<AuthorizationFileVO> list = Lists.newArrayList();

        QueryWrapper<AuthorizationFileDO> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AuthorizationFileDO::getUid, uid);
        List<AuthorizationFileDO> authorizationFileDOS = authorizationFileMapper.selectList(wrapper);
        if (CollectionUtil.isNotEmpty(authorizationFileDOS)) {
            list = authorizationFileDOS.stream().map(item -> {
                AuthorizationFileVO authorizationFileVO = new AuthorizationFileVO();
                authorizationFileVO.setId(item.getId());
                authorizationFileVO.setAttributionCode(item.getAttributionCode());
                QueryWrapper<DeviceAttributionDO> attributionWrapper = new QueryWrapper<>();
                attributionWrapper.lambda().eq(DeviceAttributionDO::getUid, uid).apply("find_in_set({0},code)",item.getAttributionCode());
                DeviceAttributionDO deviceAttributionDO = deviceAttributionMapper.selectOne(attributionWrapper);
                if (ObjectUtil.isNotNull(deviceAttributionDO)) {
                    authorizationFileVO.setAttributionName(deviceAttributionDO.getName());
                }
                return authorizationFileVO;
            }).collect(Collectors.toList());
        }
        return list;
    }

    @RoleAuthority(value = RoleEnum.ADMIN)
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addOrDelAuthorizationFiles(AuthorizationFileForm authorizationFileForm) {
        Integer addFileCount = authorizationFileForm.getAddFileCount();
        List<Long> deleteFileIDs = authorizationFileForm.getDeleteFileIDs();
        if (ObjectUtil.isNotNull(addFileCount) && addFileCount > 0) {
            for (int i = 0; i < addFileCount; i++) {
                AuthorizationFileDO entity = new AuthorizationFileDO();
                entity.setUid(authorizationFileForm.getUid());
                authorizationFileMapper.insert(entity);
            }
        }
        if (CollectionUtil.isNotEmpty(deleteFileIDs)) {
            authorizationFileMapper.deleteBatchIds(deleteFileIDs);
        }
    }

    @Override
    public void bindAuthorizationFile(AuthorizationFileBindForm authorizationFileBindForm) {
        Long id = authorizationFileBindForm.getId();
        String code = authorizationFileBindForm.getCode();
        String emailCaptcha = authorizationFileBindForm.getEmailCaptcha();
        UserVO userVO = userService.currentUser();
        String bindAttributionEmailCaptcha = UserService.EMAIL_BIND_ATTRIBUTION_CAPTCHA_REDIS_PREFIX + userVO.getEmail();
        String bindAttributionEmailCaptchaCache = (String) redisUtil.get(bindAttributionEmailCaptcha);
        if (StringUtils.isBlank(bindAttributionEmailCaptchaCache)) {
            throw new BizErrorException(BizExceptionMessageEnum.EMAIL_CAPTCHA_INVALID_ERROR);
        }
        if (StringUtils.isBlank(emailCaptcha) || !emailCaptcha.equals(bindAttributionEmailCaptchaCache)) {
            throw new BizErrorException(BizExceptionMessageEnum.EMAIL_CAPTCHA_ERROR);
        }
        // 移除邮箱验证码缓存
        redisUtil.del(bindAttributionEmailCaptcha);
        QueryWrapper<AuthorizationFileDO> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AuthorizationFileDO::getUid, userVO.getUserid());
        List<AuthorizationFileDO> authorizationFileDOS = authorizationFileMapper.selectList(wrapper);
        if (CollectionUtil.isNotEmpty(authorizationFileDOS) && authorizationFileDOS.stream().anyMatch(item -> code.equals(item.getAttributionCode()))) {
            throw new BizErrorException(BizExceptionMessageEnum.AUTHORIZATION_FILE_BIND_EXIST_ERROR);
        }
        AuthorizationFileDO entity = new AuthorizationFileDO();
        entity.setId(id);
        entity.setAttributionCode(code);
        authorizationFileMapper.updateById(entity);
    }

    @Override
    public AuthorizationFileDownloadVO downloadAuthorizationFile(Long id) {
        AuthorizationFileDO authorizationFileDO = authorizationFileMapper.selectById(id);
        if (ObjectUtil.isNull(authorizationFileDO)) {
            throw new BizErrorException(AUTHORIZATION_FILE_NOT_FOUND_ERROR);
        }
        String uid = authorizationFileDO.getUid();
        String attributionCode = authorizationFileDO.getAttributionCode();
        QueryWrapper<DeviceAttributionDO> attributionWrapper = new QueryWrapper<>();
        attributionWrapper.lambda().eq(DeviceAttributionDO::getUid, uid).apply("find_in_set({0},code)",attributionCode);
        DeviceAttributionDO deviceAttributionDO = deviceAttributionMapper.selectOne(attributionWrapper);
        if (ObjectUtil.isNull(deviceAttributionDO)) {
            throw new BizErrorException(AUTHORIZATION_FILE_BIND_NOT_FOUND_ERROR);
        }
        AuthorizationFileDownloadVO downloadVO = new AuthorizationFileDownloadVO();
        downloadVO.setAttributionName(deviceAttributionDO.getName());
        try {
            UserDO userDO = userService.getByUid(uid);
            downloadVO.setContent(AesEncryptUtil.aesEncrypt(AuthorizationFileConstant.buildAuthorizationFileContent(
                    id, userDO.getEmail(), userDO.getUserName(), deviceAttributionDO.getName(), attributionCode,
                    userDO.getAppKey(), userDO.getAppSecretKey()
            ), AuthorizationFileConstant.SECRET_KEY));
        } catch (Exception e) {
            e.printStackTrace();
            throw new BizErrorException(AUTHORIZATION_FILE_AES_ENCRYPT_ERROR);
        }
        return downloadVO;
    }

    @Override
    public Boolean authorizationFileIsValid(Long id) {
        boolean isValid = true;
        AuthorizationFileDO authorizationFileDO = authorizationFileMapper.selectById(id);
        if (ObjectUtil.isNull(authorizationFileDO)) {
            isValid = false;
        } else {
            String uid = authorizationFileDO.getUid();
            String attributionCode = authorizationFileDO.getAttributionCode();
            QueryWrapper<DeviceAttributionDO> attributionWrapper = new QueryWrapper<>();
            attributionWrapper.lambda().eq(DeviceAttributionDO::getUid, uid).apply("find_in_set({0},code)",attributionCode);
            DeviceAttributionDO deviceAttributionDO = deviceAttributionMapper.selectOne(attributionWrapper);
            if (ObjectUtil.isNull(deviceAttributionDO)) {
                isValid = false;
            }
        }
        return isValid;
    }
}
