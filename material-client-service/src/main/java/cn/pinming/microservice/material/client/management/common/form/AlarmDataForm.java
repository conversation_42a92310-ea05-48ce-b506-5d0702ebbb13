package cn.pinming.microservice.material.client.management.common.form;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 *
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2025/4/8 11:27
 */
@Data
public class AlarmDataForm {

    /**
     * 设备sn
     */
    private String deviceSn;

    /**
     * 归属方id
     */
    private Long attributionId;

    /**
     * 当前重量
     */
    private BigDecimal currentData;

    /**
     * 真实重量
     */
    private BigDecimal trueData;

    /**
     * 控制重量
     */
    private BigDecimal controlData;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "设备类型")
    private String deviceType;
}
