package cn.pinming.microservice.material.client.management.common.mapper.ext;

import cn.pinming.microservice.material.client.management.common.vo.PushConfigurationVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PushConfigurationExtMapper {
    List<PushConfigurationVO> userPushConfigurationShow(@Param("uId") String uId);

    Integer checkConfiguration(@Param("id") Long id,@Param("attributionIds") List<String> attributionIds);
}
