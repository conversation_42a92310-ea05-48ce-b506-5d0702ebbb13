package cn.pinming.microservice.material.client.management.service.biz;

import cn.pinming.microservice.material.client.management.common.form.CurveForm;
import cn.pinming.microservice.material.client.management.common.model.WeighCurveDO;
import cn.pinming.microservice.material.client.management.common.vo.WeighCurveVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 原始记录称重曲线 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
public interface IWeighCurveService extends IService<WeighCurveDO> {

    void upload(CurveForm form, String deviceType);

    WeighCurveVO detail(String id);
}
