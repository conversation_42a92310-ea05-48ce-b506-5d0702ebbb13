package cn.pinming.microservice.material.client.management.common.form.oss;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2023/6/16 15:05
 */
@Data
public class FileConfirmForm {

    @NotBlank(message = "客户端sn为空")
    private String sn;

    @Size(min = 1, message = "请添加需要确认的文件")
    private List<String> fileId;
}
