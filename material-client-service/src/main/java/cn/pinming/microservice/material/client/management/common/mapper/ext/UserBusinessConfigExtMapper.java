package cn.pinming.microservice.material.client.management.common.mapper.ext;

import cn.pinming.microservice.material.client.management.common.model.UserBusinessConfigDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface UserBusinessConfigExtMapper {

    /**
     * 查找SDK推送配置
     */
    List<UserBusinessConfigDO> findSdkPushConfig();

    /**
     * 查找用户推送业务配置
     */
    UserBusinessConfigDO findUserBusinessConfig(@Param("uid") String uid);
}
