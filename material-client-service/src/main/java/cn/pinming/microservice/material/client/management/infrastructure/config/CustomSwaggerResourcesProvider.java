package cn.pinming.microservice.material.client.management.infrastructure.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger.web.SwaggerResource;
import springfox.documentation.swagger.web.SwaggerResourcesProvider;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/13
 */
@Component
@Primary
public class CustomSwaggerResourcesProvider implements SwaggerResourcesProvider {

    private final List<Docket> dockets;

    @Autowired
    public CustomSwaggerResourcesProvider(List<Docket> dockets) {
        this.dockets = dockets;
    }

    @Override
    public List<SwaggerResource> get() {
        List<SwaggerResource> resources = new ArrayList<>();
        for (Docket docket : dockets) {
            resources.add(swaggerResource(docket));
        }
        return resources;
    }

    private SwaggerResource swaggerResource(Docket docket) {
        SwaggerResource swaggerResource = new SwaggerResource();
        swaggerResource.setName(docket.getGroupName());
        swaggerResource.setLocation("/v2/api-docs?group=" + docket.getGroupName());
        swaggerResource.setSwaggerVersion(docket.getDocumentationType().getVersion());
        return swaggerResource;
    }
}
