package cn.pinming.microservice.material.client.management.common.vo.print;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class PrintTemplateConfigVO {

    @ApiModelProperty(value = "模板ID")
    private Long id;

    @ApiModelProperty(value = "模板名")
    private String name;

    @ApiModelProperty(value = "打印次数限制")
    private Integer printLimit;

    @ApiModelProperty(value = "设备IdList")
    private List<Long> deviceIdList;
}
