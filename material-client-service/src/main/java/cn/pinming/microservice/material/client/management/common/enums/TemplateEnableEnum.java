package cn.pinming.microservice.material.client.management.common.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
public enum TemplateEnableEnum {
    ON((byte) 0, "启用"),
    OFF((byte) 1, "禁用");

    public static final Map<Byte, String> KEY_MAP = Arrays.stream(values()).collect(Collectors.toMap(TemplateEnableEnum::getVal, TemplateEnableEnum::getDesc));

    public static final Map<String, Byte> VAL_MAP = Arrays.stream(values()).collect(Collectors.toMap(TemplateEnableEnum::getDesc, TemplateEnableEnum::getVal));
    /**
     * 状态值
     */
    private final Byte val;
    /**
     * 状态的描述
     */
    private final String desc;

    TemplateEnableEnum(Byte val, String desc) {
        this.val = val;
        this.desc = desc;
    }

}
