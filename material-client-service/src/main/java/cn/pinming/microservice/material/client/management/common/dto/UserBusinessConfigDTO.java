package cn.pinming.microservice.material.client.management.common.dto;

import cn.pinming.microservice.material.client.management.common.model.UserBusinessConfigDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class UserBusinessConfigDTO extends UserBusinessConfigDO {

    @ApiModelProperty(value = "需推送的归属方id")
    private List<Long> attributionIdList;
}
