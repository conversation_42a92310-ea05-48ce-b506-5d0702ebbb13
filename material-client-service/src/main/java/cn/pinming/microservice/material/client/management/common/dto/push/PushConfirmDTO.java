package cn.pinming.microservice.material.client.management.common.dto.push;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class PushConfirmDTO {
    @ApiModelProperty(value = "基石确认单主键id")
    private Long id;

    @ApiModelProperty(value = "基石确认单id，取local_id")
    private String confirmId;

    @ApiModelProperty("回收批次扩展编码")
    private String extCode;

    @ApiModelProperty(value = "第三方项目唯一标识id")
    private String projectId;

    @ApiModelProperty(value = "基石运单信息")
    private PushConfirmDeliveryDTO jsDeliveryInfo;

    @ApiModelProperty(value = "确认物料明细 不为空")
    private List<PushConfirmMaterialDTO> cargoConfirmList;

    @ApiModelProperty(value = "确认称重、签名信息")
    private PushConfirmDataDTO confirmData;
}
