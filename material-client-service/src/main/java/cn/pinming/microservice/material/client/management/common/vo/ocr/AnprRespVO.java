package cn.pinming.microservice.material.client.management.common.vo.ocr;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2023/10/26 15:24
 */
@Data
public class AnprRespVO {

    private Integer code;

    private String message;

    private List<AnprVO> data;

    @Data
    public static class AnprVO {

        @ApiModelProperty("得分")
        private BigDecimal score;

        private RecognitionVO recognition;

        @Data
        public static class RecognitionVO {

            @ApiModelProperty("车牌")
            private String plateNo;
        }

    }

}
