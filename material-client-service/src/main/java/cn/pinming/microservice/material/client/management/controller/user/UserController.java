package cn.pinming.microservice.material.client.management.controller.user;

import cn.pinming.microservice.material.client.management.common.form.*;
import cn.pinming.microservice.material.client.management.common.enums.EmailCaptchaEnum;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizErrorException;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizExceptionMessageEnum;
import cn.pinming.microservice.material.client.management.infrastructure.response.SingleResponse;
import cn.pinming.microservice.material.client.management.infrastructure.util.StpKit;
import cn.pinming.microservice.material.client.management.common.vo.UserVO;
import cn.pinming.microservice.material.client.management.service.biz.UserService;
import cn.pinming.springboot.starter.redis.util.RedisUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @description
 */
@Api(value = "用户", tags = {"user"})
@RestController
@RequestMapping("/api/user")
public class UserController {

    @Resource
    private UserService userService;

    @Resource
    private RedisUtil redisUtil;

    @ApiOperation(value = "注册", responseReference = "SingleResponse«Boolean»", nickname = "register")
    @PostMapping("/register")
    public SingleResponse<Boolean> register(@RequestBody UserRegisterForm registerForm, HttpServletRequest request) {
        String email = registerForm.getEmail();
        String emailRegisterCacheKey = UserService.EMAIL_REGISTER_CAPTCHA_REDIS_PREFIX + email;

//        // 图形验证码
//        String registerGraphCaptcha = registerForm.getGraphCaptcha();
//        String registerGraphCaptchaCache = (String) request.getSession().getAttribute(UserService.GRAPH_CAPTCHA_SESSION_ATTRIBUTE);
//        if (StringUtils.isBlank(registerGraphCaptchaCache)) {
//            throw new BizErrorException(BizExceptionMessageEnum.GRAPH_CAPTCHA_INVALID_ERROR);
//        }
//        if (StringUtils.isBlank(registerGraphCaptcha) || !registerGraphCaptcha.equals(registerGraphCaptchaCache)) {
//            throw new BizErrorException(BizExceptionMessageEnum.GRAPH_CAPTCHA_ERROR);
//        }

        // 邮箱验证码
        String registerEmailCaptcha = registerForm.getEmailCaptcha();
        String registerEmailCaptchaCache = (String) redisUtil.get(emailRegisterCacheKey);
        if (StringUtils.isBlank(registerEmailCaptchaCache)) {
            throw new BizErrorException(BizExceptionMessageEnum.EMAIL_CAPTCHA_INVALID_ERROR);
        }
        if (StringUtils.isBlank(registerEmailCaptcha) || !registerEmailCaptcha.equals(registerEmailCaptchaCache)) {
            throw new BizErrorException(BizExceptionMessageEnum.EMAIL_CAPTCHA_ERROR);
        }

//        // 移除图形验证码缓存
//        request.getSession().removeAttribute(UserService.GRAPH_CAPTCHA_SESSION_ATTRIBUTE);
        // 移除邮箱验证码缓存
        redisUtil.del(emailRegisterCacheKey);

        boolean register = userService.register(registerForm);
        return SingleResponse.of(register);
    }

    @ApiOperation(value = "图形验证码", responseReference = "SingleResponse«String»", nickname = "graphCaptcha")
    @GetMapping("/graph/captcha")
    public SingleResponse<String> graphCaptcha(HttpServletRequest request) {
        String graphCaptcha = userService.graphCaptcha(request.getSession());
        return SingleResponse.of(graphCaptcha);
    }

    @ApiOperation(value = "邮箱验证码(免登)", responseReference = "SingleResponse«Boolean»", nickname = "emailCaptcha")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "email", value = "邮箱", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "captchaType", value = "验证码类型", required = true, dataType = "EmailCaptchaEnum", paramType = "query")
    })
    @GetMapping("/email/captchaFree")
    public SingleResponse<Boolean> emailCaptcha(@RequestParam(value = "email") String email,
                                                @RequestParam(value = "captchaType") EmailCaptchaEnum captchaEnum) {
        Boolean emailCaptcha = userService.emailCaptchaFree(email, captchaEnum);
        return SingleResponse.of(emailCaptcha);
    }

    @ApiOperation(value = "获取盐", responseReference = "SingleResponse«String»", nickname = "getSalt")
    @ApiImplicitParam(name = "email", value = "邮箱", required = true, dataType = "String", paramType = "query")
    @GetMapping("/salt")
    public SingleResponse<String> getSalt(@RequestParam(value = "email") String email) {
        String dynamicSalt = userService.getDynamicSalt(email);
        return SingleResponse.of(dynamicSalt);
    }

    @ApiOperation(value = "密码登录", responseReference = "SingleResponse«String»", nickname = "passwordLogin")
    @PostMapping("/password/login")
    public SingleResponse<String> passwordLogin(@RequestBody PasswordLoginForm loginForm) {
        String token = userService.passwordLogin(loginForm);
        return SingleResponse.of(token);
    }

    @ApiOperation(value = "邮箱验证码登录", responseReference = "SingleResponse«String»", nickname = "emailLogin")
    @PostMapping("/email/login")
    public SingleResponse<String> emailLogin(@RequestBody EmailLoginForm loginForm) {
        String emailRegisterCacheKey = UserService.EMAIL_LOGIN_CAPTCHA_REDIS_PREFIX + loginForm.getEmail();
        String emailCaptcha = loginForm.getEmailCaptcha();
        String emailCaptchaCache = (String) redisUtil.get(emailRegisterCacheKey);
        if (StringUtils.isBlank(emailCaptchaCache)) {
            throw new BizErrorException(BizExceptionMessageEnum.EMAIL_CAPTCHA_INVALID_ERROR);
        }
        if (StringUtils.isBlank(emailCaptcha) || !emailCaptcha.equals(emailCaptchaCache)) {
            throw new BizErrorException(BizExceptionMessageEnum.EMAIL_CAPTCHA_ERROR);
        }
        String token = userService.emailLogin(loginForm);
        // 移除邮箱验证码缓存
        redisUtil.del(emailRegisterCacheKey);
        return SingleResponse.of(token);
    }

    @ApiOperation(value = "当前登录用户", responseReference = "SingleResponse«UserVO»", nickname = "currentUser")
    @GetMapping("/current/user")
    public SingleResponse<UserVO> currentUser() {
        UserVO userVO = userService.currentUser();
        return SingleResponse.of(userVO);
    }

    @ApiOperation(value = "当前登录用户-伪登录", responseReference = "SingleResponse«UserVO»", nickname = "currentUserByType")
    @GetMapping("/current/currentUserByType")
    public SingleResponse<UserVO> currentUserByType() {
        UserVO userVO = userService.currentUserByType();
        return SingleResponse.of(userVO);
    }

    @ApiOperation(value = "退出登录", responseReference = "SingleResponse«Boolean»", nickname = "logout")
    @GetMapping("/logout")
    public SingleResponse<Boolean> logout() {
        StpKit.DEFAULT.logout();
        return SingleResponse.of(true);
    }

    @ApiOperation(value = "编辑用户信息", responseReference = "SingleResponse«Boolean»", nickname = "updateUser")
    @PostMapping("/update")
    public SingleResponse<Boolean> update(@RequestBody UserUpdateForm form) {
        userService.renew(form);
        return SingleResponse.of(true);
    }

    @ApiOperation(value = "邮箱验证码", responseReference = "SingleResponse«Boolean»", nickname = "emailCaptcha")
    @ApiImplicitParams(@ApiImplicitParam(name = "captchaType", value = "验证码类型", required = true, dataType = "EmailCaptchaEnum", paramType = "query"))
    @GetMapping("/email/captcha")
    public SingleResponse<Boolean> emailCaptcha(@RequestParam(value = "captchaType") EmailCaptchaEnum captchaEnum) {
        Boolean emailCaptcha = userService.emailCaptcha(captchaEnum);
        return SingleResponse.of(emailCaptcha);
    }

    @ApiOperation(value = "修改登录密码", responseReference = "SingleResponse«Boolean»", nickname = "updateUserPassword")
    @PostMapping("/updateUserPassword")
    public SingleResponse<Boolean> updateUserPassword(@RequestBody UpdatePasswordForm form) {
        userService.updateUserPassword(form);
        return SingleResponse.of(true);
    }

    @ApiOperation(value = "修改邮箱", responseReference = "SingleResponse«Boolean»", nickname = "updateUserEmail")
    @PostMapping("/updateUserEmail")
    public SingleResponse<Boolean> updateUserEmail(@RequestBody @Valid UpdateEmailForm form) {
        userService.updateUserEmail(form);
        return SingleResponse.of(true);
    }
}
