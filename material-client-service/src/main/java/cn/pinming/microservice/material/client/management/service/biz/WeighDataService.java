package cn.pinming.microservice.material.client.management.service.biz;

import cn.pinming.material.v2.model.PageList;
import cn.pinming.material.v2.model.query.WeighDataQuery;
import cn.pinming.microservice.material.client.management.common.dto.WeighDataCheckDTO;
import cn.pinming.microservice.material.client.management.common.form.ExpirePicFixForm;
import cn.pinming.microservice.material.client.management.common.form.RefreshPicForm;
import cn.pinming.microservice.material.client.management.common.form.WeighDataStandardForm;
import cn.pinming.microservice.material.client.management.common.form.WeighPicStandardForm;
import cn.pinming.microservice.material.client.management.common.model.WeighDataDO;
import cn.pinming.microservice.material.client.management.common.query.WeighDataPageQuery;
import cn.pinming.microservice.material.client.management.common.query.WeighPullQuery;
import cn.pinming.microservice.material.client.management.common.vo.DataSyncVO;
import cn.pinming.microservice.material.client.management.common.vo.RefreshPicVO;
import cn.pinming.microservice.material.client.management.common.vo.WeighDataConfirmDetailDTO;
import cn.pinming.microservice.material.client.management.common.vo.WeighDataDetailVO;
import cn.pinming.microservice.material.client.management.common.vo.WeighDataPicVO;
import cn.pinming.microservice.material.client.management.common.vo.WeighDataPullVO;
import cn.pinming.microservice.material.client.management.common.vo.WeighDataSimpleVO;
import cn.pinming.microservice.material.client.management.common.vo.WeighDataUpdateLogVO;
import cn.pinming.microservice.material.client.management.common.vo.WeighDataVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 */
public interface WeighDataService extends IService<WeighDataDO> {
    void upload(WeighDataStandardForm bean, String deviceType);

    IPage<WeighDataVO> col(WeighDataPageQuery query);

    WeighDataDetailVO detail(String recordId);

    List<WeighDataDetailVO> detailList(List<String> recordIdList);

    void pic(WeighPicStandardForm form, String deviceType);

    WeighDataCheckDTO check(String deviceSn, Long appId, boolean isOssUpload, String deviceType);

    List<WeighDataPicVO> picList(String weighDataId);

    DataSyncVO sync(String deviceSn, String deviceType);

    WeighDataVO card(String weighDataId);

    void updateRiskGrade(Long id, String riskGrade);

    void updateTruckNo(Long id, String truckNo, Boolean flag);

    List<WeighDataUpdateLogVO> updateLog(Long id);

    void weighDataUpdateLog(Long id, String updateType, String oldValue, String newValue);

    void weighDataLog(Long id, String updateType, String oldValue, String newValue, String email);

    void updateRiskGradeTask(Long id, String riskGrade, String email);

    void updatePushStatus(List<String> recordIdList);

    List<ExpirePicFixForm> expirePicFix(List<ExpirePicFixForm> list);

    RefreshPicVO getPicByTwoRecordId(RefreshPicForm form);

    List<WeighDataSimpleVO> getWeighDataListByIdS(List<String> recordIdList);

    PageList<WeighDataConfirmDetailDTO> dataQuery(WeighDataQuery query);

    IPage<WeighDataPullVO> getDataPull(WeighPullQuery query);

    /**
     * 更新称重记录的作弊报警ID
     *
     * @param recordId     称重记录ID
     * @param cheatAlarmId 作弊报警ID
     */
    void updateCheatAlarmId(String recordId, String cheatAlarmId);

    /**
     * 仅毛皮重称重记录导出
     *
     * @param query    查询条件
     * @param response 响应对象
     */
    void grossTareExport(WeighDataPageQuery query, HttpServletResponse response);

    void confirmOrderExport(WeighDataPageQuery query, HttpServletResponse response);
}
