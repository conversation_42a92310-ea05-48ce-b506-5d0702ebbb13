package cn.pinming.microservice.material.client.management.common.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class WeighPushPicDTO {
    /**
     * 终端记录id
     */
    private String recordId;

    /**
     * 归属方名称
     */
    private String name;

    /**
     * 归属方code
     */
    private String code;

    /**
     * 本地文件路径
     */
    private String filePath;

    /**
     * oss 文件id
     */
    private String fileId;

    /**
     * 1 过磅照片 2 磅房、操作棚照片 3 操作人照片
     */
    private Integer type;

    /**
     * 照片大小(kb)
     */
    private BigDecimal size;

    /**
     * 照片本地创建时间
     */
    private LocalDateTime localCTime;

    /**
     * 照片本地id
     */
    private String localId;

    /**
     * 图片预览地址
     */
    private String previewUrl;

    /**
     * 图片真实地址
     */
    private String downloadUrl;

    /**
     * 用户id
     */
    private String uid;
}
