package cn.pinming.microservice.material.client.management.infrastructure.task;

import cn.hutool.core.collection.CollUtil;
import cn.pinming.microservice.material.client.management.common.dto.UserBusinessConfigDTO;
import cn.pinming.microservice.material.client.management.common.mapper.ext.UserBusinessConfigExtMapper;
import cn.pinming.microservice.material.client.management.common.model.DeviceAttributionDO;
import cn.pinming.microservice.material.client.management.common.model.UserBusinessConfigDO;
import cn.pinming.microservice.material.client.management.service.biz.DeviceAttributionService;
import cn.pinming.microservice.material.client.management.service.business.WeighDataPushBusinessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 称重数据任务
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class WeighDataTask {
    @Resource
    private WeighDataPushBusinessService weighDataPushBusinessService;
    @Resource
    private UserBusinessConfigExtMapper userBusinessConfigExtMapper;
    @Resource
    private DeviceAttributionService deviceAttributionService;

    @Value("${rocketmq.isEnable:false}")
    private boolean isEnable;

    /**
     * 称重数据推送定时任务
     * 每整点20分钟执行一次 推送数据
     */
    @Scheduled(cron = "0 20 * * * ? ")
    @Transactional(rollbackFor = Exception.class)
    public void run() {
        if (!isEnable) {
            return;
        }
        log.info("称重数据推送定时任务start");
        // 需推送租户配置
        List<UserBusinessConfigDO> sdkPushConfigList = userBusinessConfigExtMapper.findSdkPushConfig();
        if (CollUtil.isEmpty(sdkPushConfigList)) {
            return;
        }
        // 数据需推送归属方
        List<UserBusinessConfigDTO> dataNeedPushAttributions = findNeedPushAttributions(sdkPushConfigList);
        if (CollUtil.isEmpty(dataNeedPushAttributions)) {
            return;
        }
        for (UserBusinessConfigDTO userBusinessConfigDTO : dataNeedPushAttributions) {
            weighDataPushBusinessService.pushWeighDataList(userBusinessConfigDTO);//推送租户称重数据
            weighDataPushBusinessService.pushWeighDataPicList(userBusinessConfigDTO);//推送租户称重照片
        }
    }

    /**
     * 需要推送数据方
     *
     * @return list
     */
    private List<UserBusinessConfigDTO> findNeedPushAttributions(List<UserBusinessConfigDO> sdkPushConfigList) {
        List<String> users = sdkPushConfigList.stream().map(UserBusinessConfigDO::getUid).distinct().collect(Collectors.toList());
        List<DeviceAttributionDO> allAttributionList = deviceAttributionService.lambdaQuery().in(DeviceAttributionDO::getUid, users).list();
        if (CollUtil.isEmpty(allAttributionList)) {
            return null;
        }
        Map<String, List<Long>> attributionMap = allAttributionList.stream().collect(Collectors.groupingBy(DeviceAttributionDO::getUid, Collectors.mapping(DeviceAttributionDO::getId, Collectors.toList())));
        //过滤数据
        List<UserBusinessConfigDTO> list = new ArrayList<>();
        sdkPushConfigList.forEach(userBusinessConfigDO -> {
            List<Long> attributionList = attributionMap.get(userBusinessConfigDO.getUid());
            if (CollUtil.isEmpty(attributionList)) {
                return;
            }
            UserBusinessConfigDTO userBusinessConfigDTO = weighDataPushBusinessService.buildUserBusinessConfigDTO(userBusinessConfigDO, attributionList);
            if (userBusinessConfigDTO == null) {
                return;
            }
            list.add(userBusinessConfigDTO);
        });
        return list;
    }
}
