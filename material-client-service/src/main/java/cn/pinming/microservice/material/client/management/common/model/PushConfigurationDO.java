package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 推送服务配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("s_push_configuration")
public class PushConfigurationDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 服务名称
     */
    private String serviceName;

    /**
     * 适用第三方平台
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String clientName;

    /**
     * 服务租户ids
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String userIds;

    /**
     * 服务归属方ids
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String attributionIds;

    /**
     * 是否启用 0 启用 1 禁用
     */
    private Byte isEnable;


}
