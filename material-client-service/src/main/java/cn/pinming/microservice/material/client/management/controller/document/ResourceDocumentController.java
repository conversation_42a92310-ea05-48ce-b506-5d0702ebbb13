package cn.pinming.microservice.material.client.management.controller.document;

import cn.pinming.microservice.material.client.management.infrastructure.response.SingleResponse;
import cn.pinming.microservice.material.client.management.common.form.DocumentForm;
import cn.pinming.microservice.material.client.management.common.vo.DocumentVO;
import cn.pinming.microservice.material.client.management.service.biz.DocumentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;


@Api(value = "文档资源管理", tags = {"resource-document"})
@RestController
@RequestMapping("/api/document/resource")
public class ResourceDocumentController {

    @Resource
    private DocumentService documentService;

    @ApiOperation(value = "资源文档上传", responseReference = "SingleResponse«Boolean»", nickname = "resourceDocumentUpload")
    @PostMapping("/upload")
    public SingleResponse<Boolean> resourceDocumentUpload(@RequestBody DocumentForm documentForm) {
        documentService.resourceDocumentUpload(documentForm);
        return SingleResponse.of(true);
    }

    @ApiOperation(value = "资源文档列表", responseReference = "SingleResponse«List<DocumentVO>»", nickname = "resourceDocumentList")
    @GetMapping("/list")
    public SingleResponse<List<DocumentVO>> resourceDocumentList() {
        List<DocumentVO> list = documentService.resourceDocumentList();
        return SingleResponse.of(list);
    }

    @ApiOperation(value = "资源文档预览", responseReference = "SingleResponse«String»", nickname = "resourceDocumentPreview")
    @GetMapping("/preview/{uuid}")
    public SingleResponse<String> resourceDocumentPreview(@PathVariable String uuid) {
        String url = documentService.resourceDocumentPreview(uuid);
        return SingleResponse.of(url);
    }

    @ApiOperation(value = "资源文档下载", responseReference = "SingleResponse«String»", nickname = "resourceDocumentDownload")
    @GetMapping("/download/{uuid}")
    public SingleResponse<String> resourceDocumentDownload(@PathVariable String uuid) {
        String url = documentService.resourceDocumentDownload(uuid);
        return SingleResponse.of(url);
    }

    @ApiOperation(value = "资源文档删除", responseReference = "SingleResponse«Boolean»", nickname = "resourceDocumentDelete")
    @DeleteMapping("/{uuid}")
    public SingleResponse<Boolean> resourceDocumentDelete(@PathVariable String uuid) {
        documentService.resourceDocumentDelete(uuid);
        return SingleResponse.of(true);
    }

}
