package cn.pinming.microservice.material.client.management.infrastructure.util;

import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.resource.InputStreamResource;
import cn.hutool.core.util.StrUtil;
import cn.pinming.core.upload.OssFile;
import cn.pinming.core.upload.UploadComponent;
import cn.pinming.core.upload.UploadConfig;
import cn.pinming.core.upload.domain.enums.FileTypeEnums;
import cn.pinming.microservice.material.client.management.proxy.FileServiceProxy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.util.Base64;
import java.util.Collections;

@Slf4j
@Component
public class PicUtil {
    @Value("${temporary.path}")
    private String temporaryPath;
    @Resource
    private FileServiceProxy fileServiceV2Proxy;

    /**
     * 通过base64获取uuid
     *
     * @param base64
     * @return
     * @throws FileNotFoundException
     */
    public String getUrlByBase64(String base64, Integer type) throws FileNotFoundException {
        BufferedImage bufferedImage = ImgUtil.toImage(base64.split(StrUtil.COMMA)[0]);
        String filePath = null;
        if (type == 1) {
            filePath = temporaryPath + StrUtil.format("{}.jpg", System.currentTimeMillis());
        } else if (type == 2) {
            filePath = temporaryPath + StrUtil.format("{}.png", System.currentTimeMillis());
        }
        FileOutputStream outputStream = new FileOutputStream(filePath);
        if (type == 1) {
            ImgUtil.writeJpg(bufferedImage, outputStream);
        } else if (type == 2) {
            ImgUtil.writePng(bufferedImage, outputStream);
        }
        File file = new File(filePath);
        UploadComponent uploadComponent = fileServiceV2Proxy.getDynamicUploadComponent();
        OssFile ossFile = new OssFile(file, type == 1 ? "jpg" : "png", FileTypeEnums.IMAGE);
        UploadConfig uploadConfig = new UploadConfig("ff8080815afee284015afee408680001");
        uploadConfig.setCustomSubSystem((byte) 30);
        String uuid = uploadComponent.uploadFile(ossFile, uploadConfig);
        fileServiceV2Proxy.confirm(Collections.singletonList(uuid));
        return uuid;
    }

    /**
     * 通过下载地址获取uuid
     *
     * @param downloadUrl
     * @param memberId
     * @param fileName
     * @return
     */
    public String downloadAndUploadFile(String downloadUrl, String memberId, String fileName) throws IOException {
        // 下载图片字节流
        URL url = new URL(downloadUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");
        int responseCode = connection.getResponseCode();
        if (responseCode == HttpURLConnection.HTTP_OK) {
            InputStream inputStream = connection.getInputStream();
            File file = File.createTempFile("image", ".jpg");
            FileOutputStream outputStream = new FileOutputStream(file);
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            outputStream.close();
            inputStream.close();
            fileName = StrUtil.isBlank(fileName) ? memberId + ".jpg" : fileName + ".jpg";
            OssFile ossFile = new OssFile(file, URLConnection.guessContentTypeFromName(fileName), FileTypeEnums.IMAGE);
            UploadComponent dynamicUploadComponent = fileServiceV2Proxy.getDynamicUploadComponent();
            UploadConfig uploadConfig = new UploadConfig(memberId);
            uploadConfig.setCustomSubSystem((byte) 30);
            return dynamicUploadComponent.uploadFile(ossFile, uploadConfig);
        }
        return null;
    }

    public static InputStreamResource getInputStream(MultipartFile file) {
        InputStreamResource isr = null;
        try {
            isr = new InputStreamResource(file.getInputStream(), file.getOriginalFilename());
        } catch (IOException e) {
            log.info("文件流转换异常:{}", e.getMessage());
        }
        return isr;
    }

    public static InputStreamResource base64ToInputStreamResource(String base64Image) {
        InputStreamResource isr = null;
        try {
            // 解码Base64字符串为字节数组
            byte[] imageBytes = Base64.getDecoder().decode(base64Image);

            // 创建字节数组输入流
            ByteArrayInputStream bais = new ByteArrayInputStream(imageBytes);

            // 创建InputStreamResource对象，这里为了简单起见，没有提供文件名，你可以根据实际情况提供
            isr = new InputStreamResource(bais, "image.png");
        } catch (Exception e) {
            log.info("Base64图片转换为InputStreamResource异常:{}", e.getMessage());
        }
        return isr;
    }
}
