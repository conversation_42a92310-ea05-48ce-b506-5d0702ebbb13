package cn.pinming.microservice.material.client.management.common.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class OCRModuleVO implements Serializable {
    @ApiModelProperty(value = "模版id")
    private Long id;

    @ApiModelProperty(value = "模版父id")
    private Long pid;

    @ApiModelProperty(value = "适用第三方平台")
    private String client;

    @ApiModelProperty(value = "业务数据模板名称")
    private String name;

    @ApiModelProperty(value = "是否启用 1 是 2 否")
    private Byte isEnable;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "外部系统id")
    private String extId;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime gmtModify;

    @ApiModelProperty(value = "归属方数量")
    private Integer attributionAmount;

    @ApiModelProperty(value = "引用数量")
    private Long childrenAmount;

    @ApiModelProperty(value = "数据组列表")
    private List<OCRModuleDetailVO> list;

    @ApiModelProperty(value = "模版底图")
    private String pic;

    @ApiModelProperty(value = "识别结果")
    private String  result;

    @ApiModelProperty(value = "归属方主键ids")
    private String attributionIds;

    @ApiModelProperty("单据匹配方式 :  0 - 区域坐标匹配 1 - 表格索引匹配")
    private Byte billMatchType;

    @ApiModelProperty("html")
    private String html;

}
