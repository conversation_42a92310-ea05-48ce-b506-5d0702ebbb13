package cn.pinming.microservice.material.client.management.common.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2024/9/20 09:38
 */
@Data
public class GatewayForm {

    @ApiModelProperty(value = "路由ID")
    @NotNull(message = "路由ID为空")
    private Long routeId;

    @ApiModelProperty(value = "网关地址")
    @NotBlank(message = "网关地址为空")
    private String gateway;
}
