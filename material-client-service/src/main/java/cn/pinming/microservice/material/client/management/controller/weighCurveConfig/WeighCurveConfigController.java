package cn.pinming.microservice.material.client.management.controller.weighCurveConfig;


import cn.hutool.core.util.ObjUtil;
import cn.pinming.microservice.base.common.util.UserUtil;
import cn.pinming.microservice.material.client.management.common.form.weighCurveConfigForm;
import cn.pinming.microservice.material.client.management.common.model.WeighCurveConfigDO;
import cn.pinming.microservice.material.client.management.common.vo.WeighCurveVO;
import cn.pinming.microservice.material.client.management.infrastructure.response.SingleResponse;
import cn.pinming.microservice.material.client.management.infrastructure.util.UserIdUtil;
import cn.pinming.microservice.material.client.management.service.biz.IWeighCurveConfigService;
import cn.pinming.microservice.material.client.management.service.biz.IWeighCurveService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 原始记录称重曲线配置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@Api(value = "称重曲线数据配置", tags = {"curveConfig"})
@RestController
@RequestMapping("/api/curveConfig")
public class WeighCurveConfigController {
    @Resource
    private IWeighCurveConfigService weighCurveConfigService;
    @Resource
    private UserIdUtil userIdUtil;

    @ApiOperation(value = "称重曲线配置")
    @PostMapping("/save")
    public SingleResponse<?> save(@RequestBody weighCurveConfigForm form) {
        weighCurveConfigService.config(form);
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "称重曲线配置列表")
    @GetMapping("/list")
    public SingleResponse<?> list() {
        List<WeighCurveConfigDO> list = weighCurveConfigService.lambdaQuery()
                .eq(WeighCurveConfigDO::getUid, userIdUtil.getUId())
                .list();
        return SingleResponse.of(list);
    }

    @ApiOperation(value = "称重曲线配置详情")
    @GetMapping("/detail")
    public SingleResponse<?> detail(@RequestParam(required = false)Integer id,@RequestParam(required = false)Integer attributionId) {
        if (ObjUtil.isNull(id) && ObjUtil.isNull(attributionId)) {
            return SingleResponse.buildSuccess();
        }
        WeighCurveConfigDO one = weighCurveConfigService.lambdaQuery()
                .eq(ObjUtil.isNotNull(id), WeighCurveConfigDO::getId, id)
                .eq(ObjUtil.isNotNull(attributionId), WeighCurveConfigDO::getAttributionId, attributionId)
                .one();
        return SingleResponse.of(one);
    }
}

