package cn.pinming.microservice.material.client.management.infrastructure.util;

import cn.pinming.springboot.starter.redis.util.RedisUtil;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

@Component
@AllArgsConstructor
public class NoUtil {
    private static final String REBAR_CHECK_KEY_PREFIX = "GJYS-";
    private static final String WEIGH_DATA_CONFIRM_KEY_PREFIX = "ZZQRD";

    private final RedisUtil redisUtil;

    /**
     * 采购单号
     *
     * @param attributionCode 归属方code
     * @return
     */
    public String getRebarCheckKeyPrefixNo(String attributionCode) {
        String redisKeyPrefix = getRedisKeyPrefix(REBAR_CHECK_KEY_PREFIX + attributionCode);
        long incrNum = redisUtil.incr(redisKeyPrefix, 1);
        return redisKeyPrefix + String.format("%1$03d", incrNum);
    }

    /**
     * 采购单号
     *
     * @return
     */
    public String getWeighDataConfirmKeyPrefixNo() {
        String redisKeyPrefix = getRedisKeyPrefix(WEIGH_DATA_CONFIRM_KEY_PREFIX);
        long incrNum = redisUtil.incr(redisKeyPrefix, 1);
        return redisKeyPrefix + String.format("%1$06d", incrNum);
    }

    public String getRedisKeyPrefix(String keyPrefix) {
        LocalDate today = LocalDate.now();
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyyMMdd");
        String dateStr = today.format(fmt);
        return keyPrefix + "-" + dateStr;
    }

    /**
     * 生成唯一14位id = 1位类型 + 13位数值
     *
     * @param type 1 运单 2 ocr识别回收编号
     */
    public String generateUniqueId(int type) {
        UUID uuid = UUID.randomUUID();
        long mostSignificantBits = uuid.getMostSignificantBits();
        long leastSignificantBits = uuid.getLeastSignificantBits();
        long combined = mostSignificantBits ^ leastSignificantBits;
        combined = Math.abs(combined);
        return String.format("%d%013d", type, combined % 10000000000000L);
    }

//    public static void main(String[] args) {
//        String uuid = generateId(1, "ee3c3e71850ad4a11a0bf4026ae372a3", "90000");
//        System.out.println(uuid);
//    }
//
//    public static String generateId(int type, String externalSystemId, String ownerId) {
//        // 使用外部系统ID和归属方ID生成基础数字
//        String base = externalSystemId + ":" + ownerId;
//        // 生成哈希值
//        int hash = base.hashCode();
//        // 取绝对值并转换为字符串
//        String numericString = String.valueOf(Math.abs(hash));
//        // 截取或填充到13位
//        if (numericString.length() > 13) {
//            return type + numericString.substring(0, 13);
//        } else {
//            return type + String.format("%013d", Long.parseLong(numericString));
//        }
//    }
}
