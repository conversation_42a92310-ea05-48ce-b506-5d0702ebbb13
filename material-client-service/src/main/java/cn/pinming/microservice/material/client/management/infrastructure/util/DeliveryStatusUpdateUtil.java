package cn.pinming.microservice.material.client.management.infrastructure.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.pinming.microservice.material.client.management.common.enums.DeliveryDetailStatusEnum;
import cn.pinming.microservice.material.client.management.common.enums.DeliveryStatusEnum;
import cn.pinming.microservice.material.client.management.common.model.DeliveryDO;
import cn.pinming.microservice.material.client.management.common.model.DeliveryDetailDO;
import cn.pinming.microservice.material.client.management.common.model.ext.WeighDataExtDO;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizErrorException;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizExceptionMessageEnum;
import cn.pinming.microservice.material.client.management.service.biz.IDeliveryDetailService;
import cn.pinming.microservice.material.client.management.service.biz.IDeliveryService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class DeliveryStatusUpdateUtil {
    @Resource
    private IDeliveryService deliveryService;
    @Resource
    private IDeliveryDetailService deliveryDetailService;

    public void judge(DeliveryDO deliveryDO, Integer isConfirmed, List<WeighDataExtDO> weighDataList) {
        if (ObjectUtil.isNull(deliveryDO)) {
            return;
        }

        List<DeliveryDetailDO> detailDOList = deliveryDetailService.lambdaQuery()
                .select(DeliveryDetailDO::getStatus)
                .eq(DeliveryDetailDO::getDeliveryId, deliveryDO.getId())
                .list();

        // 确认单回传
        if (CollUtil.isNotEmpty(detailDOList)) {
            long oneCount = detailDOList.stream().filter(e -> e.getStatus().equals(DeliveryDetailStatusEnum.ONE.value())).count();
            if (oneCount == detailDOList.size()) {
                deliveryDO.setStatus(DeliveryStatusEnum.ONE.value());
            }

            long count = detailDOList.stream().filter(e -> e.getStatus().equals(DeliveryDetailStatusEnum.TWO.value()) || e.getStatus().equals(DeliveryDetailStatusEnum.THREE.value())).count();
            if (count > 0) {
                deliveryDO.setStatus(DeliveryStatusEnum.TWO.value());
            }

            long fourCount = detailDOList.stream().filter(e -> e.getStatus().equals(DeliveryDetailStatusEnum.FOUR.value())).count();
            if (fourCount == detailDOList.size()) {
                deliveryDO.setStatus(DeliveryStatusEnum.THREE.value());
            }

            long fourOrFiveCount = detailDOList.stream().filter(e -> e.getStatus().equals(DeliveryDetailStatusEnum.FOUR.value()) || e.getStatus().equals(DeliveryDetailStatusEnum.FIVE.value())).count();
            if (fourOrFiveCount == detailDOList.size()) {
                deliveryDO.setStatus(DeliveryStatusEnum.THREE.value());
            }

            long fiveCount = detailDOList.stream().filter(e -> e.getStatus().equals(DeliveryDetailStatusEnum.FIVE.value())).count();
            if (fiveCount == detailDOList.size()) {
                deliveryDO.setStatus(DeliveryStatusEnum.FOUR.value());
            }

        }

        // OCR确认单回传
        if (deliveryDO.getType() == 2) {
            if (isConfirmed == 1) {
                // 运单确认
                deliveryDO.setStatus(DeliveryStatusEnum.THREE.value());
            }
            if (isConfirmed == 0) {
                // 运单在途
                deliveryDO.setStatus(DeliveryStatusEnum.ONE.value());
            }
        }

        // 仅毛皮重运单
        if (CollUtil.isNotEmpty(weighDataList) && (deliveryDO.getType() == 3 || deliveryDO.getType() == 1)) {
            if (weighDataList.size() > 2) {
                throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "称重数据异常");
            }
            int size = weighDataList.size();
            if (size == 1) {
                deliveryDO.setStatus(DeliveryStatusEnum.FIVE.value());
            } else {
                deliveryDO.setStatus(DeliveryStatusEnum.THREE.value());
            }
        }

        deliveryService.updateById(deliveryDO);
    }
}
