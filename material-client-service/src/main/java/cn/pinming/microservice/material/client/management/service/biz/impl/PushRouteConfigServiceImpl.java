package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizErrorException;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizExceptionMessageEnum;
import cn.pinming.microservice.material.client.management.infrastructure.util.UserIdUtil;
import cn.pinming.microservice.material.client.management.common.mapper.PushRouteConfigMapper;
import cn.pinming.microservice.material.client.management.common.model.DeviceAttributionDO;
import cn.pinming.microservice.material.client.management.common.model.PushRouteConfigDO;
import cn.pinming.microservice.material.client.management.common.model.PushUserConfigDO;
import cn.pinming.microservice.material.client.management.common.vo.push.PushRouteConfigVO;
import cn.pinming.microservice.material.client.management.service.biz.DeviceAttributionService;
import cn.pinming.microservice.material.client.management.service.biz.IPushRouteConfigService;
import cn.pinming.microservice.material.client.management.service.biz.IPushUserConfigService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 公共推送路由配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-11
 */
@Service
public class PushRouteConfigServiceImpl extends ServiceImpl<PushRouteConfigMapper, PushRouteConfigDO> implements IPushRouteConfigService {

    @Resource
    private UserIdUtil userIdUtil;
    @Resource
    private PushRouteConfigMapper pushRouteConfigMapper;
    @Resource
    private IPushUserConfigService pushUserConfigService;
    @Resource
    private DeviceAttributionService deviceAttributionService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enablePush(Long routeId) {
        String uid = userIdUtil.getUId();
        PushUserConfigDO userConfigDO = pushUserConfigService.lambdaQuery().eq(PushUserConfigDO::getCreateId, uid).eq(PushUserConfigDO::getRouteConfigId, routeId).one();
        if (userConfigDO == null) {
            userConfigDO = new PushUserConfigDO();
            userConfigDO.setRouteConfigId(routeId);
            userConfigDO.setIsOpen(2);
        } else {
            userConfigDO.setIsOpen(userConfigDO.getIsOpen() == 1 ? 2 : 1);
        }
        if (userConfigDO.getIsOpen() == 2) {
            if (StrUtil.isBlank(userConfigDO.getGateway())) {
                throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), "请配置回调网关");
            }

            String excludeId = userConfigDO.getExcludeId();
            List<Long> attributionIdList = new ArrayList<>();
            if (StrUtil.isNotBlank(excludeId)) {
                attributionIdList = StrUtil.split(excludeId, StrUtil.COMMA).stream().map(Long::parseLong).collect(Collectors.toList());
            }
            List<DeviceAttributionDO> attributionList = deviceAttributionService.lambdaQuery().eq(DeviceAttributionDO::getUid, uid)
                    .notIn(CollUtil.isNotEmpty(attributionIdList), DeviceAttributionDO::getId, attributionIdList)
                    .isNull(DeviceAttributionDO::getPrimaryCode).list();
            if (CollUtil.isNotEmpty(attributionList)) {
                List<String> attributionNameList = attributionList.stream().map(DeviceAttributionDO::getName).collect(Collectors.toList());
                throw new BizErrorException(BizExceptionMessageEnum.LOGIC_ERROR.errorCode(), StrUtil.format("所选归属方未配置主CODE:{}", StrUtil.join(StrUtil.COMMA, attributionNameList)));
            }
        }
        pushUserConfigService.saveOrUpdate(userConfigDO);
    }

    @Override
    public List<PushRouteConfigVO> sdkPushConfig() {
        String uid = userIdUtil.getUId();
        List<PushRouteConfigVO> list = pushRouteConfigMapper.selectRouteList(uid);
        if (CollUtil.isNotEmpty(list)) {
            list.forEach(item -> {
                if (StrUtil.isNotBlank(item.getExcludeId())) {
                    List<Long> attributionIdList = StrUtil.split(item.getExcludeId(), StrUtil.COMMA).stream().map(Long::parseLong).collect(Collectors.toList());
                    item.setAttributionIdList(attributionIdList);
                }
            });
        }
        return list;
    }
}
