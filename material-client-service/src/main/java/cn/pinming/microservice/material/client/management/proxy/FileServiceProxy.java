package cn.pinming.microservice.material.client.management.proxy;

import cn.pinming.core.upload.UploadComponent;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * Created by jin on 2020-03-17.
 */
public interface FileServiceProxy {
    String uploadByBase64(String base64);

    String uploadByBase64ForUuid(String base64);

    String uploadFileForUuid(MultipartFile multipartFile);

    void confirm(List<String> list);

    UploadComponent getDynamicUploadComponent();
}
