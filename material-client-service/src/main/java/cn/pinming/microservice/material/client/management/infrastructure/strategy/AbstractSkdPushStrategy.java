package cn.pinming.microservice.material.client.management.infrastructure.strategy;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import cn.pinming.microservice.material.client.management.common.enums.SdkPushTypeEnum;
import cn.pinming.microservice.material.client.management.common.model.DeviceAttributionDO;
import cn.pinming.microservice.material.client.management.common.model.SupplierConfigDO;
import cn.pinming.microservice.material.client.management.common.model.UserDO;
import cn.pinming.microservice.material.client.management.common.vo.push.PushRouteConfigVO;
import cn.pinming.microservice.material.client.management.infrastructure.util.SignUtil;
import cn.pinming.microservice.material.client.management.service.biz.DeviceAttributionService;
import cn.pinming.microservice.material.client.management.service.biz.IPushSdkLogService;
import cn.pinming.microservice.material.client.management.service.biz.IPushUserConfigService;
import cn.pinming.microservice.material.client.management.service.biz.SupplierConfigService;
import cn.pinming.microservice.material.client.management.service.biz.UserService;
import cn.pinming.microservice.material.client.management.service.push.sdk.dto.SdkPushDTO;
import cn.pinming.microservice.material.client.management.service.push.sdk.dto.SdkRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Async;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

/**
 * 请求鉴权策略抽象类.
 *
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2024/9/9 17:19
 */
@Slf4j
public abstract class AbstractSkdPushStrategy<T> {

    /**
     * 单次最大推送数量.
     */
    private static final Integer MAX_SIZE = 1;

    @Resource
    private IPushUserConfigService   userConfigService;
    @Resource
    private UserService              userService;
    @Resource
    private IPushSdkLogService       pushSdkLogService;
    @Resource
    private DeviceAttributionService deviceAttributionService;
    @Resource
    private SupplierConfigService    supplierConfigService;
    @Resource
    private ApplicationContext       applicationContext;

    abstract Long getRouteConfigId();

    /**
     * 查询数据.
     */
    abstract Map<String, List<T>> queryDataListMap(PushRouteConfigVO config, Map<Long, String> attributionMap);

    /**
     * 处理推送结果.
     *
     * @param result     推送响应结果
     * @param pushedData 本次实际推送的数据列表
     */
    abstract void afterCompletion(SdkRespDTO result, List<T> pushedData);

    /**
     * 获取推送配置列表.
     *
     * @return 推送配置列表
     */
    public List<PushRouteConfigVO> getConfigList() {
        List<PushRouteConfigVO> configList = userConfigService.selectOpenedRouteConfigList(getRouteConfigId());
        if (CollUtil.isEmpty(configList)) {
            String serviceName = SdkPushTypeEnum.KEY_MAP.get(getRouteConfigId());
            log.error("没有找到启用的《{}》配置", serviceName);
            return new ArrayList<>();
        }
        return configList;
    }

    public Map<Long, String> checkAttribution(PushRouteConfigVO config) {
        String uid = config.getUid();
        String excludeId = config.getExcludeId();
        List<String> attributionIdList = new ArrayList<>();
        if (StrUtil.isNotBlank(excludeId)) {
            attributionIdList = StrUtil.split(excludeId, StrUtil.COMMA);
        }
        List<DeviceAttributionDO> attributionList = deviceAttributionService.lambdaQuery().eq(DeviceAttributionDO::getUid, uid)
                .notIn(CollUtil.isNotEmpty(attributionIdList), DeviceAttributionDO::getId, attributionIdList).isNotNull(DeviceAttributionDO::getPrimaryCode).list();
        Map<Long, String> result = new HashMap<>();
        if (CollUtil.isNotEmpty(attributionList)) {
            result = attributionList.stream().collect(Collectors.toMap(DeviceAttributionDO::getId, DeviceAttributionDO::getPrimaryCode));
        }
        return result;
    }

    /**
     * 推送数据.
     */
    public void pushData(String code, UserDO userDO, PushRouteConfigVO config, List<T> list, String requestId) {
        long timestamp = System.currentTimeMillis();
        SdkPushDTO<T> dto = new SdkPushDTO<>();
        dto.setSignature(SignUtil.sign(userDO.getAppKey(), userDO.getAppSecretKey(), timestamp));// 签名
        dto.setTimestamp(timestamp);
        dto.setPrimaryCode(code);
        dto.setData(list);
        // log.info("[{}-{}] 推送请求原始内容, code: {}, 请求: {}, 时间: {}", config.getDescription(), requestId, code, JSONUtil.toJsonStr(dto), timestamp);
        // 推送数据
        String requestBody = request(userDO.getUid(), config.getGateway() + config.getEndPoint(), JSONUtil.toJsonStr(dto), timestamp);

        // 打印对方响应的原始内容
        // log.info("[{}-{}] 对方响应原始内容, code: {}, 响应: {}, 时间: {}", config.getDescription(), requestId, code, requestBody, timestamp);

        if (JSONUtil.isTypeJSON(requestBody)) {
//            log.info("返回结果: {}", requestBody);
            // 更新数据
            SdkRespDTO resp = JSONUtil.toBean(requestBody, SdkRespDTO.class);
            if (resp.isSuccess()) {
                afterCompletion(resp, list);
            }
        }
    }

    /**
     * 执行推送.
     */
    public void execute() {
        if (getRouteConfigId() == null) {
            return;
        }
        List<PushRouteConfigVO> configList = getConfigList();
        // 查询数据
        AbstractSkdPushStrategy proxy = applicationContext.getBean(this.getClass());
        configList.forEach(proxy::pushSdkData);
    }

    // @Async("sdkPushExecutor")
    // public void pushSdkData(PushRouteConfigVO config) {
    //     UserDO userDO = userService.getByUid(config.getUid());
    //     Map<Long, String> attributionMap = checkAttribution(config);
    //     if (CollUtil.isEmpty(attributionMap)) {
    //         log.error("没有找到有效的设备归属信息");
    //         return;
    //     }
    //     // 查询数据
    //     Map<String, List<T>> dataListMap = queryDataListMap(config, attributionMap);
    //     if (CollUtil.isNotEmpty(dataListMap)) {
    //         dataListMap.forEach((code, dataList) -> {
    //             List<List<T>> subList = ListUtil.split(dataList, MAX_SIZE);
    //             subList.parallelStream().forEach(sub -> pushData(code, userDO, config, sub));
    //         });
    //     } else {
    //         log.error("没有查询到有效数据");
    //     }
    // }


    @Async("sdkPushExecutor")
    public void pushSdkData(PushRouteConfigVO config) {
        String requestId = UUID.randomUUID().toString();
        log.info("[{}-{}] 开始推送SDK数据, uid: {}", config.getDescription(), requestId, config.getUid());

        UserDO userDO = userService.getByUid(config.getUid());
        if (userDO == null) {
            log.error("[{}-{}] 用户不存在, uid: {}", config.getDescription(), requestId, config.getUid());
            return;
        }

        Map<Long, String> attributionMap = checkAttribution(config);
        if (CollUtil.isEmpty(attributionMap)) {
            log.error("[{}-{}] 没有找到有效的设备归属信息, uid: {}", config.getDescription(), requestId, config.getUid());
            return;
        }

        // 查询数据
        Map<String, List<T>> dataListMap = queryDataListMap(config, attributionMap);
        if (CollUtil.isEmpty(dataListMap)) {
            log.error("[{}-{}] 没有查询到有效数据, uid: {}", config.getDescription(), requestId, config.getUid());
            return;
        }

        // 使用CompletableFuture进行异步编排
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        dataListMap.forEach((code, dataList) -> {
            List<List<T>> subLists = ListUtil.split(dataList, MAX_SIZE);

            for (List<T> subList : subLists) {
                // 使用默认线程池(ForkJoinPool.commonPool())
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    try {
                        pushData(code, userDO, config, subList, requestId);
                    } catch (Exception e) {
                        log.error("[{}-{}] 推送数据异常, code: {}, 异常: {}", config.getDescription(), requestId, code, e.getMessage(), e);
                    }
                });

                futures.add(future);
            }
        });

        try {
            // 等待所有任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get(30, TimeUnit.MINUTES);
            log.info("[{}-{}] 所有数据推送完成, uid: {}", config.getDescription(), requestId, config.getUid());
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("[{}-{}] 推送任务被中断, uid: {}", config.getDescription(), requestId, config.getUid(), e);
        } catch (ExecutionException e) {
            log.error("[{}-{}] 推送任务执行异常, uid: {}, 异常: {}", config.getDescription(), requestId, config.getUid(), e.getCause().getMessage(), e.getCause());
        } catch (TimeoutException e) {
            log.error("[{}-{}] 推送任务超时, uid: {}", config.getDescription(), requestId, config.getUid(), e);
        }
    }


    /**
     * 请求接口.
     */
    private String request(String uid, String url, String body, long timestamp) {
        HttpRequest request = HttpRequest.post(url).body(body)
                .header("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********")
                .setConnectionTimeout(3000).setReadTimeout(120000);
        try {
            HttpResponse execute = request.execute();
//            if (!execute.isOk()) {
////                recordLog(uid, url, body, execute.body(), null);
//            }
            return execute.body();
        } catch (Exception e) {
            log.error("HTTP请求异常, uid: {}, url: {}, ,异常: {}, 时间: {}", uid, url, e.getMessage(), timestamp, e);
//            recordLog(uid, url, body, null, e.getMessage());
        }
        return "";
    }

    public List<String> getSupplierList(String uid, boolean isDelivery, boolean isConfirm) {
        List<String> result = new ArrayList<>();
        if (isDelivery || isConfirm) {
            List<SupplierConfigDO> list = supplierConfigService.lambdaQuery().eq(SupplierConfigDO::getCreateId, uid)
                    .eq(isDelivery, SupplierConfigDO::getDeliveryPush, 1)
                    .eq(isConfirm, SupplierConfigDO::getConfirmPush, 1)
                    .list();
            if (CollUtil.isNotEmpty(list)) {
                result = list.stream().map(SupplierConfigDO::getSupplierSysId).collect(Collectors.toList());
            }
        }
        return result;
    }

//    @Async("sdkPushLogExecutor")
//    private void recordLog(String uid, String url, String body, String resp, String errorInfo) {
////        PushSdkLogDO logDO = new PushSdkLogDO();
////        logDO.setCreateId(uid);
////        logDO.setRequestBody(body);
////        logDO.setRequestUrl(url);
////        logDO.setRequestResult(resp);
////        logDO.setErrorInfo(errorInfo);
////        pushSdkLogService.save(logDO);
//    }


}
