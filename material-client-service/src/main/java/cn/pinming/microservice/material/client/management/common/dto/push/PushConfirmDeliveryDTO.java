package cn.pinming.microservice.material.client.management.common.dto.push;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class PushConfirmDeliveryDTO {
    @ApiModelProperty(value = "业务系统订单id")
    private String orderId;

    @ApiModelProperty(value = "基石订单运单单号")
    private String deliveryNo;

    @ApiModelProperty(value = "车牌号 不为空")
    private String truckNo;

    @ApiModelProperty(value = "司机名称 不为空")
    private String driverName;

    @ApiModelProperty(value = "司机电话 不为空")
    private String driverPhoneNo;

    @ApiModelProperty(value = "基石运单发货时间 不为空")
    private String deliveryTime;
}
