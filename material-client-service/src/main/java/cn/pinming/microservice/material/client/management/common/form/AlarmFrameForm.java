package cn.pinming.microservice.material.client.management.common.form;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 *
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2025/4/8 11:27
 */
@Data
public class AlarmFrameForm {

    @ApiModelProperty(value = "本地日志ID")
    private String id;

    @ApiModelProperty(value = "设备sn")
    private String deviceSn;

    @ApiModelProperty(value = "设备类型")
    private String deviceType;

    @ApiModelProperty(value = "归属方id")
    private Long attributionId;

    @ApiModelProperty(value = "数据")
    private String data;

//    @ApiModelProperty(value = "是否已处理")
//    private Integer isRiskRemoved;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime riskRemoveTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty("照片文件ID")
    private String fileId;


}
