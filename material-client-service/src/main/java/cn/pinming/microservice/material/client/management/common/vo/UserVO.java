package cn.pinming.microservice.material.client.management.common.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/6/12
 */
@Data
@Builder
public class UserVO {

    @ApiModelProperty("用户ID")
    private String userid;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("用户名")
    private String userName;

    @ApiModelProperty("管理员")
    private Byte isAdmin;

    @ApiModelProperty("用户logo")
    private String logoPic;

    @ApiModelProperty("是否为伪登录")
    private Boolean isFake;
}
