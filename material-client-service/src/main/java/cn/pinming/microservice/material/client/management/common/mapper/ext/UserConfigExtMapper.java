package cn.pinming.microservice.material.client.management.common.mapper.ext;

import cn.pinming.microservice.material.client.management.common.query.UserQuery;
import cn.pinming.microservice.material.client.management.common.vo.AppInvocationVO;
import cn.pinming.microservice.material.client.management.common.vo.UserSubscribeVO;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2023/8/7 17:08
 */
public interface UserConfigExtMapper {

    @InterceptorIgnore(tenantLine = "true")
    IPage<UserSubscribeVO> selectSubscribeList(@Param("query") UserQuery query);

    List<AppInvocationVO> selectAppInvocationList(@Param("uid") String uid, @Param("appIdList") List<Long> appIdList);
}
