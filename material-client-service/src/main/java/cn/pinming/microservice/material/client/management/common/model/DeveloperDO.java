package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 开发者表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("s_developer")
public class DeveloperDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * appId
     */
    private Long appId;

    /**
     * 0 使用中 1 停用中
     */
    private Byte type;

//    /**
//     * 服务状态 1 正常使用 2 请续费
//     */
//    private Byte state;

    /**
     * 以下风险等级及以上不允许操作（中：MIDDLE，高：HIGH）
     */
    private String riskGrade;

    /**
     * 是否允许手动输入 1 允许 2 不允许
     */
    private Byte isInputEnable;

}
