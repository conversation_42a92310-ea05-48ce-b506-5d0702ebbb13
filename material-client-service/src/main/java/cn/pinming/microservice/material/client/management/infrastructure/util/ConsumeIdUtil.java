package cn.pinming.microservice.material.client.management.infrastructure.util;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizErrorException;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizExceptionMessageEnum;
import cn.pinming.microservice.material.client.management.common.BaseDO;
import cn.pinming.microservice.material.client.management.common.model.DeviceAttributionDO;
import cn.pinming.microservice.material.client.management.common.model.UserConsumerDO;
import cn.pinming.microservice.material.client.management.common.vo.AttributionExtConfigVO;
import cn.pinming.microservice.material.client.management.common.vo.ConsumerVO;
import cn.pinming.microservice.material.client.management.service.biz.DeviceAttributionService;
import cn.pinming.microservice.material.client.management.service.biz.UserConsumerService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;

/**
 * <AUTHOR>
 */
@Component
public class ConsumeIdUtil {
    @Resource
    private UserConsumerService userConsumerService;
    @Resource
    private DeviceAttributionService deviceAttributionService;

    /**
     * 获取当前登录用户信息
     *
     * @return
     */
    public ConsumerVO getLoginInfo(Long attributionId) {
        String loginId = null;
        try {
            loginId = (String) StpKit.CONSUMER.getLoginId();
        } catch (Exception ignored) {
        }
        if (StrUtil.isBlank(loginId)) {
            throw new BizErrorException(BizExceptionMessageEnum.NOT_LOGIN_ERROR);
        }

        UserConsumerDO one = userConsumerService.lambdaQuery()
                .eq(BaseDO::getId, loginId)
                .one();
        if (ObjectUtil.isNull(one)) {
            throw new BizErrorException(BizExceptionMessageEnum.CONSUMER_DELETED);
        }

        ConsumerVO consumerVO = new ConsumerVO();
        if (ObjectUtil.isNotNull(attributionId)) {
            DeviceAttributionDO deviceAttributionDO = deviceAttributionService.lambdaQuery()
                    .eq(DeviceAttributionDO::getUid, one.getUid())
                    .eq(BaseDO::getId, attributionId)
                    .one();
            if (ObjectUtil.isNull(deviceAttributionDO)) {
                throw new BizErrorException(BizExceptionMessageEnum.ATTRIBUTION_IS_ERROR);
            }
            AttributionExtConfigVO attributionExtConfigVO = new AttributionExtConfigVO();
            attributionExtConfigVO.setAttributionId(deviceAttributionDO.getId());
            attributionExtConfigVO.setName(deviceAttributionDO.getName());
            attributionExtConfigVO.setCode(deviceAttributionDO.getCode());
            consumerVO.setList(Collections.singletonList(attributionExtConfigVO));
        }
        consumerVO.setConsumeId(Long.valueOf(loginId));
        consumerVO.setUid(one.getUid());

        return consumerVO;
    }
}
