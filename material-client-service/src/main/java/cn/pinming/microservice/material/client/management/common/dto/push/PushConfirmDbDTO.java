package cn.pinming.microservice.material.client.management.common.dto.push;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class PushConfirmDbDTO {
    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "确认单id")
    private String confirmId;

    @ApiModelProperty(value = "业务系统订单id")
    private String orderId;

    @ApiModelProperty(value = "基石订单运单单号")
    private String deliveryNo;

    @ApiModelProperty(value = "发货单车牌号")
    private String driverTruckNo;

    @ApiModelProperty(value = "司机名称")
    private String driverName;

    @ApiModelProperty(value = "司机电话")
    private String driverPhoneNo;

    @ApiModelProperty(value = "发货时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String deliveryTime;

    @ApiModelProperty("车牌号")
    private String truckNo;

    @ApiModelProperty(value = "扣杂(含水率)")
    private BigDecimal deductRatio;

    @ApiModelProperty("该货物的称重换算系数，如2.334意思为1立方米 = 2.334吨，不能为空，数字类型")
    private BigDecimal scaleFactor;

    @ApiModelProperty(value = "称重类型 1-收料过磅；2-发料过磅")
    private Integer weighingType;

    @ApiModelProperty(value = "单据照片")
    private String documentPic;

    @ApiModelProperty(value = "签名照片")
    private String signPic;

    @ApiModelProperty(value = "签名人照片")
    private String signerPic;

    @ApiModelProperty(value = "折算数量")
    private BigDecimal convertValue;

    @ApiModelProperty(value = "折算单位")
    private String unitOfMeasurement;

    @ApiModelProperty(value = "偏差数量")
    private BigDecimal deviation;

    @ApiModelProperty(value = "扣重")
    private BigDecimal deductWeight;

    @ApiModelProperty(value = "净重")
    private BigDecimal netWeight;

    @ApiModelProperty(value = "实重")
    private BigDecimal actualWeight;

    @ApiModelProperty(value = "称重单位")
    private String unitInuse;

    @ApiModelProperty(value = "称重数据id1")
    private String recordId1;

    @ApiModelProperty(value = "称重数据id2")
    private String recordId2;

    @ApiModelProperty("毛重")
    private BigDecimal weightGross;

    @ApiModelProperty("皮重")
    private BigDecimal weightTare;

    @ApiModelProperty("进场时间")
    private String enterTime;

    @ApiModelProperty("出场时间")
    private String leaveTime;

    @ApiModelProperty("订单明细")
    private String deliveryDetailIds;

    @ApiModelProperty("主code(用于推送展示)")
    private String primaryCode;

    @ApiModelProperty("回收批次扩展编码")
    private String extCode;

    @ApiModelProperty("订单来源")
    private String source;
}
