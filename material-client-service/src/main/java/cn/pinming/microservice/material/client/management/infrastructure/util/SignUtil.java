package cn.pinming.microservice.material.client.management.infrastructure.util;


import javax.xml.bind.DatatypeConverter;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2024/9/13 08:54
 */
public class SignUtil {

    /**
     * 生成签名
     *
     * @param appKey       appKey
     * @param appSecretKey appSecretKey
     * @param timestamp    时间戳
     * @return 签名
     */
    public static String sign(String appKey, String appSecretKey, Long timestamp) {
        MessageDigest md;
        String signStr = appSecretKey + timestamp;
        try {
            md = MessageDigest.getInstance("SHA-1");
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
        md.update(signStr.getBytes());
        byte[] hashBytes = md.digest();
        String sign = DatatypeConverter.printHexBinary(hashBytes);
        return "JS:" + appKey + ":" + sign;
    }
}
