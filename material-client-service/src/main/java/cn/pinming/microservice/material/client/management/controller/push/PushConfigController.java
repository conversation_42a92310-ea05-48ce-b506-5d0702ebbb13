package cn.pinming.microservice.material.client.management.controller.push;


import cn.pinming.microservice.material.client.management.infrastructure.response.SingleResponse;
import cn.pinming.microservice.material.client.management.common.form.GatewayForm;
import cn.pinming.microservice.material.client.management.common.form.PushConfigForm;
import cn.pinming.microservice.material.client.management.common.vo.push.PushRouteConfigVO;
import cn.pinming.microservice.material.client.management.service.biz.IPushRouteConfigService;
import cn.pinming.microservice.material.client.management.service.biz.IPushUserConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 公共推送配置 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-09
 */
@Api(value = "sdk-push-config", tags = {"sdk-push-config"})
@RestController
@RequestMapping("/api/push-config")
public class PushConfigController {

    @Resource
    private IPushRouteConfigService pushRouteConfigService;
    @Resource
    private IPushUserConfigService pushUserConfigService;

    @ApiOperation(value = "保存网关", nickname = "saveGateway")
    @PostMapping("/gateway")
    public SingleResponse<?> saveGateway(@Validated @RequestBody GatewayForm form) {
        pushUserConfigService.saveGateway(form);
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "回调接口列表", nickname = "routeList", response = PushRouteConfigVO.class)
    @GetMapping("/route/list")
    public SingleResponse<?> routeList() {
        List<PushRouteConfigVO> result = pushRouteConfigService.sdkPushConfig();
        return SingleResponse.of(result);
    }

    @ApiOperation(value = "启用禁用推送", nickname = "enablePush")
    @PostMapping("/route/{routeId}/enable")
    public SingleResponse<?> enablePush(@PathVariable Long routeId) {
        pushRouteConfigService.enablePush(routeId);
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "推送配置保存(归属方)", nickname = "pushConfigSave")
    @PostMapping("/attribution")
    public SingleResponse<?> save(@Validated @RequestBody PushConfigForm form) {
        pushUserConfigService.savePushConfig(form);
        return SingleResponse.buildSuccess();
    }

}

