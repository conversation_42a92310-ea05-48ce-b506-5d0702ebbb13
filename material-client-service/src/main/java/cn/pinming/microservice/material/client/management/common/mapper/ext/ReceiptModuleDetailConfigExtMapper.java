package cn.pinming.microservice.material.client.management.common.mapper.ext;

import cn.pinming.microservice.material.client.management.common.vo.ReceiptModuleDetailConfigVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ReceiptModuleDetailConfigExtMapper {
    List<ReceiptModuleDetailConfigVO> recycleModuleDetailFunctionShow(@Param("moduleId") Long moduleId, @Param("type") Byte type);

    List<String> selectRequireList(@Param("templateId") Long templateId);
}
