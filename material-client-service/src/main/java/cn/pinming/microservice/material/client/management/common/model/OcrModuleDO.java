package cn.pinming.microservice.material.client.management.common.model;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * OCR模版表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("s_ocr_module")
public class OcrModuleDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 用户id
     */
    private String uid;

    /**
     * 外部系统id
     */
    private String extId;

    /**
     * 归属方主键ids
     */
    private String attributionIds;

    /**
     * 业务数据模板名称
     */
    private String name;

    /**
     * 适用第三方平台
     */
    private String client;

    /**
     * 引用模板数
     */
    private Long count;

    /**
     * 父模版id
     */
    private Long pid;

    /**
     * 是否启用 1 是 2 否
     */
    private Byte isEnable;

    /**
     * 类型 1 基石方创建 2 第三方创建
     */
    private Byte type;

    /**
     * 模版底图
     */
    private String fileId;

    /**
     * 单据匹配方式 :  0 - 区域坐标匹配（旧） 1 - 索引匹配（新）
     */
    private Byte billMatchType;

    /**
     * 模版html
     */
    private String html;
}
