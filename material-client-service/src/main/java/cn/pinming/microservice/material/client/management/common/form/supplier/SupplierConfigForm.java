package cn.pinming.microservice.material.client.management.common.form.supplier;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2024/8/19 17:42
 */
@Data
public class SupplierConfigForm {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("供应商名称")
    @NotBlank(message = "供应商名称为空")
    private String name;

    @ApiModelProperty("来自业务系统的供应商唯一ID")
    @NotBlank(message = "来自业务系统的供应商唯一ID为空")
    private String supplierExtId;

    @ApiModelProperty("供应商在基石平台的ID(UID)")
    @NotBlank(message = "供应商在基石平台的唯一ID为空")
    private String supplierSysId;
}
