package cn.pinming.microservice.material.client.management.controller.purchase;


import cn.pinming.microservice.material.client.management.infrastructure.response.SingleResponse;
import cn.pinming.microservice.material.client.management.common.query.PurchasePageQuery;
import cn.pinming.microservice.material.client.management.common.vo.purchase.PurchaseDetailVO;
import cn.pinming.microservice.material.client.management.common.vo.purchase.PurchaseVO;
import cn.pinming.microservice.material.client.management.service.biz.IPurchaseOrderService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 采购单(订单)主表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-21
 */
@Api(value = "订单管理", tags = {"purchase-order"})
@RestController
@RequestMapping("/api/purchase")
public class PurchaseOrderController {

    @Resource
    private IPurchaseOrderService purchaseOrderService;

    @ApiOperation(value = "列表", responseReference = "SingleResponse«Page<PurchaseVO>»", nickname = "purchasePage")
    @PostMapping("/page")
    public SingleResponse<Page<PurchaseVO>> page(@RequestBody PurchasePageQuery query) {
        Page<PurchaseVO> page = purchaseOrderService.pageByQuery(query);
        return SingleResponse.of(page);
    }

    @ApiOperation(value = "订单作废(采购单)", nickname = "purchaseCancel")
    @PostMapping("/{orderId}/cancel")
    public SingleResponse<?> purchaseCancel(@PathVariable Long orderId) {
        purchaseOrderService.cancel(orderId);
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "详情", responseReference = "SingleResponse«PurchaseDetailVO»", nickname = "purchaseDetail",response = PurchaseDetailVO.class)
    @GetMapping("/{id}")
    public SingleResponse<PurchaseDetailVO> detail(@PathVariable Long id) {
        PurchaseDetailVO result = purchaseOrderService.detailById(id);
        return SingleResponse.of(result);
    }



}

