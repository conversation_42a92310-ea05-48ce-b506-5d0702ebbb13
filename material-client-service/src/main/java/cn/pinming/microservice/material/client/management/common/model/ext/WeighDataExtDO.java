package cn.pinming.microservice.material.client.management.common.model.ext;

import cn.pinming.microservice.material.client.management.common.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 称重记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("d_weigh_data")
public class WeighDataExtDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 终端记录id
     */
    private String recordId;

    /**
     * 归属方id
     */
    private Integer attributionId;

    /**
     * 车牌号、货名
     */
    private String name;

    /**
     * 重量(吨)
     */
    private BigDecimal weight;

    /**
     * appKey
     */
    private String appKey;

    /**
     * 1 载车称重 2 净货称重
     */
    private Byte type;

    /**
     * 上传时间
     */
    private LocalDateTime uploadTime;

    /**
     * 单位
     */
    private String unit;


}
