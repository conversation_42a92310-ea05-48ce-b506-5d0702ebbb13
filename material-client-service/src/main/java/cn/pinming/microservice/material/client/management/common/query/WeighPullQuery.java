package cn.pinming.microservice.material.client.management.common.query;

import cn.pinming.microservice.base.common.base.BaseQuery;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class WeighPullQuery extends BasePageQuery {
    /**
     * 称重开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 称重结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 数据归属方code(精确搜索)
     */
    private String attributionCode;

    /**
     * 设备机器码(精确搜索)
     */
    private String deviceSn;

    /**
     * 车牌号(支持模糊搜索,与“车牌号列表”二选一)
     */
    private String truckNo;

    /**
     * 车牌号列表(精确搜索;与“车牌号”二选一)
     */
    private List<String> truckNos;

    /**
     * 推送状态  1 未推送 3 已推送
     */
    private Integer pushStatus;

    /**
     * 是否需要称重曲线
     */
    private Boolean curveFlag;
}
