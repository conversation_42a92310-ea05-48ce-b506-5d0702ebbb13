package cn.pinming.microservice.material.client.management.infrastructure.util;

import cn.hutool.core.util.ObjectUtil;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @date 2023/6/27
 */
public class DateUtil {

    private static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    public static String parseLocalDateTime(LocalDateTime time) {
        if (ObjectUtil.isNull(time)) {
            return null;
        }
        return time.format(DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SS));
    }

    public static LocalDateTime parseLocalDateTime(String time) {
        if (StringUtils.isBlank(time)) {
            return null;
        }
        return LocalDateTime.parse(time.replace("T", " "), DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SS));
    }

}
