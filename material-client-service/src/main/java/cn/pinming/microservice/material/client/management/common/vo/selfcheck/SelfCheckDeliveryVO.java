package cn.pinming.microservice.material.client.management.common.vo.selfcheck;


import cn.hutool.json.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class SelfCheckDeliveryVO {

    @ApiModelProperty("单据预占位类型：1 - 运单模式的发货单(运单);2 - OCR单据回收预生成单据")
    private Integer jsPreType;

    @ApiModelProperty("此发货单在基石平台的唯一编号，不能为空")
    @JsonProperty("jsDeliveryNo")
    private String no;

    @ApiModelProperty("送货车牌号，不能为空")
    private String truckNo;

    @ApiModelProperty("司机姓名，不能为空")
    @JsonProperty("driverName")
    private String driver;

    @ApiModelProperty("司机手机号，不能为空")
    @JsonProperty("driverPhoneNo")
    private String driverMobile;

    @ApiModelProperty("发货时间，格式按yyyy-MM-dd hh:mm:ss，不能为空")
    private String deliveryTime;

    @ApiModelProperty("最终确认打印允许打印的张数，默认为1，不可为空")
    @JsonProperty("allowedPrintCounts")
    private Integer printLimit = 1;

    @ApiModelProperty("配置好的打印模板，不可为空")
    private JSONObject printTemplate;

    @ApiModelProperty("订单基础信息，不可为空")
    private PurchaseBaseInfoVO purchaseBaseInfo;

    @ApiModelProperty("发货单物料明细，不能为空")
    private List<CargoVO> cargoList;
}

