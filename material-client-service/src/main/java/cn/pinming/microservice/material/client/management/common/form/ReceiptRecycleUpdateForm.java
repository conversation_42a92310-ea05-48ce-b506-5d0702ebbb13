package cn.pinming.microservice.material.client.management.common.form;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class ReceiptRecycleUpdateForm implements Serializable {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("过磅类型(1-收料、2-发料)")
    private Integer weighType;

    @ApiModelProperty("车牌号")
    private String truckNo;

    @ApiModelProperty("毛重")
    private BigDecimal weightGross;

    @ApiModelProperty("皮重")
    private BigDecimal weightTare;

    @ApiModelProperty("净重")
    private BigDecimal weightNet;

    @ApiModelProperty("毛重时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime weightGrossTime;

    @ApiModelProperty("皮重时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime weightTareTime;

    @ApiModelProperty("称重单位（吨）")
    private String unit;

    @ApiModelProperty("称重集合")
    private List<String> recordIdList;


    @ApiModelProperty("模块id")
    private Long moduleId;

    @ApiModelProperty("模板集合")
    private List<ReceiptRecycleModuleForm> moduleFormList;

}
