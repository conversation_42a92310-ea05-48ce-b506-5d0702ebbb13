package cn.pinming.microservice.material.client.management.infrastructure.util.pwd;

import cn.hutool.crypto.digest.DigestUtil;

/**
 * <AUTHOR>
 * @date 2023/6/12
 */
public class PwdMd5Util {

    public static String md5Password(String password) {
        String passwordMd5Digest = DigestUtil.md5Hex(password);
        return passwordMd5Digest;
    }

    public static String md5PasswordAndSalt(String password, String salt) {
        String passwordMd5Digest = DigestUtil.md5Hex(password + salt);
        return passwordMd5Digest;
    }

}
