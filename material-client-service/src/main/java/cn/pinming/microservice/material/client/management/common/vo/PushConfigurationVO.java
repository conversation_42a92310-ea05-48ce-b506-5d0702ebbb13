package cn.pinming.microservice.material.client.management.common.vo;

import cn.pinming.microservice.material.client.management.common.model.PushConfigurationDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <AUTHOR>
 */
@Data
public class PushConfigurationVO extends PushConfigurationDO {
    private static final long serialVersionUID = -7628837495532145444L;

    @ApiModelProperty(value = "适配租户数")
    private Integer userAmount;

    @ApiModelProperty(value = "适配归属方数")
    private Integer attributionAmount;
}
