package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.microservice.material.client.management.common.BaseDO;
import cn.pinming.microservice.material.client.management.common.mapper.UserConsumerMapper;
import cn.pinming.microservice.material.client.management.common.mapper.ext.UserConsumerExtMapper;
import cn.pinming.microservice.material.client.management.common.model.DeviceAttributionDO;
import cn.pinming.microservice.material.client.management.common.model.UserConsumerDO;
import cn.pinming.microservice.material.client.management.common.vo.UserConsumerVO;
import cn.pinming.microservice.material.client.management.service.biz.DeviceAttributionService;
import cn.pinming.microservice.material.client.management.service.biz.UserConsumerService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class UserConsumerServiceImpl extends ServiceImpl<UserConsumerMapper, UserConsumerDO> implements UserConsumerService {
    @Resource
    private UserConsumerExtMapper userConsumerExtMapper;
    @Resource
    private DeviceAttributionService deviceAttributionService;

    @Override
    public List<UserConsumerVO> consumerList(String uId) {
        List<UserConsumerVO> list = userConsumerExtMapper.consumerList(uId);

        if (CollUtil.isNotEmpty(list)) {
            List<UserConsumerVO> collected = list.stream().filter(e -> StrUtil.isNotBlank(e.getAttributions())).collect(Collectors.toList());

            if (CollUtil.isNotEmpty(collected)) {
                Set<String> collect = collected.stream().flatMap(e -> Arrays.stream(e.getAttributions().split(","))).collect(Collectors.toSet());
                List<DeviceAttributionDO> attributionDOList = deviceAttributionService.lambdaQuery()
                        .in(BaseDO::getId, collect)
                        .list();
                if (CollUtil.isNotEmpty(attributionDOList)) {
                    Map<Long, String> attributionMap = attributionDOList.stream().collect(Collectors.toMap(BaseDO::getId, DeviceAttributionDO::getName));
                    collected.forEach(e -> {
                            List<String> attributionNames = new ArrayList<>();
                            StrUtil.split(e.getAttributions(),",").forEach(item -> {
                                String attributionName = attributionMap.get(Long.valueOf(item));
                                attributionNames.add(attributionName);
                            });
                            if (CollUtil.isNotEmpty(attributionNames)) {
                                e.setAttributionNames(String.join(",", attributionNames));
                            }
                    });
                }
            }
        }

        return list;
    }
}
