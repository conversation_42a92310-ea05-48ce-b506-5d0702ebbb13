package cn.pinming.microservice.material.client.management.service.impl;

import okhttp3.*;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2025/4/25 10:13
 */
public class BaiduOCRTest {

    public static final String CLIENT_ID = "";
    public static final String CLIENT_SECRET = "";

    public static final OkHttpClient HTTP_CLIENT = new OkHttpClient().newBuilder().readTimeout(300, TimeUnit.SECONDS).build();


    public static void main(String[] args) throws IOException {
        //24.352ad058587ab0a79f329ace462aadec.2592000.1748152363.282335-118525490
        String original = "https://ossm.pinming.cn/b2a400e1-7160-2c94-8d4e-327c1e7a512a.jpg?Expires=1748153052&OSSAccessKeyId=LTAI5t6AkVS76uhp9SCTxdHz&Signature=DyQcbT%2BBfuC2k1%2BwiPzAt%2FuaKK4%3D&response-content-disposition=attachment%3Bfilename%3D%22log1742276448643.jpg%22";
        accurate(original);
    }

    public static void accurate(String original) throws IOException {
        MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
        // url转义处理
        //https://ossm.pinming.cn/b2a400e1-7160-2c94-8d4e-327c1e7a512a.jpg?Expires=1748153052&OSSAccessKeyId=LTAI5t6AkVS76uhp9SCTxdHz&Signature=DyQcbT%2BBfuC2k1%2BwiPzAt%2FuaKK4%3D&response-content-disposition=attachment%3Bfilename%3D%22log1742276448643.jpg%22
        String encode = URLEncoder.encode(original, StandardCharsets.UTF_8.name());

        RequestBody body = RequestBody.create(mediaType, "url=" + encode + "&detect_language=true&language_type=CHN_ENG&eng_granularity=word&recognize_granularity=big&detect_direction=true&vertexes_location=false&paragraph=true&probability=false&char_probability=false&multidirectional_recognize=true");
        Request request = new Request.Builder()
                .url("https://aip.baidubce.com/rest/2.0/ocr/v1/accurate?access_token=24.352ad058587ab0a79f329ace462aadec.2592000.1748152363.282335-11852549")// + getAuth(CLIENT_ID, CLIENT_SECRET))
                .method("POST", body)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .addHeader("Accept", "application/json")
                .build();
        Response response = HTTP_CLIENT.newCall(request).execute();
        System.out.println(response.body().string());
    }




}
