package cn.pinming.microservice.material.client.management.service.impl;


import cn.hutool.core.util.StrUtil;
import cn.pinming.microservice.material.client.management.common.model.DeviceAttributionDO;
import cn.pinming.microservice.material.client.management.service.biz.DeviceAttributionService;
import cn.pinming.microservice.material.client.management.service.biz.IShortLinkService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@SpringBootTest
public class ShortLinkTest {

    @Resource
    private IShortLinkService shortLinkService;

    @Test
    public void genShortLink() {
        String longLink = "https://blog.csdn.net/feiying101/article/details/139953150";
        String shortLink = shortLinkService.createShortLink(longLink);
        Assertions.assertNotNull(shortLink);
    }

    @Test
    public void getLongLink() {
        String shortLink = "5T2TUsmvbrB";
        String longLink = shortLinkService.getLongLink(shortLink);
        System.out.println(longLink);
    }

    @Resource
    private DeviceAttributionService deviceAttributionService;

    @Test
    public void sql(){
        String uid = "85d7c908920f407b89610a234ae420c8";
        String attributionCode = "59,47936";
        DeviceAttributionDO one = deviceAttributionService.lambdaQuery()
                .eq(DeviceAttributionDO::getUid, uid)
                .eq(StrUtil.isNotBlank(attributionCode) && attributionCode.contains(StrUtil.COMMA), DeviceAttributionDO::getCode, attributionCode)
                .apply(StrUtil.isNotBlank(attributionCode) && !attributionCode.contains(StrUtil.COMMA), "find_in_set({0},code)", attributionCode)
                .one();
    }



}
