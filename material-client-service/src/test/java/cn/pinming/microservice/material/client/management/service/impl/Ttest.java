package cn.pinming.microservice.material.client.management.service.impl;

import net.coobird.thumbnailator.Thumbnails;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.URL;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2025/4/10 10:29
 */
public class Ttest {

    public static void main(String[] args) {
        try {
            File file = new File("test.jpg");
            FileOutputStream outputStream = new FileOutputStream(file);
            URL url = new URL("https://ossm.pinming.cn/eb0a15fe58704d89b114cbc64c754714/2025-04-10/b2bb00ff-7e4a-2c94-a06d-3a0b5ce272d5.jpg?Expires=1746862322&OSSAccessKeyId=LTAI5t6AkVS76uhp9SCTxdHz&Signature=wNyRJNv9vvbsr2ff%2FIAXQTswb%2BQ%3D&response-content-disposition=attachment%3Bfilename%3D%22153004.jpg%22");
            Thumbnails.of(url)
                    .scale(1.0) // 保持原尺寸
                    .outputQuality(0.5f) // 压缩质量 50%
                    .outputFormat("jpg") // 输出格式
                    .toOutputStream(outputStream);
            outputStream.close();
            System.out.println("图片压缩完成！");
        } catch (IOException e) {
            e.printStackTrace();
        }

    }
}
