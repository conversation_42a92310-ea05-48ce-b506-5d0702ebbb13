package cn.pinming.microservice.material.client.management.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.pinming.microservice.material.client.management.common.enums.RecycleStatusEnum;
import cn.pinming.microservice.material.client.management.common.model.ReceiptRecycleDO;
import cn.pinming.microservice.material.client.management.service.biz.ReceiptRecycleService;
import cn.pinming.microservice.material.client.management.service.business.RecycleService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;

@SpringBootTest
public class OCRRecycleTest {

    @Resource
    private ReceiptRecycleService receiptRecycleService;
    @Resource
    private RecycleService recycleService;

    @Test
    public void recycleProcess() {
        List<ReceiptRecycleDO> list = receiptRecycleService.lambdaQuery().eq(ReceiptRecycleDO::getRecycleStatus, RecycleStatusEnum.WAIT.getValue())
                .isNotNull(ReceiptRecycleDO::getRecyclePic).isNull(ReceiptRecycleDO::getLocalId)
                .orderByDesc(ReceiptRecycleDO::getGmtCreate).last("limit 100").list();
        if (CollUtil.isNotEmpty(list)) {
            for (ReceiptRecycleDO receiptRecycleDO : list) {
                recycleService.syncProcessRecycle(receiptRecycleDO);
            }
        }
    }

}
