package cn.pinming.microservice.material.client.management.service.impl;

import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

//@SpringBootTest
class FileOssServiceImplTest {
    private static int NUM = 0;

    @Test
    public void threadTest() {
        for (int i = 0; i < 5; i++) {
            test();
        }
        int i = 1;
        BigDecimal m = BigDecimal.ONE;
        BigDecimal add = m.add(BigDecimal.TEN);
    }

    public void test() {
        ExecutorService executorService = Executors.newFixedThreadPool(5);

        Runnable runnable = new Runnable() {
            @Override
            public void run() {
                for (int i = 0; i < 1000; i++) {
                    System.out.print(Thread.currentThread().getName() + "->");
                    NUM = ++NUM;
                    System.out.println(NUM);
                }
            }
        };

        long start = System.currentTimeMillis();
        for (int i = 0; i < 5; i++) {
            add(executorService,runnable);
        }
        long end = System.currentTimeMillis();
        long result = end - start;
        System.out.println("===========================所用时间："+ result + "ms");
        executorService.shutdown();

    }

    private synchronized void add(ExecutorService executorService,Runnable runnable) {
        executorService.execute(runnable);
    }

}